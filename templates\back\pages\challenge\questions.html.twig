{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Challenge Summary Card */
        .challenge-summary-card {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            border-left: 4px solid #0d6efd;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .challenge-summary-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .challenge-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .challenge-title i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        /* Info Item Styles */
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .info-item i {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            margin-right: 0.75rem;
            font-size: 14px;
        }

        .info-label {
            font-weight: 600;
            margin-right: 0.5rem;
            color: #495057;
        }

        .info-value {
            color: #6c757d;
        }

        /* Accordion Styles */
        .accordion-item {
            border: 1px solid rgba(0,0,0,.05);
            border-radius: 0.75rem !important;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .accordion-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .accordion-button {
            background-color: #fff;
            color: #212529;
            font-weight: 600;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem !important;
        }

        .accordion-button:not(.collapsed) {
            background-color: #f8f9fa;
            color: #0d6efd;
            box-shadow: none;
        }

        .accordion-button:focus {
            box-shadow: none;
            border-color: rgba(0,0,0,.125);
        }

        .accordion-body {
            padding: 1.5rem;
            background-color: #fff;
        }

        /* Question Styles */
        .question-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0,0,0,.05);
        }

        .option-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
            transition: all 0.2s ease;
        }

        .option-item:hover {
            background-color: #f8f9fa;
        }

        .option-item.correct {
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
            font-weight: 600;
        }

        .option-item i {
            margin-right: 0.75rem;
            font-size: 1rem;
        }

        .option-label {
            font-weight: 600;
            margin-right: 0.5rem;
            color: #495057;
        }

        /* Empty State Styles */
        .empty-state {
            padding: 3rem;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 0.75rem;
        }

        .empty-state-icon {
            font-size: 4rem;
            color: #adb5bd;
            margin-bottom: 1.5rem;
        }

        .empty-state-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.75rem;
        }

        .empty-state-text {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_challenge_show', {'id': challenge.id}) }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Challenge">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Challenge Questions</h1>
                    <p class="text-muted mb-0">Manage questions for {{ challenge.name }}</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_quizz_new') }}" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Add a new question to this challenge">
                        <i class="ri-add-line me-1"></i> Add New Question
                    </a>
                </div>
            </div>
        </div>

        <!-- Challenge Summary Card -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0 fw-bold d-flex align-items-center">
                    <i class="ri-trophy-line text-primary me-2"></i> Challenge Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="challenge-title">
                            {{ challenge.name }}
                        </div>
                        <div class="info-item">
                            <i class="ri-file-text-line bg-primary-subtle text-primary"></i>
                            <span class="info-label">Description:</span>
                            <span class="info-value">{{ challenge.description }}</span>
                        </div>
                        <div class="info-item">
                            <i class="ri-time-line bg-warning-subtle text-warning"></i>
                            <span class="info-label">Duration:</span>
                            <span class="info-value">{{ challenge.duration }} minutes</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <i class="ri-calendar-event-line bg-info-subtle text-info"></i>
                            <span class="info-label">Start Date:</span>
                            <span class="info-value">{{ challenge.start ? challenge.start|date('M d, Y H:i') : 'Not set' }}</span>
                        </div>
                        <div class="info-item">
                            <i class="ri-calendar-check-line bg-success-subtle text-success"></i>
                            <span class="info-label">End Date:</span>
                            <span class="info-value">{{ challenge.end ? challenge.end|date('M d, Y H:i') : 'Not set' }}</span>
                        </div>
                        <div class="info-item">
                            <i class="ri-question-line bg-danger-subtle text-danger"></i>
                            <span class="info-label">Total Questions:</span>
                            <span class="badge bg-primary rounded-pill px-3 py-2">{{ quizzes|length }} Questions</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions List -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-question-line text-primary me-2"></i> Questions List
                        </h5>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-primary-subtle text-primary rounded-pill px-3 py-2">
                            <i class="ri-questionnaire-line me-1"></i> {{ quizzes|length }} Questions
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if quizzes|length > 0 %}
                    <div class="accordion" id="questionsAccordion">
                        {% for quiz in quizzes %}
                            <div class="accordion-item animate__animated animate__fadeIn" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                                <h2 class="accordion-header" id="heading{{ quiz.id }}">
                                    <button class="accordion-button {% if not loop.first %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ quiz.id }}" aria-expanded="{{ loop.first ? 'true' : 'false' }}" aria-controls="collapse{{ quiz.id }}">
                                        <i class="ri-questionnaire-line me-2"></i>
                                        Question {{ loop.index }}
                                    </button>
                                </h2>
                                <div id="collapse{{ quiz.id }}" class="accordion-collapse collapse {% if loop.first %}show{% endif %}" aria-labelledby="heading{{ quiz.id }}" data-bs-parent="#questionsAccordion">
                                    <div class="accordion-body">
                                        <div class="question-text">
                                            {{ quiz.question }}
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="option-item {% if quiz.answer == 1 %}correct{% endif %}">
                                                    <i class="ri-checkbox-blank-circle-line"></i>
                                                    <span class="option-label">Option A:</span>
                                                    {{ quiz.choice1 }}
                                                    {% if quiz.answer == 1 %}<i class="ri-check-line ms-auto"></i>{% endif %}
                                                </div>
                                                <div class="option-item {% if quiz.answer == 2 %}correct{% endif %}">
                                                    <i class="ri-checkbox-blank-circle-line"></i>
                                                    <span class="option-label">Option B:</span>
                                                    {{ quiz.choice2 }}
                                                    {% if quiz.answer == 2 %}<i class="ri-check-line ms-auto"></i>{% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="option-item {% if quiz.answer == 3 %}correct{% endif %}">
                                                    <i class="ri-checkbox-blank-circle-line"></i>
                                                    <span class="option-label">Option C:</span>
                                                    {{ quiz.choice3 }}
                                                    {% if quiz.answer == 3 %}<i class="ri-check-line ms-auto"></i>{% endif %}
                                                </div>
                                                <div class="option-item {% if quiz.answer == 4 %}correct{% endif %}">
                                                    <i class="ri-checkbox-blank-circle-line"></i>
                                                    <span class="option-label">Option D:</span>
                                                    {{ quiz.choice4 }}
                                                    {% if quiz.answer == 4 %}<i class="ri-check-line ms-auto"></i>{% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-4 d-flex justify-content-end">
                                            <a href="{{ path('app_quizz_edit', {'id': quiz.id}) }}"
                                               class="btn btn-sm btn-outline-warning me-2"
                                               data-bs-toggle="tooltip"
                                               data-bs-placement="top"
                                               title="Edit this question">
                                                <i class="ri-edit-line me-1"></i> Edit
                                            </a>
                                            <a onclick="return confirm('Are you sure you want to delete this question? This action cannot be undone.');"
                                               href="{{ path('app_quizz_delete', {'id': quiz.id}) }}"
                                               class="btn btn-sm btn-outline-danger"
                                               data-bs-toggle="tooltip"
                                               data-bs-placement="top"
                                               title="Delete this question">
                                                <i class="ri-delete-bin-line me-1"></i> Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state animate__animated animate__fadeIn">
                        <i class="ri-questionnaire-line empty-state-icon"></i>
                        <h5 class="empty-state-title">No Questions Added Yet</h5>
                        <p class="empty-state-text">Start adding questions to this challenge to make it available for participants</p>
                        <a href="{{ path('app_quizz_new') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i> Add First Question
                        </a>
                    </div>
                {% endif %}
            </div>
            {% if quizzes|length > 0 %}
                <div class="card-footer bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            <i class="ri-information-line me-1"></i> Total: {{ quizzes|length }} question(s)
                        </div>
                        <a href="{{ path('app_quizz_new') }}" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i> Add Another Question
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
