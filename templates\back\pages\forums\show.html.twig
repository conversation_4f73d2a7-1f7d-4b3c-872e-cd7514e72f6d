{% extends 'back/pages/home/<USER>' %}

{% block dash %} {% endblock %}
{% block forum %}active {% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Forum Content Styles */
        .forum-content-card {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            border-left: 4px solid #0d6efd;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .forum-content-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .forum-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .forum-title i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        .forum-content {
            color: #495057;
            line-height: 1.6;
            white-space: pre-line;
        }

        /* Info Card Styles */
        .info-card {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,.05);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .info-card-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .info-card-title i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Comment Styles */
        .comment-item {
            transition: all 0.3s ease;
        }

        .comment-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .comment-content {
            color: #495057;
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 36px;
            height: 36px;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_forums_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Forums">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Forum Details</h1>
                    <p class="text-muted mb-0">View and manage forum discussion</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-discuss-line text-primary me-2"></i> {{ forum.title }}
                        </h5>
                        <div>
                            {% if forum.user %}
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2 bg-primary-subtle rounded-circle">
                                        <span class="avatar-text text-primary">{{ forum.user.getFullName()|slice(0,1)|upper }}</span>
                                    </div>
                                    <span class="text-muted">{{ forum.user.getFullName() }}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Forum Content -->
                        <div class="forum-content-card animate__animated animate__fadeIn">
                            <div class="forum-content">{{ forum.content|nl2br }}</div>
                        </div>

                        <!-- Forum Info Cards -->
                        <div class="row mb-4">
                            <div class="col-md-4 mb-3 mb-md-0">
                                <div class="info-card animate__animated animate__fadeIn">
                                    <div class="info-card-title">
                                        <i class="ri-calendar-line text-primary"></i> Created Date
                                    </div>
                                    <div class="info-card-value">
                                        {{ forum.createdat ? forum.createdat|date('F d, Y') : 'Not specified' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3 mb-md-0">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                                    <div class="info-card-title">
                                        <i class="ri-time-line text-info"></i> Created Time
                                    </div>
                                    <div class="info-card-value">
                                        {{ forum.createdat ? forum.createdat|date('h:i A') : 'Not specified' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                                    <div class="info-card-title">
                                        <i class="ri-chat-1-line text-success"></i> Comments
                                    </div>
                                    <div class="info-card-value">
                                        {% if forum.comments is defined %}
                                            {{ forum.comments|length }} comment(s)
                                        {% else %}
                                            0 comments
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comments Section -->
                        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                            <div class="card-header bg-white py-3">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                                            <i class="ri-chat-1-line text-primary me-2"></i> Comments
                                        </h5>
                                    </div>
                                    <div class="col-auto">
                                        <div class="input-group">
                                            <input type="text" id="commentSearch" class="form-control" placeholder="Search comments...">
                                            <span class="input-group-text bg-primary text-white">
                                                <i class="ri-search-line"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle border-0" id="commentsTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Content</th>
                                                <th>Upvotes</th>
                                                <th class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% if forum.comments is defined and forum.comments|length > 0 %}
                                            {% for comment in forum.comments %}
                                                <tr class="align-middle comment-item">
                                                    <td>
                                                        <div class="comment-content">{{ comment.content|length > 150 ? comment.content|slice(0, 150) ~ '...' : comment.content }}</div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success-subtle text-success rounded-pill">
                                                            <i class="ri-thumb-up-line me-1"></i> {{ comment.upvotes }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex justify-content-end gap-2">
                                                            <a href="{{ path('app_comments_show', {'id': comment.id}) }}"
                                                               class="btn btn-sm btn-outline-primary"
                                                               data-bs-toggle="tooltip"
                                                               data-bs-placement="top"
                                                               title="View Comment">
                                                                <i class="ri-eye-line"></i>
                                                            </a>
                                                            <form method="post" action="{{ path('app_comments_delete', {'id': comment.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                                                <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ comment.id) }}">
                                                                <button class="btn btn-sm btn-outline-danger"
                                                                        data-bs-toggle="tooltip"
                                                                        data-bs-placement="top"
                                                                        title="Delete Comment">
                                                                    <i class="ri-delete-bin-line"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center py-5">
                                                    <div class="empty-state">
                                                        <i class="ri-chat-1-line empty-state-icon"></i>
                                                        <h5>No comments found</h5>
                                                        <p class="text-muted">There are no comments for this forum yet</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted d-flex align-items-center">
                                <i class="ri-information-line me-1"></i> Last updated: {{ forum.createdat ? forum.createdat|date('F d, Y h:i A') : 'Not available' }}
                            </div>
                            <div>
                                <form method="post" action="{{ path('app_forums_delete', {'id': forum.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this forum? This action cannot be undone.');">
                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ forum.id) }}">
                                    <button class="btn btn-outline-danger"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="Delete Forum">
                                        <i class="ri-delete-bin-line me-1"></i> Delete Forum
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize DataTable with enhanced styling
            $('#commentsTable').DataTable({
                "order": [[ 1, "desc" ]],  // Sort by upvotes by default
                "pageLength": 5,
                "language": {
                    "lengthMenu": "Show _MENU_ comments per page",
                    "zeroRecords": "No comments found",
                    "info": "Showing page _PAGE_ of _PAGES_",
                    "infoEmpty": "No comments available",
                    "infoFiltered": "(filtered from _MAX_ total comments)"
                },
                "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                       '<"row"<"col-sm-12"tr>>' +
                       '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                "responsive": true,
                "autoWidth": false,
                "drawCallback": function() {
                    // Reinitialize tooltips after table redraw
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }
            });

            // Custom search functionality
            const searchInput = document.getElementById('commentSearch');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    $('#commentsTable').DataTable().search(this.value).draw();
                });
            }

            // Add hover effect to comment items
            const commentItems = document.querySelectorAll('.comment-item');
            commentItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });
            });

            // Add animation to info cards
            const infoCards = document.querySelectorAll('.info-card');
            infoCards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Add animation to forum content card
            const forumContentCard = document.querySelector('.forum-content-card');
            if (forumContentCard) {
                forumContentCard.classList.add('animate__animated', 'animate__fadeIn');
            }
        });
    </script>
{% endblock %}
