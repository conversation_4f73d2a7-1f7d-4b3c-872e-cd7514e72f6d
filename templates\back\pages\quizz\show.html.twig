{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Question Content Styles */
        .question-content-card {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            border-left: 4px solid #0d6efd;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .question-content-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .question-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .question-title i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        /* Info Card Styles */
        .info-card {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,.05);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .info-card-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .info-card-title i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Choice Item Styles */
        .choice-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
            border: 1px solid rgba(0,0,0,.05);
        }

        .choice-item:hover {
            background-color: #e9ecef;
        }

        .choice-item.correct {
            background-color: rgba(25, 135, 84, 0.1);
            border-color: rgba(25, 135, 84, 0.2);
        }

        .choice-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #0d6efd;
            color: #fff;
            font-size: 0.875rem;
            font-weight: 600;
            margin-right: 0.75rem;
        }

        .choice-item.correct .choice-number {
            background-color: #198754;
        }

        .choice-text {
            font-size: 1rem;
            color: #495057;
            flex-grow: 1;
        }

        .choice-badge {
            margin-left: auto;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Stats Card */
        .stats-card {
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0,0,0,.05);
            transition: all 0.3s ease;
            border: none;
            background-color: #fff;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,.1);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 24px;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
            line-height: 1.2;
        }

        .stats-label {
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Correct Answer Display */
        .correct-answer-display {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 1.5rem;
        }

        .correct-answer-number {
            font-size: 3rem;
            font-weight: 700;
            color: #198754;
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .correct-answer-label {
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_quizz_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Questions">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Quiz Question Details</h1>
                    <p class="text-muted mb-0">View and manage question information</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_quizz_edit', {'id': quizz.id}) }}" class="btn btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Question">
                        <i class="ri-edit-line me-1"></i> Edit
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Question Details -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-question-line text-primary me-2"></i> Question Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Question Content -->
                        <div class="question-content-card animate__animated animate__fadeIn">
                            <div class="question-title">
                                <i class="ri-question-answer-line"></i> Question
                            </div>
                            <p class="mb-0">{{ quizz.question }}</p>
                        </div>

                        <!-- Answer Choices -->
                        <h5 class="mb-3 fw-bold d-flex align-items-center">
                            <i class="ri-list-check text-primary me-2"></i> Answer Choices
                        </h5>
                        <div class="animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                            {% for i in 1..4 %}
                                {% set choice = attribute(quizz, 'choice' ~ i) %}
                                <div class="choice-item {% if quizz.answer == i %}correct{% endif %}">
                                    <div class="choice-number">{{ i }}</div>
                                    <div class="choice-text">{{ choice }}</div>
                                    {% if quizz.answer == i %}
                                        <div class="choice-badge">
                                            <span class="badge bg-success-subtle text-success rounded-pill px-3 py-2">
                                                <i class="ri-check-line me-1"></i> Correct Answer
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Challenge Information -->
                        {% if quizz.challenge %}
                            <div class="info-card mt-4 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                                <div class="info-card-title">
                                    <i class="ri-trophy-line text-primary"></i> Associated Challenge
                                </div>
                                <div class="d-flex align-items-center">
                                    <a href="{{ path('app_challenge_show', {'id': quizz.challenge.id}) }}" class="badge bg-primary-subtle text-primary rounded-pill px-3 py-2 text-decoration-none">
                                        <i class="ri-trophy-line me-1"></i> {{ quizz.challenge.name }}
                                    </a>
                                    <a href="{{ path('app_challenge_show', {'id': quizz.challenge.id}) }}" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="ri-external-link-line me-1"></i> View Challenge
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted d-flex align-items-center">
                                <i class="ri-information-line me-1"></i> Question ID: {{ quizz.id }}
                            </div>
                            <div>
                                <form method="post" action="{{ path('app_quizz_delete', {'id': quizz.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this question? This action cannot be undone.');">
                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ quizz.id) }}">
                                    <button class="btn btn-outline-danger"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="Delete Question">
                                        <i class="ri-delete-bin-line me-1"></i> Delete Question
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Stats -->
            <div class="col-lg-4 mb-4">
                <!-- Correct Answer Card -->
                <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-checkbox-circle-line text-success me-2"></i> Correct Answer
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="correct-answer-display">
                            <div class="correct-answer-number">{{ quizz.answer }}</div>
                            <div class="correct-answer-label">Choice {{ quizz.answer }}</div>
                        </div>
                        <div class="progress mt-2" style="height: 6px;">
                            {% set percentage = (quizz.answer / 4) * 100 %}
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ percentage }}%;" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats Card -->
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-bar-chart-line text-primary me-2"></i> Question Stats
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="info-card">
                                    <div class="info-card-title">
                                        <i class="ri-text-spacing text-info"></i> Question Length
                                    </div>
                                    <div class="info-card-value">
                                        {{ quizz.question|length }} characters
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="info-card">
                                    <div class="info-card-title">
                                        <i class="ri-list-check text-warning"></i> Choices
                                    </div>
                                    <div class="info-card-value">
                                        4 options
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="info-card">
                                    <div class="info-card-title">
                                        <i class="ri-text-spacing text-success"></i> Average Choice Length
                                    </div>
                                    <div class="info-card-value">
                                        {% set totalChoiceLength = quizz.choice1|length + quizz.choice2|length + quizz.choice3|length + quizz.choice4|length %}
                                        {% set avgChoiceLength = (totalChoiceLength / 4)|round %}
                                        {{ avgChoiceLength }} characters
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
