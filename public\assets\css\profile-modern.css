/* Modern Profile Page Styling */
:root {
  --primary-color: #6BB748;
  --primary-dark: #5a9c3c;
  --primary-light: #8cc96f;
  --primary-very-light: #e8f5e9;
  --secondary-color: #17303B;
  --text-dark: #333333;
  --text-medium: #555555;
  --text-light: #777777;
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --border-color: #e0e0e0;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-circle: 50%;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
}

/* Main Container */
.profile-modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

/* Back Link */
.profile-back-link {
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  text-decoration: none;
  margin-bottom: 1.5rem;
  font-weight: 500;
  transition: color var(--transition-normal);
}

.profile-back-link:hover {
  color: var(--primary-dark);
}

.profile-back-link i {
  margin-right: 0.5rem;
}

/* Alert Styles */
.profile-alert {
  padding: 1rem 1.25rem;
  border-radius: var(--radius-sm);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  border-left: 4px solid transparent;
}

.profile-alert i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.profile-alert-success {
  background: var(--primary-very-light);
  color: var(--primary-dark);
  border-left-color: var(--primary-color);
}

/* Profile Layout */
.profile-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1.5rem;
}

/* Profile Sidebar */
.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Profile Card */
.profile-card {
  background: var(--bg-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.profile-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-light);
}

.profile-card-header h2 {
  color: var(--primary-color);
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.profile-card-header p {
  color: var(--text-light);
  margin: 0;
}

.profile-card-body {
  padding: 1.5rem;
}

/* Profile Image */
.profile-image-container {
  text-align: center;
  padding: 1.5rem;
}

.profile-image {
  width: 180px;
  height: 180px;
  border-radius: var(--radius-circle);
  object-fit: cover;
  border: 4px solid var(--bg-white);
  box-shadow: var(--shadow-md);
  margin: 0 auto 1.5rem;
  transition: transform var(--transition-normal);
}

.profile-image:hover {
  transform: scale(1.05);
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.profile-email {
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

/* Progress Chart */
.progress-chart-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.progress-chart-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.progress-stats {
  margin-top: 1rem;
}

.progress-stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.progress-stat-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.progress-stat-label {
  color: var(--text-medium);
  font-weight: 500;
}

.progress-stat-value {
  color: var(--text-dark);
  font-weight: 600;
}

/* Form Styles */
.profile-form-group {
  margin-bottom: 1.5rem;
}

.profile-form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
  font-weight: 500;
}

.profile-form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 1rem;
  transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
}

.profile-form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(107, 183, 72, 0.1);
  outline: none;
}

.profile-form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M8 11.5l-6-6h12l-6 6z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 12px;
}

/* Custom File Input */
.profile-file-input-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.profile-file-input {
  position: absolute;
  left: -9999px;
  opacity: 0;
  width: 0.1px;
  height: 0.1px;
  overflow: hidden;
}

.profile-file-input-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1.5rem;
  background-color: var(--bg-light);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.profile-file-input-label:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-very-light);
}

.profile-file-input-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.profile-file-input-text {
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.profile-file-input-subtext {
  font-size: 0.875rem;
  color: var(--text-light);
  text-align: center;
}

.profile-file-preview {
  display: flex;
  align-items: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: var(--bg-light);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.profile-file-preview-image {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-sm);
  object-fit: cover;
  margin-right: 1rem;
}

.profile-file-preview-info {
  flex: 1;
}

.profile-file-preview-name {
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.profile-file-preview-size {
  font-size: 0.75rem;
  color: var(--text-light);
}

.profile-file-preview-remove {
  color: #f44336;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  padding: 0.25rem;
  transition: color var(--transition-normal);
}

.profile-file-preview-remove:hover {
  color: #d32f2f;
}

/* Button Styles */
.profile-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  text-decoration: none;
}

.profile-btn i {
  margin-right: 0.5rem;
}

.profile-btn-primary {
  background: var(--primary-color);
  color: white;
}

.profile-btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.profile-btn-secondary {
  background: var(--bg-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.profile-btn-secondary:hover {
  background: var(--primary-very-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.profile-btn-danger {
  background: #f44336;
  color: white;
}

.profile-btn-danger:hover {
  background: #d32f2f;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 992px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .profile-modern-container {
    padding: 1rem;
  }

  .profile-btn {
    width: 100%;
  }
}
