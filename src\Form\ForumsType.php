<?php

namespace App\Form;

use App\Entity\Forums;
use Doctrine\DBAL\Types\StringType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;

class ForumsType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Title',
                'attr' => [
                    'class' => 'form-control form-field',
                    'placeholder' => 'Enter forum title',
                    'minlength' => 3,
                    'maxlength' => 255,
                ],
                'constraints' => [
                    new Assert\NotBlank([
                        'message' => 'Title cannot be empty',
                    ]),
                    new Assert\Length([
                        'min' => 3,
                        'max' => 255,
                        'minMessage' => 'Title must be at least {{ limit }} characters long',
                        'maxMessage' => 'Title cannot exceed {{ limit }} characters',
                    ]),
                    new Assert\Regex([
                        'pattern' => '/^[a-zA-Z0-9\s\-_\.]+$/',
                        'message' => 'Title can only contain alphanumeric characters, spaces, dashes (-), underscores (_), and dots (.)',
                    ]),
                ],
                'empty_data' => false,
            ])
            ->add('content', TextareaType::class, [
                'label' => 'Content',
                'attr' => [
                    'class' => 'form-control form-field',
                    'placeholder' => 'Enter forum content',
                    'rows' => 6,
                    'style' => 'resize: vertical;',
                ],
                'constraints' => [
                    new Assert\NotBlank([
                        'message' => 'Content cannot be empty',
                    ]),
                    new Assert\Length([
                        'min' => 10,
                        'minMessage' => 'Content must be at least {{ limit }} characters long',
                    ]),
                ],
                'empty_data' => false,
            ])
            ->add('createdat', DateTimeType::class, [
                'widget' => 'single_text',
                'required' => false,
                'attr' => ['style' => 'display: none;'],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Forums::class,
        ]);
    }
}
