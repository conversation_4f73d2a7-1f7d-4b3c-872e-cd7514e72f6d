<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - eco.net</title>
    
    {# Favicon #}
    <link rel="icon" type="image/x-icon" href="{{asset('front/img/eco-net.png')}}">
    
    {% block stylesheets %}
        {# CSS Files #}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
        <link href="{{ asset('back/css/style.css') }}" rel="stylesheet">
        <link rel="stylesheet" href="{{ asset('front/css/auth.css') }}">
        <link href="{{ asset('back/css/auth.css') }}" rel="stylesheet">
    {% endblock %}
</head>
<body>


    {% block body %}
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo mb-4">
                    <img src="{{ asset('front/img/eco_net.svg') }}" alt="eco.net" height="40">
                </div>
                <h1>Create Account</h1>
                <p>Join our eco-friendly community</p>
            </div>

            {# {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-{{ label }}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endfor %}  #}

            {{ form_start(registrationForm, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
                <div class="form-floating {% if not registrationForm.full_name.vars.valid %}is-invalid{% endif %}">
                    {{ form_widget(registrationForm.full_name, {
                        'attr': {
                            'class': 'form-control ' ~ (not registrationForm.full_name.vars.valid ? 'is-invalid' : '')
                        }
                    }) }}
                    {{ form_label(registrationForm.full_name) }}
                    {% if not registrationForm.full_name.vars.valid %}
                        <div class="invalid-feedback">
                            {{ form_errors(registrationForm.full_name) }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating {% if not registrationForm.email.vars.valid %}is-invalid{% endif %}">
                    {{ form_widget(registrationForm.email, {
                        'attr': {
                            'class': 'form-control ' ~ (not registrationForm.email.vars.valid ? 'is-invalid' : '')
                        }
                    }) }}
                    {{ form_label(registrationForm.email) }}
                    {% if not registrationForm.email.vars.valid %}
                        <div class="invalid-feedback">
                            {{ form_errors(registrationForm.email) }}
                        </div>
                    {% endif %}
                </div>

                <div class="gender-group mb-3 {% if not registrationForm.gender.vars.valid %}is-invalid{% endif %}">
                    {{ form_label(registrationForm.gender, null, {'label_attr': {'class': 'form-label'}}) }}
                    <div class="d-flex gap-4">
                        {% for choice in registrationForm.gender %}
                            <div class="form-check">
                                {{ form_widget(choice, {
                                    'attr': {
                                        'class': 'form-check-input ' ~ (not registrationForm.gender.vars.valid ? 'is-invalid' : '')
                                    }
                                }) }}
                                {{ form_label(choice, null, {'label_attr': {'class': 'form-check-label'}}) }}
                            </div>
                        {% endfor %}
                    </div>
                    {% if not registrationForm.gender.vars.valid %}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(registrationForm.gender) }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating {% if not registrationForm.password.first.vars.valid %}is-invalid{% endif %}">
                    {{ form_widget(registrationForm.password.first, {
                        'attr': {
                            'class': 'form-control ' ~ (not registrationForm.password.first.vars.valid ? 'is-invalid' : '')
                        }
                    }) }}
                    {{ form_label(registrationForm.password.first) }}
                    {% if not registrationForm.password.first.vars.valid %}
                        <div class="invalid-feedback">
                            {{ form_errors(registrationForm.password.first) }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating mb-3 {% if not registrationForm.password.second.vars.valid %}is-invalid{% endif %}">
                    {{ form_widget(registrationForm.password.second, {
                        'attr': {
                            'class': 'form-control ' ~ (not registrationForm.password.second.vars.valid ? 'is-invalid' : '')
                        }
                    }) }}
                    {{ form_label(registrationForm.password.second) }}
                    {% if not registrationForm.password.second.vars.valid %}
                        <div class="invalid-feedback">
                            {{ form_errors(registrationForm.password.second) }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-check mb-3 {% if not registrationForm.terms.vars.valid %}is-invalid{% endif %}">
                    {{ form_widget(registrationForm.terms, {
                        'attr': {
                            'class': 'form-check-input ' ~ (not registrationForm.terms.vars.valid ? 'is-invalid' : '')
                        }
                    }) }}
                    {{ form_label(registrationForm.terms, null, {'label_attr': {'class': 'form-check-label'}}) }}
                    {% if not registrationForm.terms.vars.valid %}
                        <div class="invalid-feedback">
                            {{ form_errors(registrationForm.terms) }}
                        </div>
                    {% endif %}
                </div>

                <button class="auth-btn primary-btn" type="submit">
                    <i class="ri-user-add-line"></i>
                    Create Account
                </button>

                <div class="auth-divider">
                    <span>or sign up with</span>
                </div>

                <div class="social-auth-buttons">
                    <a href="{{ path('back_auth_oauth_github') }}" class="social-auth-btn github-btn">
                        <i class="ri-github-fill"></i>
                        GitHub
                    </a>
                    <a href="{{ path('back_auth_oauth_google') }}" class="social-auth-btn google-btn">
                        <i class="ri-google-fill"></i>
                        Google
                    </a>
                </div>

                <div class="auth-footer">
                    <div class="auth-links">
                        <span class="text-muted">Already have an account?</span>
                        <a href="{{ path('back_auth_login') }}" class="login-link">
                            <i class="ri-login-circle-line"></i>
                            Sign in
                        </a>
                    </div>
                </div>
            {{ form_end(registrationForm) }}
        </div>
    </div>
    {% endblock %}

    {# JavaScript Files #}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
