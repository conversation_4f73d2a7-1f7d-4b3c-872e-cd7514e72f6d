<?php

namespace App\Controller\Back\Profile;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\User\UserInterface;

#[Route('/profile/password', name: 'back_profile_password_')]
class PasswordValidationController extends AbstractController
{
    #[Route('/validate-current', name: 'validate_current', methods: ['POST'])]
    public function validateCurrentPassword(
        Request $request,
        UserPasswordHasherInterface $passwordHasher
    ): JsonResponse {
        // Get the current user
        $user = $this->getUser();
        
        // Check if user is logged in
        if (!$user instanceof UserInterface) {
            return new JsonResponse(['valid' => false, 'message' => 'User not authenticated'], 401);
        }
        
        // Get the password from the request
        $data = json_decode($request->getContent(), true);
        $currentPassword = $data['currentPassword'] ?? '';
        
        // Validate the password
        $isValid = $passwordHasher->isPasswordValid($user, $currentPassword);
        
        // Return the result
        return new JsonResponse([
            'valid' => $isValid,
            'message' => $isValid ? 'Current password is valid' : 'Current password is incorrect'
        ]);
    }
}
