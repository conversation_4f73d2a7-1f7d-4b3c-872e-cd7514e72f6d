{% extends 'back/base.html.twig' %}

{% block title %}Edit Donation{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .donation-form {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-top: 2rem;
        }
        .page-title {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #4CAF50;
        }
        .back-link {
            color: #4CAF50;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .back-link:hover {
            color: #45a049;
        }
        .form-control:focus, .form-select:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        .btn-primary {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }
        .btn-primary:hover {
            background-color: #45a049;
            border-color: #45a049;
        }
        .form-label {
            color: #2c3e50;
            font-weight: 500;
        }
        .invalid-feedback {
            color: #dc3545;
            font-size: 0.875rem;
        }
        .action-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container mt-4">
        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endfor %}

        <a href="{{ path('app_back_donation_index') }}" class="back-link">
            <i class="fas fa-arrow-left me-2"></i> Back to Donations List
        </a>
        
        <div class="donation-form">
            <h1 class="page-title">Edit Donation</h1>
            {{ include('back/pages/donation/_form.html.twig', {'button_label': 'Update'}) }}
            
            <div class="action-buttons">
                {{ include('back/pages/donation/_delete_form.html.twig') }}
            </div>
        </div>
    </div>
{% endblock %}
