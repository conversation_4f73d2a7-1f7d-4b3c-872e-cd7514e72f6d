{% extends 'front/base.html.twig' %}

{% block title %}Change Password{% endblock %}

{% block stylesheets %}
    <link rel="shortcut icon" href="{{asset('front/img/eco-net.png')}}" type="image/x-icon"/>

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:wght@400;500;700;900&display=swap" rel="stylesheet"/>

    <!-- Icon Font Stylesheet -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet"/>

    <!-- Libraries Stylesheet -->
    <link rel="stylesheet" href="{{asset('front/lib/animate/animate.min.css')}}"/>
    <link href="{{asset('front/lib/lightbox/css/lightbox.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('front/lib/owlcarousel/assets/owl.carousel.min.css')}}" rel="stylesheet"/>

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{asset('front/css/bootstrap.min.css')}}" rel="stylesheet"/>

    <!-- Template Stylesheet -->
    <link href="{{asset('front/css/style.css')}}" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Modern Profile Stylesheet -->
    <link rel="stylesheet" href="{{ asset('assets/css/profile-modern.css') }}">

    <!-- Password Validation Styles -->
    <style>
        .password-requirements {
            margin-bottom: 1.5rem;
            padding: 1.25rem;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #6BB748;
            transition: all 0.3s ease;
        }

        .password-requirements h4 {
            font-size: 1rem;
            color: #333;
            margin-bottom: 0.75rem;
            font-weight: 600;
        }

        .password-requirements ul {
            margin-bottom: 0;
            padding-left: 0;
            list-style-type: none;
        }

        .password-requirement {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: #555;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .password-requirement:last-child {
            margin-bottom: 0;
        }

        .requirement-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            margin-right: 0.75rem;
            background-color: #e0e0e0;
            color: #fff;
            font-size: 0.7rem;
            transition: all 0.3s ease;
        }

        .requirement-valid .requirement-icon {
            background-color: #6BB748;
        }

        .requirement-valid {
            color: #6BB748;
        }

        .password-match-indicator {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #555;
            transition: color 0.3s ease;
        }

        .password-match-indicator.valid {
            color: #6BB748;
        }

        .password-match-indicator.invalid {
            color: #f44336;
        }

        .password-strength-meter {
            height: 6px;
            background-color: #e0e0e0;
            margin-top: 0.75rem;
            border-radius: 3px;
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .password-strength-value {
            height: 100%;
            width: 0;
            background-color: #f44336;
            transition: width 0.3s ease, background-color 0.3s ease;
            border-radius: 3px;
        }

        .password-strength-value.weak {
            background-color: #f44336;
            width: 25%;
        }

        .password-strength-value.medium {
            background-color: #ff9800;
            width: 50%;
        }

        .password-strength-value.strong {
            background-color: #4caf50;
            width: 75%;
        }

        .password-strength-value.very-strong {
            background-color: #6BB748;
            width: 100%;
        }

        .password-strength-text {
            display: block;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            text-align: right;
            color: #555;
        }

        /* Password visibility toggle */
        .password-field-wrapper {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            outline: none;
        }

        .password-toggle:hover {
            color: #6BB748;
            background-color: rgba(107, 183, 72, 0.1);
        }

        .password-toggle:active {
            transform: translateY(-50%) scale(0.95);
        }

        .password-toggle:focus {
            box-shadow: 0 0 0 2px rgba(107, 183, 72, 0.25);
        }

        .password-toggle i {
            transition: transform 0.2s ease;
        }

        .password-toggle:hover i {
            transform: scale(1.1);
        }

        /* Button styles */
        .profile-btn-primary {
            background-color: #6BB748;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .button-status-indicator {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s ease;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        .profile-btn-primary:hover:not(:disabled) {
            background-color: #5a9c3c;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .profile-btn-primary:disabled {
            background-color: #cccccc;
            color: #888888;
            cursor: not-allowed;
            box-shadow: none;
            opacity: 0.7;
        }

        .profile-btn-primary i {
            margin-right: 0.5rem;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Function to handle page navigation detection
        function handlePageShow(e) {
            // If this is a page navigation (not a refresh), force validation
            if (e.persisted) {
                console.log('Page was loaded from cache (navigation). Forcing validation...');
                initializeValidation(true);
            }
        }

        // Add pageshow event listener to detect navigation from cache
        window.addEventListener('pageshow', handlePageShow);

        // Add turbo:load event listener for Turbo Drive navigation if it's being used
        document.addEventListener('turbo:load', function() {
            console.log('Turbo navigation detected. Forcing validation...');
            initializeValidation(true);
        });

        // Add popstate event listener for history navigation
        window.addEventListener('popstate', function() {
            console.log('History navigation detected. Forcing validation...');
            setTimeout(() => initializeValidation(true), 0);
        });

        // Main initialization function
        function initializeValidation(forceValidation = false) {
            // Get password fields - try both direct IDs and form-generated IDs
            const currentPasswordField = document.getElementById('current-password') ||
                                        document.querySelector('input[name="change_password[currentPassword]"]');
            const newPasswordField = document.getElementById('new-password') ||
                                    document.querySelector('input[name="change_password[password][first]"]');
            const repeatPasswordField = document.getElementById('repeat-password') ||
                                       document.querySelector('input[name="change_password[password][second]"]');

            // Check if elements exist before proceeding
            if (!currentPasswordField || !newPasswordField || !repeatPasswordField) {
                console.error('Password fields not found. Form validation disabled.');
                return;
            }

            // Get password requirement elements
            const lengthRequirement = document.getElementById('length-requirement') ||
                                     document.querySelector('.password-requirement:nth-child(1)');
            const uppercaseRequirement = document.getElementById('uppercase-requirement') ||
                                        document.querySelector('.password-requirement:nth-child(2)');
            const lowercaseRequirement = document.getElementById('lowercase-requirement') ||
                                        document.querySelector('.password-requirement:nth-child(3)');
            const numberRequirement = document.getElementById('number-requirement') ||
                                     document.querySelector('.password-requirement:nth-child(4)');
            const specialRequirement = document.getElementById('special-requirement') ||
                                      document.querySelector('.password-requirement:nth-child(5)');

            // Check if requirement elements exist
            if (!lengthRequirement || !uppercaseRequirement || !lowercaseRequirement ||
                !numberRequirement || !specialRequirement) {
                console.error('Password requirement elements not found. Validation disabled.');
                return;
            }

            // Get password strength elements
            const strengthMeter = document.querySelector('.password-strength-value');
            const strengthText = document.querySelector('.password-strength-text');

            // Check if strength elements exist
            if (!strengthMeter || !strengthText) {
                console.error('Password strength elements not found. Strength meter disabled.');
                return;
            }

            // Get password match indicator
            const passwordMatchIndicator = document.getElementById('password-match-indicator') ||
                                          document.querySelector('.password-match-indicator');
            const currentPasswordFeedback = document.getElementById('current-password-feedback') ||
                                           document.querySelector('#current-password-feedback');

            // Check if indicators exist
            if (!passwordMatchIndicator || !currentPasswordFeedback) {
                console.error('Password match indicators not found. Match validation disabled.');
                return;
            }

            // Get submit button
            const submitButton = document.querySelector('button[type="submit"]') ||
                               document.querySelector('.profile-btn-primary');
            if (!submitButton) {
                console.error('Submit button not found. Form validation disabled.');
                return;
            }

            // Password toggle buttons
            const toggleButtons = document.querySelectorAll('.password-toggle');
            if (toggleButtons.length > 0) {
                toggleButtons.forEach(button => {
                    if (button) {
                        // Remove any existing event listeners to prevent duplicates
                        const newButton = button.cloneNode(true);
                        button.parentNode.replaceChild(newButton, button);

                        // Add the event listener to the new button
                        newButton.addEventListener('click', function(e) {
                            e.preventDefault(); // Prevent form submission
                            e.stopPropagation(); // Stop event propagation

                            // Find the target field using multiple methods
                            let targetField = null;

                            // Method 1: Try using data-target attribute with getElementById
                            const targetId = this.getAttribute('data-target');
                            if (targetId) {
                                targetField = document.getElementById(targetId);
                            }

                            // Method 2: If not found, try finding the closest input in the parent wrapper
                            if (!targetField) {
                                const wrapper = this.closest('.password-field-wrapper');
                                if (wrapper) {
                                    targetField = wrapper.querySelector('input[type="password"], input[type="text"]');
                                }
                            }

                            // If we still don't have a target field, exit
                            if (!targetField) {
                                console.error('Password field not found for toggle button');
                                return;
                            }

                            // Get the icon element
                            const icon = this.querySelector('i');
                            if (!icon) return;

                            // Toggle password visibility
                            if (targetField.type === 'password') {
                                targetField.type = 'text';
                                icon.classList.remove('fa-eye');
                                icon.classList.add('fa-eye-slash');

                                // Add a tooltip to indicate password is visible
                                this.title = 'Hide password';
                            } else {
                                targetField.type = 'password';
                                icon.classList.remove('fa-eye-slash');
                                icon.classList.add('fa-eye');

                                // Add a tooltip to indicate password is hidden
                                this.title = 'Show password';
                            }

                            // Ensure the field keeps focus if it had it
                            if (document.activeElement === targetField) {
                                setTimeout(() => targetField.focus(), 0);
                            }
                        });
                    }
                });

                console.log('Password toggle buttons event listeners attached:', toggleButtons.length);
            }

            // Function to check if password meets requirements
            function validatePassword(password) {
                if (typeof password !== 'string') {
                    password = '';
                }

                // Check length
                const isLengthValid = password.length >= 8;
                toggleRequirement(lengthRequirement, isLengthValid);

                // Check uppercase
                const hasUppercase = /[A-Z]/.test(password);
                toggleRequirement(uppercaseRequirement, hasUppercase);

                // Check lowercase
                const hasLowercase = /[a-z]/.test(password);
                toggleRequirement(lowercaseRequirement, hasLowercase);

                // Check number
                const hasNumber = /[0-9]/.test(password);
                toggleRequirement(numberRequirement, hasNumber);

                // Check special character
                const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
                toggleRequirement(specialRequirement, hasSpecial);

                // Calculate password strength
                let strength = 0;
                if (isLengthValid) strength += 1;
                if (hasUppercase) strength += 1;
                if (hasLowercase) strength += 1;
                if (hasNumber) strength += 1;
                if (hasSpecial) strength += 1;

                // If password is empty, set strength to 0 regardless of previous calculation
                if (password.length === 0) {
                    strength = 0;
                }

                updatePasswordStrength(strength);

                // Return true if all requirements are met
                return isLengthValid && hasUppercase && hasLowercase && hasNumber && hasSpecial;
            }

            // Function to toggle requirement class
            function toggleRequirement(element, isValid) {
                if (!element) return;

                if (isValid) {
                    element.classList.add('requirement-valid');
                } else {
                    element.classList.remove('requirement-valid');
                }
            }

            // Function to update password strength indicator
            function updatePasswordStrength(strength) {
                if (!strengthMeter || !strengthText) return;

                // Remove all classes
                strengthMeter.classList.remove('weak', 'medium', 'strong', 'very-strong');

                // Update strength meter based on score
                if (strength === 0) {
                    strengthText.textContent = 'Password strength: Not entered';
                    strengthMeter.style.width = '0';
                } else if (strength === 1) {
                    strengthMeter.classList.add('weak');
                    strengthMeter.style.width = '25%';
                    strengthText.textContent = 'Password strength: Weak';
                } else if (strength === 2) {
                    strengthMeter.classList.add('medium');
                    strengthMeter.style.width = '50%';
                    strengthText.textContent = 'Password strength: Medium';
                } else if (strength === 3 || strength === 4) {
                    strengthMeter.classList.add('strong');
                    strengthMeter.style.width = '75%';
                    strengthText.textContent = 'Password strength: Strong';
                } else if (strength === 5) {
                    strengthMeter.classList.add('very-strong');
                    strengthMeter.style.width = '100%';
                    strengthText.textContent = 'Password strength: Very Strong';
                }
            }

            // Function to check if passwords match
            function checkPasswordsMatch() {
                if (!passwordMatchIndicator || !newPasswordField || !repeatPasswordField) return false;

                const newPassword = newPasswordField.value || '';
                const repeatPassword = repeatPasswordField.value || '';

                // If either field is empty, hide the indicator
                if (newPassword.length === 0 || repeatPassword.length === 0) {
                    passwordMatchIndicator.style.display = 'none';
                    return false;
                }

                const iconElement = passwordMatchIndicator.querySelector('i');
                const textElement = passwordMatchIndicator.querySelector('span');

                if (!iconElement || !textElement) {
                    console.error('Password match indicator elements not found');
                    return false;
                }

                if (newPassword === repeatPassword) {
                    passwordMatchIndicator.style.display = 'flex';
                    passwordMatchIndicator.classList.add('valid');
                    passwordMatchIndicator.classList.remove('invalid');
                    iconElement.className = 'fas fa-check-circle';
                    iconElement.style.marginRight = '0.5rem';
                    textElement.textContent = 'Passwords match';
                    return true;
                } else {
                    passwordMatchIndicator.style.display = 'flex';
                    passwordMatchIndicator.classList.add('invalid');
                    passwordMatchIndicator.classList.remove('valid');
                    iconElement.className = 'fas fa-times-circle';
                    iconElement.style.marginRight = '0.5rem';
                    textElement.textContent = 'Passwords do not match';
                    return false;
                }
            }

            // Function to validate current password via AJAX
            let currentPasswordValid = false;
            let currentPasswordValidationTimeout;
            let currentPasswordLastValidated = ''; // Track last validated password
            let userHasInteractedWithCurrentPassword = false; // Track if user has interacted with the field

            function validateCurrentPassword(forceValidation = false) {
                if (!currentPasswordField || !currentPasswordFeedback) return false;

                const currentPassword = currentPasswordField.value || '';

                // If password is empty, hide feedback and return
                if (currentPassword.length === 0) {
                    currentPasswordFeedback.style.display = 'none';
                    currentPasswordValid = false;
                    return false;
                }

                // Skip validation if the password hasn't changed and we're not forcing validation
                if (!forceValidation && currentPassword === currentPasswordLastValidated) {
                    return currentPasswordValid;
                }

                // Don't show feedback unless user has interacted with the field or we're forcing validation
                // with a non-empty field that the user has entered
                const shouldShowFeedback = userHasInteractedWithCurrentPassword ||
                                          (forceValidation && currentPassword !== 'silent-warmup' &&
                                           currentPassword !== 'endpoint-check');

                // Update the last validated password
                currentPasswordLastValidated = currentPassword;

                // Clear any existing timeout
                if (currentPasswordValidationTimeout) {
                    clearTimeout(currentPasswordValidationTimeout);
                }

                // Set a timeout to avoid too many requests while typing
                currentPasswordValidationTimeout = setTimeout(() => {
                    // Show loading state only if we should show feedback
                    const iconElement = currentPasswordFeedback.querySelector('i');
                    const textElement = currentPasswordFeedback.querySelector('span');

                    if (shouldShowFeedback && iconElement && textElement) {
                        currentPasswordFeedback.style.display = 'flex';
                        iconElement.className = 'fas fa-spinner fa-spin';
                        textElement.textContent = 'Validating...';
                    } else {
                        currentPasswordFeedback.style.display = 'none';
                    }

                    // Send AJAX request to validate the password
                    fetch('{{ path('back_profile_password_validate_current') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({ currentPassword: currentPassword })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (!iconElement || !textElement) return;

                        // Only show feedback if user has interacted with the field
                        if (shouldShowFeedback) {
                            currentPasswordFeedback.style.display = 'flex';

                            if (data.valid) {
                                currentPasswordValid = true;
                                currentPasswordFeedback.classList.add('valid');
                                currentPasswordFeedback.classList.remove('invalid');
                                iconElement.className = 'fas fa-check-circle';
                                textElement.textContent = data.message;
                            } else {
                                currentPasswordValid = false;
                                currentPasswordFeedback.classList.add('invalid');
                                currentPasswordFeedback.classList.remove('valid');
                                iconElement.className = 'fas fa-times-circle';
                                textElement.textContent = data.message;
                            }
                        } else {
                            // Just update the validation state without showing feedback
                            currentPasswordValid = data.valid;
                            currentPasswordFeedback.style.display = 'none';
                        }

                        // Update form validity
                        checkFormValidity();
                    })
                    .catch(error => {
                        console.error('Error validating password:', error);
                        currentPasswordValid = false;

                        if (shouldShowFeedback && iconElement && textElement) {
                            currentPasswordFeedback.style.display = 'flex';
                            currentPasswordFeedback.classList.add('invalid');
                            currentPasswordFeedback.classList.remove('valid');
                            iconElement.className = 'fas fa-exclamation-circle';
                            textElement.textContent = 'Error validating password';
                        } else {
                            currentPasswordFeedback.style.display = 'none';
                        }

                        checkFormValidity();
                    });
                }, 500); // 500ms delay to avoid too many requests

                return currentPasswordValid;
            }

            // Function to check if form is valid
            function checkFormValidity() {
                if (!submitButton) return;

                // Get the new password value
                const newPassword = newPasswordField ? newPasswordField.value : '';
                const repeatPassword = repeatPasswordField ? repeatPasswordField.value : '';
                const currentPassword = currentPasswordField ? currentPasswordField.value : '';

                // Check if fields have values
                const hasCurrentPassword = currentPassword.length > 0;
                const hasNewPassword = newPassword.length > 0;
                const hasRepeatPassword = repeatPassword.length > 0;

                // Check all requirements individually
                const isLengthValid = newPassword.length >= 8;
                const hasUppercase = /[A-Z]/.test(newPassword);
                const hasLowercase = /[a-z]/.test(newPassword);
                const hasNumber = /[0-9]/.test(newPassword);
                const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(newPassword);

                // All password requirements must be met
                const isNewPasswordValid = isLengthValid && hasUppercase && hasLowercase && hasNumber && hasSpecial;

                // Check if passwords match
                const doPasswordsMatch = hasNewPassword && hasRepeatPassword && newPassword === repeatPassword;

                // Check if all conditions are met
                const isFormValid = hasCurrentPassword && currentPasswordValid && isNewPasswordValid && doPasswordsMatch;

                // Get the status indicator
                const statusIndicator = submitButton.querySelector('.button-status-indicator');

                // Update button state with visual feedback
                if (isFormValid) {
                    submitButton.disabled = false;
                    submitButton.title = "All requirements met. You can update your password.";

                    // Show the status indicator
                    if (statusIndicator) {
                        statusIndicator.style.display = 'inline-block';
                    }
                } else {
                    submitButton.disabled = true;

                    // Hide the status indicator
                    if (statusIndicator) {
                        statusIndicator.style.display = 'none';
                    }

                    // Provide specific feedback on what's missing
                    if (!hasCurrentPassword) {
                        submitButton.title = "Please enter your current password";
                    } else if (!currentPasswordValid) {
                        submitButton.title = "Please enter your current password correctly";
                    } else if (!hasNewPassword) {
                        submitButton.title = "Please enter a new password";
                    } else if (!isNewPasswordValid) {
                        submitButton.title = "Please ensure your new password meets all requirements";
                    } else if (!hasRepeatPassword) {
                        submitButton.title = "Please confirm your new password";
                    } else if (!doPasswordsMatch) {
                        submitButton.title = "Please ensure your passwords match";
                    }
                }

                return isFormValid;
            }

            // Add event listeners safely
            if (newPasswordField) {
                newPasswordField.addEventListener('input', function() {
                    validatePassword(this.value || '');
                    checkPasswordsMatch();
                    checkFormValidity();
                });
            }

            if (repeatPasswordField) {
                repeatPasswordField.addEventListener('input', function() {
                    checkPasswordsMatch();
                    checkFormValidity();
                });
            }

            if (currentPasswordField) {
                // Track when user has interacted with the current password field
                currentPasswordField.addEventListener('focus', function() {
                    userHasInteractedWithCurrentPassword = true;
                });

                // Validate on input
                currentPasswordField.addEventListener('input', function() {
                    userHasInteractedWithCurrentPassword = true;
                    validateCurrentPassword();
                    checkFormValidity();
                });
            }

            // Add form submission handler
            const form = document.querySelector('form.profile-form');
            if (form) {
                form.addEventListener('submit', function() {
                    // Mark the form as submitted to prevent clearing fields during initialization
                    this.classList.add('submitted');
                });
            }

            // Initialize password toggle buttons
            function initializePasswordToggles() {
                // Find all password fields (both type="password" and type="text" that should be password)
                const passwordFields = document.querySelectorAll('.password-field-wrapper input');

                passwordFields.forEach(field => {
                    // Reset to password type
                    field.type = 'password';

                    // Clear any value that might be showing during page load
                    if (field.id === 'current-password') {
                        // Only clear if we're not in a form submission (to avoid losing user input)
                        if (!document.querySelector('form').classList.contains('submitted')) {
                            field.value = '';
                        }
                    }

                    // Find and reset the associated toggle button
                    const wrapper = field.closest('.password-field-wrapper');
                    if (wrapper) {
                        const toggleButton = wrapper.querySelector('.password-toggle');
                        if (toggleButton) {
                            const icon = toggleButton.querySelector('i');
                            if (icon) {
                                icon.classList.remove('fa-eye-slash');
                                icon.classList.add('fa-eye');
                            }
                            toggleButton.title = 'Show password';
                        }
                    }
                });

                console.log('Password toggle buttons initialized');
            }

            // Initial validation - only if all elements exist
            if (newPasswordField && repeatPasswordField && currentPasswordField) {
                // Initialize password validation state for empty fields
                validatePassword('');

                // Initialize password match state
                passwordMatchIndicator.style.display = 'none';

                // Initialize current password validation state
                currentPasswordFeedback.style.display = 'none';
                currentPasswordValid = false;

                // Initialize form validity (should disable the button initially)
                checkFormValidity();

                // Initialize password toggles
                initializePasswordToggles();

                // Force validation on page load to ensure validators are active
                // This is important when navigating between pages without a refresh
                setTimeout(function() {
                    console.log('Running delayed validation...');

                    // Re-validate with current values (if any)
                    validatePassword(newPasswordField.value || '');
                    checkPasswordsMatch();

                    // Only validate current password if it has a value
                    if (currentPasswordField.value && currentPasswordField.value.length > 0) {
                        validateCurrentPassword(true); // Force validation on page load
                    } else if (forceValidation) {
                        // If we're forcing validation, just warm up the AJAX endpoint silently
                        // without modifying the input field or showing any feedback
                        silentlyWarmUpAjaxEndpoint();
                    }

                    // Update form validity
                    checkFormValidity();
                }, forceValidation ? 0 : 100); // Run immediately if forcing validation
            }

            // Log initialization completion
            console.log('Password validation initialized' + (forceValidation ? ' (forced)' : ''));
        }

        // Function to silently warm up the AJAX endpoint without showing any UI feedback
        function silentlyWarmUpAjaxEndpoint() {
            console.log('Silently warming up AJAX endpoint...');

            // Make a silent request to the endpoint without affecting the UI
            fetch('{{ path('back_profile_password_validate_current') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ currentPassword: 'silent-warmup' })
            })
            .then(response => response.json())
            .then(data => {
                console.log('AJAX endpoint warmed up successfully');
            })
            .catch(error => {
                console.error('Error warming up AJAX endpoint:', error);
            });
        }

        // Function to check if AJAX endpoint is ready
        function checkAjaxEndpoint() {
            return new Promise((resolve, reject) => {
                fetch('{{ path('back_profile_password_validate_current') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ currentPassword: 'endpoint-check' })
                })
                .then(response => {
                    if (response.ok) {
                        console.log('AJAX endpoint is ready');
                        resolve(true);
                    } else {
                        console.warn('AJAX endpoint returned error status:', response.status);
                        resolve(false);
                    }
                })
                .catch(error => {
                    console.error('Error checking AJAX endpoint:', error);
                    resolve(false);
                });
            });
        }

        // Call the initialization function on DOMContentLoaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired');

            // Initialize password toggles immediately to prevent flashing of password
            const passwordFields = document.querySelectorAll('.password-field-wrapper input');
            passwordFields.forEach(field => {
                if (field.id === 'current-password') {
                    field.value = ''; // Clear any value that might be showing
                }
                field.type = 'password'; // Ensure it's set to password type
            });

            // Check if AJAX endpoint is ready before initializing
            checkAjaxEndpoint().then(isReady => {
                initializeValidation(isReady);
            });
        });

        // Also run initialization immediately (not waiting for DOMContentLoaded)
        // This helps prevent the password from being visible during page load
        (function() {
            const passwordFields = document.querySelectorAll('.password-field-wrapper input');
            passwordFields.forEach(field => {
                if (field.id === 'current-password') {
                    field.value = ''; // Clear any value that might be showing
                }
                field.type = 'password'; // Ensure it's set to password type
            });
        })();
    </script>
{% endblock %}

{% block content %}
    <!-- Navbar & Hero Start -->
    <div class="container-fluid position-relative p-0">
        {% include 'front/includes/navbar.html.twig' %}

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb-profile">
            <div class="container text-center py-5" style="max-width: 900px">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                    Change Password
                </h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('app_home')}}">Home</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('back_profile_edit')}}">Profile</a>
                    </li>
                    <li class="breadcrumb-item active text-primary">Change Password</li>
                </ol>
            </div>
        </div>
        <!-- Header End -->
    </div>
    <!-- Navbar & Hero End -->

    <!-- Modern Profile Container -->
    <div class="profile-modern-container">

        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% if (label == 'success') %}
                {% for message in messages %}
                    <div class="profile-alert profile-alert-{{ label }}">
                        <i class="fas fa-{{ label == 'success' ? 'check-circle' : 'exclamation-circle' }}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endfor %}

        <!-- Password Change Card -->
        <div class="profile-card" style="max-width: 600px; margin: 0 auto;">
            <div class="profile-card-header">
                <h2>Change Password</h2>
                <p>Update your account password</p>
            </div>

            <div class="profile-card-body">
                {{ form_start(form, {'attr': {'class': 'profile-form', 'novalidate': 'novalidate'}}) }}
                    <div class="profile-form-group">
                        {{ form_label(form.currentPassword, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        <div class="password-field-wrapper">
                            {{ form_widget(form.currentPassword, {'attr': {'class': 'profile-form-control', 'id': 'current-password'}}) }}
                            <button type="button" class="password-toggle" data-target="current-password" tabindex="-1" title="Show password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="current-password-feedback" class="password-match-indicator" style="display: none;">
                            <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
                            <span>Current password is valid</span>
                        </div>
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.currentPassword) }}
                        </span>
                    </div>

                    <div class="profile-form-group">
                        {{ form_label(form.password.first, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        <div class="password-field-wrapper">
                            {{ form_widget(form.password.first, {'attr': {'class': 'profile-form-control', 'id': 'new-password'}}) }}
                            <button type="button" class="password-toggle" data-target="new-password" tabindex="-1" title="Show password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength-meter">
                            <div class="password-strength-value"></div>
                        </div>
                        <span class="password-strength-text">Password strength: Not entered</span>
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.password.first) }}
                        </span>
                    </div>

                    <div class="profile-form-group">
                        {{ form_label(form.password.second, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        <div class="password-field-wrapper">
                            {{ form_widget(form.password.second, {'attr': {'class': 'profile-form-control', 'id': 'repeat-password'}}) }}
                            <button type="button" class="password-toggle" data-target="repeat-password" tabindex="-1" title="Show password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="password-match-indicator" class="password-match-indicator" style="display: none;">
                            <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
                            <span>Passwords match</span>
                        </div>
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.password.second) }}
                        </span>
                    </div>

                    <div class="password-requirements">
                        <h4>Password Requirements:</h4>
                        <ul>
                            <li class="password-requirement" id="length-requirement">
                                <span class="requirement-icon"><i class="fas fa-check"></i></span>
                                <span>At least 8 characters long</span>
                            </li>
                            <li class="password-requirement" id="uppercase-requirement">
                                <span class="requirement-icon"><i class="fas fa-check"></i></span>
                                <span>Include at least one uppercase letter</span>
                            </li>
                            <li class="password-requirement" id="lowercase-requirement">
                                <span class="requirement-icon"><i class="fas fa-check"></i></span>
                                <span>Include at least one lowercase letter</span>
                            </li>
                            <li class="password-requirement" id="number-requirement">
                                <span class="requirement-icon"><i class="fas fa-check"></i></span>
                                <span>Include at least one number</span>
                            </li>
                            <li class="password-requirement" id="special-requirement">
                                <span class="requirement-icon"><i class="fas fa-check"></i></span>
                                <span>Include at least one special character</span>
                            </li>
                        </ul>
                    </div>

                    <button type="submit" class="profile-btn profile-btn-primary" disabled title="Please complete all password requirements">
                        <i class="fas fa-key"></i> Update Password
                        <span class="button-status-indicator" style="margin-left: 8px; display: none;">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </button>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}