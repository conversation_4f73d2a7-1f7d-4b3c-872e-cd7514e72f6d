{% extends 'front/base.html.twig' %}

{% block content %}

<!-- Nav<PERSON> & <PERSON> Start -->
	<div class="container-fluid position-relative p-0">

		{% include 'front/includes/navbar.html.twig' %}

		<!-- Header Start -->
		<div class="container-fluid bg-breadcrumb-products">
			<div class="container text-center py-5" style="max-width: 900px">
				<h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
					Products
				</h4>
				<ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
					<li class="breadcrumb-item">
						<a class="text-white" href="{{path('app_home')}}">Home</a>
					</li>
					<li class="breadcrumb-item active text-white-50">Pages</li>
					<li class="breadcrumb-item active text-primary">Products</li>
				</ol>
			</div>
		</div>
		<!-- Header End -->
	</div>
	<!-- Navbar & Hero End -->

    
<div class="container mt-5">
    <h1 class="mb-4 text-center">Your Cart</h1>
    <div class="row">
        <!-- Cart Items List -->
        <div class="col-md-8">
            <div id="cart-list"></div>
            <div id="empty-cart-message" class="alert alert-info text-center" style="display:none;">
                Your cart is empty.
            </div>
        </div>

        <!-- Order Summary & Command Form -->
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Order Summary</h4>
                </div>
                <div class="card-body">
                    <p id="total-amount" class="h5">Total: 0 DT</p>
                    <hr>
                    <form id="command-form" action="{{ path('app_command_from_cart') }}" method="post">
                        <div class="mb-3">
                            <label for="deliveryAddress" class="form-label">Delivery Address</label>
                            <input type="text" id="deliveryAddress" name="deliveryAddress" class="form-control" placeholder="Enter delivery address" required>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea id="notes" name="notes" class="form-control" rows="3" placeholder="Additional instructions"></textarea>
                        </div>
                        <!-- Hidden Fields -->
                        <input type="hidden" id="totalAmount" name="totalAmount">
                        <input type="hidden" id="id_user" name="id_user" value="4">
                        <input type="hidden" id="products" name="products">
                        <button type="submit" class="btn btn-success w-100">Add Command</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript to Render Cart and Handle Removals -->
<script>
    // Function to render the cart and update the order summary
    function renderCart() {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        const cartList = document.getElementById('cart-list');
        const emptyMessage = document.getElementById('empty-cart-message');
        let total = 0;
        let html = '';
        if (cart.length === 0) {
            emptyMessage.style.display = 'block';
            cartList.innerHTML = '';
        } else {
            emptyMessage.style.display = 'none';
            cart.forEach((product, index) => {
                total += product.price * product.quantity;
                html += `
                <div class="card mb-3">
                    <div class="row g-0">
                        <div class="col-md-4">
                            <img src="http://localhost/img/${product.image}" class="img-fluid rounded-start" alt="${product.name}">
                        </div>
                        <div class="col-md-8">
                            <div class="card-body">
                                <h5 class="card-title">${product.name}</h5>
                                <p class="card-text">Price: ${product.price} DT</p>
                                <p class="card-text">Quantity: ${product.quantity}</p>
                                <button class="btn btn-danger btn-sm" onclick="removeFromCart(${index})">Remove</button>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            });
            cartList.innerHTML = html;
        }
        // Update the displayed total and hidden total field
        document.getElementById('total-amount').innerText = 'Total: ' + total.toFixed(2) + ' DT';
        document.getElementById('totalAmount').value = total.toFixed(2);
        // Store the cart items as JSON in a hidden field so that they can be submitted
        document.getElementById('products').value = JSON.stringify(cart);
    }

    // Remove an item from the cart using its index
    function removeFromCart(index) {
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        cart.splice(index, 1);
        localStorage.setItem('cart', JSON.stringify(cart));
        renderCart();
    }

    // Ensure the DOM is fully loaded before rendering the cart
    document.addEventListener('DOMContentLoaded', function(){
        renderCart();
    });
</script>
{% endblock %}
