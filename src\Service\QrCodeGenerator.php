<?php
namespace App\Service;

class QrCodeGenerator
{
    public function generate(string $content, int $id, string $prefix='produit'): string
    {
        $qrCodePath = "uploads/qrcode/{$prefix}-{$id}.png";

        // Return existing file if it exists
        if(file_exists($qrCodePath)){
            return $qrCodePath;
        }

        // Create directory if it doesn't exist
        $directory = dirname($qrCodePath);
        if (!file_exists($directory)) {
            mkdir($directory, 0777, true);
        }

        // Create QR code using the Builder
        $builder = \Endroid\QrCode\Builder\Builder::create();
        $builder->data($content);
        $builder->writer(new \Endroid\QrCode\Writer\PngWriter());
        $result = $builder->build();

        // Save QR code
        $result->saveToFile($qrCodePath);

        return $qrCodePath;
    }
}