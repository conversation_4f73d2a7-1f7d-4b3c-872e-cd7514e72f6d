{% extends 'back/base.html.twig' %}

{% block title %}Edit Partner{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .partner-form {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-top: 2rem;
        }
        .form-control:focus, .form-select:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        .btn-primary {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }
        .btn-primary:hover {
            background-color: #45a049;
            border-color: #45a049;
        }
        .page-title {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #4CAF50;
        }
        .back-link {
            color: #4CAF50;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .back-link:hover {
            color: #45a049;
        }
        .form-label {
            color: #2c3e50;
            font-weight: 500;
        }
        /* Error styling */
        .form-error {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: block;
        }
        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: #dc3545;
            padding-right: calc(1.5em + 0.75rem);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .form-control.is-invalid:focus,
        .form-select.is-invalid:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        /* Alert styling */
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn-close {
            font-size: 0.8rem;
            opacity: 0.5;
        }
        .btn-close:hover {
            opacity: 0.75;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container mt-4">
        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endfor %}

        <a href="{{ path('app_partners_index') }}" class="back-link">
            <i class="fas fa-arrow-left me-2"></i> Back to Partners List
        </a>
        
        <div class="partner-form">
            <h1 class="page-title">Edit Partner</h1>
            
            {{ form_start(form) }}
            {{ form_errors(form) }}

            <div class="mb-3">
                {{ form_label(form.name) }}
                {{ form_widget(form.name, {
                    'attr': {
                        'class': 'form-control' ~ (form.name.vars.valid ? '' : ' is-invalid')
                    }
                }) }}
                {{ form_errors(form.name) }}
            </div>

            <div class="mb-3">
                {{ form_label(form.type) }}
                {{ form_widget(form.type, {
                    'attr': {
                        'class': 'form-select' ~ (form.type.vars.valid ? '' : ' is-invalid')
                    }
                }) }}
                {{ form_errors(form.type) }}
            </div>

            <div class="mb-3">
                {{ form_label(form.description) }}
                {{ form_widget(form.description, {
                    'attr': {
                        'class': 'form-control' ~ (form.description.vars.valid ? '' : ' is-invalid')
                    }
                }) }}
                {{ form_errors(form.description) }}
            </div>

            <div class="mb-3">
                {{ form_label(form.geoLocation) }}
                {{ form_widget(form.geoLocation, {
                    'attr': {
                        'class': 'form-control' ~ (form.geoLocation.vars.valid ? '' : ' is-invalid')
                    }
                }) }}
                {{ form_errors(form.geoLocation) }}
            </div>

            <div class="text-end mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Save Changes
                </button>
            </div>
            {{ form_end(form) }}
            
            <div class="mt-3">
                <a href="{{ path('app_partners_index') }}" class="btn btn-secondary">Back to List</a>
                {{ include('back/pages/partners/_delete_form.html.twig') }}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}
