<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Admin Dashboard</title>
    
    {# Favicon #}
    <link rel="icon" type="image/x-icon" href="{{asset('front/img/eco-net.png')}}">
    
    {# CSS Files #}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="{{ asset('back/css/auth.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    {% block body %}
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo mb-4">
                    <img src="{{ asset('front/img/eco_net.svg') }}" alt="eco.net" height="40">
                </div>
                <h1>Welcome Back!</h1>
                <p>Please login to your account</p>
            </div>

            {% if error %}
                <div class="alert alert-danger">
                    {{ error.messageKey|trans(error.messageData, 'security') }}
                </div>
            {% endif %}



            {{ form_start(form, {
                'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'},
                'action': path('back_auth_login')
            }) }}
                <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

                <div class="form-floating">
                    {{ form_widget(form.email, {
                        'attr': {
                            'class': 'form-control ' ~ (form.email.vars.errors|length > 0 ? 'is-invalid' : ''),
                            'novalidate': 'novalidate'
                        }
                    }) }}
                    {{ form_label(form.email) }}
                    <div class="" >
                        <span id="emailError" style="color: red;"></span>
                        {{ form_errors(form.email) }}
                    </div>
                </div>

                 <div class="form-floating">
                    {{ form_widget(form.password, {
                        'attr': {
                            'class': 'form-control ' ~ (form.password.vars.errors|length > 0 ? 'is-invalid' : ''),
                            'novalidate': 'novalidate'
                        }
                    }) }}
                    {{ form_label(form.password) }}
                    <div class="">
                    <span id="passwordError" style="color: red;"></span>
                        {{ form_errors(form.password) }}
                    </div>
                </div>

                <div class="form-check mb-3">
                    {{ form_widget(form.remember_me) }}
                    {{ form_label(form.remember_me) }}
                </div>

                <button class="auth-btn primary-btn" type="submit" onClick="return logInFun()">
                    <i class="ri-login-circle-line"></i>
                    Sign in with Email
                </button>

                <div class="auth-divider">
                    <span>or continue with</span>
                </div>

                <div class="social-auth-buttons">
                    <a href="{{ path('back_auth_oauth_github') }}" class="social-auth-btn github-btn">
                        <i class="ri-github-fill"></i>
                        GitHub
                    </a>
                    <a href="{{ path('back_auth_oauth_google') }}" class="social-auth-btn google-btn">
                        <i class="ri-google-fill"></i>
                        Google
                    </a>
                    <a href="{{ path('back_auth_detect_face_login') }}" class="social-auth-btn face-id-btn">
                        <svg id="Layer_1" style="enable-background:new 0 0 30 30;" version="1.1" viewBox="0 0 30 30" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25">
                            <path d="  M12.062,20c0.688,0.5,1.688,1,2.938,1s2.25-0.5,2.938-1" style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;"/>
                            <line style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="20" x2="20" y1="12" y2="14"/>
                            <line style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" x1="10" x2="10" y1="12" y2="14"/>
                            <path d="M15,12  v4c0,0.552-0.448,1-1,1" style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;"/>
                            <g>
                                <path d="M26,9   V6c0-1.105-0.895-2-2-2h-3" style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;"/>
                                <path d="M9,4   H6C4.895,4,4,4.895,4,6v3" style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;"/>
                                <path d="   M21,26h3c1.105,0,2-0.895,2-2v-3" style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;"/>
                                <path d="M4,21   v3c0,1.105,0.895,2,2,2h3" style="fill:none;stroke:#76b852;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;"/>
                            </g>
                        </svg>

                        Face ID
                    </a>
                </div>

                <div class="auth-footer">
                    <div class="auth-links mb-2">
                        <a href="{{ path('back_auth_forgot_password') }}" class="forgot-link">
                            <i class="ri-question-line"></i>
                            Forgot password?
                        </a>
                    </div>

                    <div class="auth-divider">
                        <span>OR</span>
                    </div>

                    <div class="auth-links">
                        Don't have an account? <a href="{{ path('back_auth_register') }}" class="signup-link">
                            <i class="ri-user-add-line"></i>
                            Sign up
                        </a>
                    </div>
                </div>
            {{ form_end(form) }}
        </div>
    </div>

    {% endblock %}

    
</body>
</html>
