/**
 * Enhanced Forum UI Functionality
 * Provides smooth animations and improved interactions for the forums page
 */

document.addEventListener('DOMContentLoaded', function() {
    // View Toggle Functionality with Enhanced Animations
    const gridViewBtn = document.querySelector('[data-view="grid"]');
    const listViewBtn = document.querySelector('[data-view="list"]');
    const viewToggle = document.querySelector('.view-toggle');
    const forumContainer = document.querySelector('.col-lg-8');
    const forumCards = document.querySelectorAll('.forum-card');

    if (gridViewBtn && listViewBtn && forumContainer) {
        // Function to apply grid view with animations
        function applyGridView() {
            // First hide all cards with a quick fade out
            forumCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'scale(0.9)';
            });

            // After a short delay, change the view mode and animate cards back in
            setTimeout(() => {
                forumContainer.classList.remove('forum-list-view');
                forumContainer.classList.add('forum-grid-view');
                viewToggle.classList.remove('list-active');
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');

                // Stagger the animation of cards appearing
                forumCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'scale(1)';
                    }, 50 * index);
                });

                localStorage.setItem('forumViewMode', 'grid');
            }, 200);
        }

        // Function to apply list view with animations
        function applyListView() {
            // First hide all cards with a quick fade out
            forumCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateX(-20px)';
            });

            // After a short delay, change the view mode and animate cards back in
            setTimeout(() => {
                forumContainer.classList.remove('forum-grid-view');
                forumContainer.classList.add('forum-list-view');
                viewToggle.classList.add('list-active');
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');

                // Stagger the animation of cards appearing
                forumCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateX(0)';
                    }, 50 * index);
                });

                localStorage.setItem('forumViewMode', 'list');
            }, 200);
        }

        // Add event listeners with enhanced animations
        gridViewBtn.addEventListener('click', applyGridView);
        listViewBtn.addEventListener('click', applyListView);

        // Load saved view preference with animations
        const savedViewMode = localStorage.getItem('forumViewMode');
        if (savedViewMode === 'list') {
            // Apply list view without animations on initial load
            forumContainer.classList.remove('forum-grid-view');
            forumContainer.classList.add('forum-list-view');
            viewToggle.classList.add('list-active');
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
        } else {
            // Apply grid view without animations on initial load
            forumContainer.classList.remove('forum-list-view');
            forumContainer.classList.add('forum-grid-view');
            viewToggle.classList.remove('list-active');
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
        }
    }

    // Enhanced Tag Filtering with Animations
    const tagSearchInput = document.getElementById('tagSearchInput');
    const tagItems = document.querySelectorAll('.tag-item');
    const tagChips = document.querySelectorAll('.tag-chip');
    const tagFilterChips = document.getElementById('tagFilterChips');

    // Function to filter tags with animations
    function filterTags(searchTerm) {
        searchTerm = searchTerm.toLowerCase().trim();

        // Filter tags in the sidebar
        tagItems.forEach(tag => {
            const tagText = tag.textContent.toLowerCase();
            if (tagText.includes(searchTerm) || searchTerm === '') {
                tag.style.display = '';
                setTimeout(() => {
                    tag.style.opacity = '1';
                    tag.style.transform = 'scale(1)';
                }, 10);
            } else {
                tag.style.opacity = '0';
                tag.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    tag.style.display = 'none';
                }, 300);
            }
        });

        // Filter tag chips in the filter bar
        if (tagFilterChips) {
            const chips = tagFilterChips.querySelectorAll('.tag-chip');
            chips.forEach(chip => {
                const chipText = chip.textContent.toLowerCase();
                if (chipText.includes(searchTerm) || searchTerm === '') {
                    chip.style.display = '';
                    setTimeout(() => {
                        chip.style.opacity = '1';
                        chip.style.transform = 'scale(1)';
                    }, 10);
                } else {
                    chip.style.opacity = '0';
                    chip.style.transform = 'scale(0.8)';
                    setTimeout(() => {
                        chip.style.display = 'none';
                    }, 300);
                }
            });
        }
    }

    // Initialize tag search functionality
    if (tagSearchInput) {
        // Add clear button to search
        const searchWrapper = tagSearchInput.parentElement;
        const clearButton = document.createElement('button');
        clearButton.className = 'btn btn-sm position-absolute end-0 top-0 mt-1 me-1';
        clearButton.innerHTML = '<i class="fas fa-times"></i>';
        clearButton.style.display = 'none';
        clearButton.style.zIndex = '5';
        clearButton.style.backgroundColor = 'transparent';
        clearButton.style.border = 'none';
        clearButton.style.color = '#999';
        searchWrapper.style.position = 'relative';
        searchWrapper.appendChild(clearButton);

        // Handle input events
        tagSearchInput.addEventListener('input', function() {
            filterTags(this.value);

            // Show/hide clear button
            if (this.value.trim() !== '') {
                clearButton.style.display = 'block';
            } else {
                clearButton.style.display = 'none';
            }
        });

        // Handle clear button click
        clearButton.addEventListener('click', function() {
            tagSearchInput.value = '';
            filterTags('');
            this.style.display = 'none';
            tagSearchInput.focus();
        });
    }

    // Add click animations to tag chips
    tagChips.forEach(chip => {
        chip.addEventListener('click', function(e) {
            // Don't prevent default - we want the link to work

            // Add click animation
            this.classList.add('tag-clicked');

            // Show loading indicator
            document.body.style.cursor = 'wait';

            // Add active class to this chip and remove from others
            const parent = this.parentElement;
            if (parent) {
                parent.querySelectorAll('.tag-chip').forEach(otherChip => {
                    otherChip.classList.remove('active');
                });
                this.classList.add('active');
            }

            // Remove the animation class after it completes
            setTimeout(() => {
                this.classList.remove('tag-clicked');
            }, 500);
        });
    });
});
