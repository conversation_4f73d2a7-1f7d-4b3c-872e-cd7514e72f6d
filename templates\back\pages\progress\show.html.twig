{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Progress Content Styles */
        .progress-content-card {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            border-left: 4px solid #0d6efd;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .progress-content-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .progress-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .progress-title i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        /* Info Card Styles */
        .info-card {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,.05);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .info-card-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .info-card-title i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Progress Bar Styles */
        .progress {
            height: 10px;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-lg {
            height: 20px;
        }

        .progress-bar {
            border-radius: 0.5rem;
        }

        .progress-label {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
        }

        .progress-value {
            color: #6c757d;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-lg {
            width: 64px;
            height: 64px;
        }

        .avatar-lg .avatar-text {
            font-size: 24px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Stats Card */
        .stats-card {
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0,0,0,.05);
            transition: all 0.3s ease;
            border: none;
            background-color: #fff;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,.1);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 24px;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
            line-height: 1.2;
        }

        .stats-label {
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Score Display */
        .score-display {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 1.5rem;
        }

        .score-value {
            font-size: 3rem;
            font-weight: 700;
            color: #198754;
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .score-label {
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Challenge Badge */
        .challenge-badge {
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 500;
            border-radius: 50rem;
            display: inline-flex;
            align-items: center;
        }

        .challenge-badge i {
            margin-right: 0.25rem;
        }

        /* Date Display */
        .date-display {
            display: flex;
            flex-direction: column;
        }

        .date-display .date {
            font-weight: 500;
        }

        .date-display .time {
            font-size: 0.75rem;
            color: #6c757d;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_progress_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Progress List">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Progress Details</h1>
                    <p class="text-muted mb-0">View and manage progress information</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_progress_edit', {'id': progress.id}) }}" class="btn btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Progress">
                        <i class="ri-edit-line me-1"></i> Edit
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- User Information -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-user-line text-primary me-2"></i> User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="avatar avatar-lg mx-auto mb-3 bg-primary-subtle">
                                {% if progress.user.getImage() %}
                                    <img
                                        src="http://localhost/img/{{ progress.user.getImage() }}"
                                        alt="Profile Image"
                                        style="width: 100%; height: 100%; object-fit: cover;"
                                    >
                                {% else %}
                                    <span class="avatar-text text-primary">{{ progress.user.getFullName()|slice(0,1)|upper }}</span>
                                {% endif %}
                            </div>
                            <h5 class="mb-1">{{ progress.user.getFullName() }}</h5>
                            <p class="text-muted mb-0">{{ progress.user.email }}</p>
                        </div>

                        <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                            <div class="info-card-title">
                                <i class="ri-trophy-line text-warning"></i> Challenge
                            </div>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('app_challenge_show', {'id': progress.challenge.id}) }}" class="challenge-badge bg-primary-subtle text-primary rounded-pill text-decoration-none">
                                    <i class="ri-trophy-line"></i> {{ progress.challenge.name }}
                                </a>
                                <a href="{{ path('app_challenge_show', {'id': progress.challenge.id}) }}" class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="ri-external-link-line me-1"></i> View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Last Updated Card -->
                <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-time-line text-info me-2"></i> Last Updated
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if progress.lastUpdated %}
                            <div class="text-center py-3">
                                <div class="date-display d-inline-block">
                                    <span class="h3 mb-0">{{ progress.lastUpdated|date('M d, Y') }}</span>
                                    <span class="h5 text-muted">{{ progress.lastUpdated|date('H:i:s') }}</span>
                                </div>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="ri-time-line" style="font-size: 48px; color: #adb5bd;"></i>
                                <p class="text-muted mt-3">Not updated yet</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Progress Details -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-bar-chart-line text-primary me-2"></i> Progress Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Score Card -->
                        <div class="row mb-4">
                            <div class="col-md-6 mb-4 mb-md-0">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                                    <div class="info-card-title">
                                        <i class="ri-award-line text-success"></i> Score
                                    </div>
                                    {% set totalQuestions = progress.challenge.quizzs|length %}
                                    {% set scorePercent = totalQuestions > 0 ? (progress.score / (totalQuestions * 10)) * 100 : 0 %}
                                    <div class="score-display">
                                        <div class="score-value">{{ progress.score }}</div>
                                        <div class="score-label">out of {{ totalQuestions * 10 }} points</div>
                                    </div>
                                    <div class="progress-label mt-3">
                                        <span>Score Percentage</span>
                                        <span class="progress-value">{{ scorePercent|round }}%</span>
                                    </div>
                                    <div class="progress progress-lg">
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: {{ scorePercent }}%;"
                                             aria-valuenow="{{ scorePercent }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                                    <div class="info-card-title">
                                        <i class="ri-question-answer-line text-info"></i> Questions Completed
                                    </div>
                                    {% set totalQuestions = progress.challenge.quizzs|length %}
                                    {% set progressPercent = totalQuestions > 0 ? (progress.progressnb / totalQuestions) * 100 : 0 %}
                                    <div class="score-display">
                                        <div class="score-value" style="color: #0dcaf0;">{{ progress.progressnb }}</div>
                                        <div class="score-label">out of {{ totalQuestions }} questions</div>
                                    </div>
                                    <div class="progress-label mt-3">
                                        <span>Completion Rate</span>
                                        <span class="progress-value">{{ progressPercent|round }}%</span>
                                    </div>
                                    <div class="progress progress-lg">
                                        <div class="progress-bar bg-info" role="progressbar"
                                             style="width: {{ progressPercent }}%;"
                                             aria-valuenow="{{ progressPercent }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Statistics -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="progress-content-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                                    <div class="progress-title">
                                        <i class="ri-bar-chart-grouped-line"></i> Progress Statistics
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="stats-icon bg-primary-subtle text-primary me-3">
                                                    <i class="ri-trophy-line"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Challenge</div>
                                                    <div class="fw-semibold">{{ progress.challenge.name }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="stats-icon bg-success-subtle text-success me-3">
                                                    <i class="ri-award-line"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Score</div>
                                                    <div class="fw-semibold">{{ progress.score }} / {{ totalQuestions * 10 }} points</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="stats-icon bg-info-subtle text-info me-3">
                                                    <i class="ri-question-answer-line"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Questions Completed</div>
                                                    <div class="fw-semibold">{{ progress.progressnb }} / {{ totalQuestions }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="stats-icon bg-warning-subtle text-warning me-3">
                                                    <i class="ri-percent-line"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Completion Rate</div>
                                                    <div class="fw-semibold">{{ progressPercent|round }}%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted d-flex align-items-center">
                                <i class="ri-information-line me-1"></i> Progress ID: {{ progress.id }}
                            </div>
                            <div>
                                <form method="post" action="{{ path('app_progress_delete', {'id': progress.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this progress entry? This action cannot be undone.');">
                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ progress.id) }}">
                                    <button class="btn btn-outline-danger"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="Delete Progress">
                                        <i class="ri-delete-bin-line me-1"></i> Delete Progress
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
