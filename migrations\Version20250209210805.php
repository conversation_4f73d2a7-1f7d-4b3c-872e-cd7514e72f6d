<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250209210805 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE comments ADD postid_id INT NOT NULL');
        $this->addSql('ALTER TABLE comments ADD CONSTRAINT FK_5F9E962AEB348947 FOREIGN KEY (postid_id) REFERENCES forums (id)');
        $this->addSql('CREATE INDEX IDX_5F9E962AEB348947 ON comments (postid_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE comments DROP FOREIGN KEY FK_5F9E962AEB348947');
        $this->addSql('DROP INDEX IDX_5F9E962AEB348947 ON comments');
        $this->addSql('ALTER TABLE comments DROP postid_id');
    }
}
