<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class AppExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('unique', [$this, 'uniqueFilter']),
        ];
    }

    /**
     * Filter that returns unique elements from an array or unique characters from a string
     *
     * @param mixed $value The input array or string
     * @return mixed The filtered array or string with unique elements
     */
    public function uniqueFilter($value)
    {
        // If it's an array, return unique values
        if (is_array($value)) {
            return array_unique($value);
        }
        
        // If it's a string, return unique characters
        if (is_string($value)) {
            return implode('', array_unique(str_split($value)));
        }
        
        // For other types, return as is
        return $value;
    }
}
