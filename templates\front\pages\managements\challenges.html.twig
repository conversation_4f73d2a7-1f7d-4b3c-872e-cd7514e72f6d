{% extends 'front/base.html.twig' %}

{% block title %}Challenges - eco.net{% endblock %}

{% block content %}
    <!-- Navbar & Hero Start -->
    <div class="container-fluid position-relative p-0">
        {% include 'front/includes/navbar.html.twig' %}

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb-challenges">
            <div class="container text-center py-5" style="max-width: 900px">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                    Challenges
                </h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('app_home')}}">Home</a>
                    </li>
                    <li class="breadcrumb-item active text-white-50">Pages</li>
                    <li class="breadcrumb-item active text-primary">Challenges</li>
                </ol>
            </div>
        </div>
        <!-- Header End -->
    </div>
    <!-- Navbar & Hero End -->

    <!-- Challenges Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Flash Messages -->
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show mb-4" role="alert">
                        <div class="d-flex align-items-center">
                            {% if label == 'error' or label == 'danger' %}
                                <i class="fas fa-exclamation-circle me-2"></i>
                            {% elseif label == 'warning' %}
                                <i class="fas fa-exclamation-triangle me-2"></i>
                            {% elseif label == 'success' %}
                                <i class="fas fa-check-circle me-2"></i>
                            {% else %}
                                <i class="fas fa-info-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endfor %}

            <!-- Filter Tabs Start -->
            <div class="challenge-filter mb-5 wow fadeInUp" data-wow-delay="0.1s">
                <ul class="nav nav-pills d-inline-flex justify-content-center border-bottom mb-4">
                    <li class="nav-item">
                        <a class="nav-link {% if currentFilter == 'all' %}active{% endif %}"
                           data-filter="all"
                           href="{{ path('app_challenges_list', {'filter': 'all'}) }}">All</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if currentFilter == 'done' %}active{% endif %}"
                           data-filter="done"
                           href="{{ path('app_challenges_list', {'filter': 'done'}) }}">Done</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if currentFilter == 'in-progress' %}active{% endif %}"
                           data-filter="in-progress"
                           href="{{ path('app_challenges_list', {'filter': 'in-progress'}) }}">In Progress</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if currentFilter == 'todo' %}active{% endif %}"
                           data-filter="todo"
                           href="{{ path('app_challenges_list', {'filter': 'todo'}) }}">ToDo</a>
                    </li>
                </ul>
            </div>
            <!-- Filter Tabs End -->

            <div class="row g-4">
                {% for challenge in pagination %}
                    {% set progress = challengeProgress[challenge.id].progress %}
                    {% set totalQuestions = challengeProgress[challenge.id].totalQuestions %}
                    {% set challengeStatus = challengeStatuses[challenge.id] %}

                    <div class="col-lg-4 col-md-6 wow fadeInUp challenge-item {{ challengeStatus }}" data-wow-delay="{{ loop.index0 * 0.2 }}s">
                        <div class="challenge-card">
                            <div class="challenge-image">
                                <img src="http://localhost/{{ challenge.image }}" alt="{{ challenge.name }}" class="img-fluid">
                                {% if progress %}
                                    {% set correctAnswers = progress.score / 10 %}
                                    {% set percentage = (correctAnswers / totalQuestions * 100)|round %}
                                    <div class="progress-overlay">
                                        <div class="progress-circle"
                                             style="background: conic-gradient(
                                                {% if percentage >= 70 %}var(--bs-success)
                                                {% elseif percentage >= 40 %}var(--bs-warning)
                                                {% else %}var(--bs-danger){% endif %}
                                                {{ percentage }}%,
                                                rgba(255,255,255,0.2) 0
                                             )">
                                            <span>{{ percentage }}%</span>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>

                            <div class="challenge-content">
                                <h3 class="challenge-title">{{ challenge.name }}</h3>
                                <p class="challenge-description">{{ challenge.description|length > 100 ? challenge.description|slice(0, 100) ~ '...' : challenge.description }}</p>

                                <div class="challenge-info">
                                    <div class="info-item">
                                        <i class="far fa-clock text-primary"></i>
                                        <span>{{ challenge.duration }} min</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="far fa-calendar-alt text-primary"></i>
                                        <span>Until {{ challenge.end|date('d M Y') }}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="far fa-question-circle text-primary"></i>
                                        <span>{{ totalQuestions }} Questions</span>
                                    </div>
                                </div>

                                {% if progress %}
                                    {% set correctAnswers = progress.score / 10 %}
                                    {% set percentage = (correctAnswers / totalQuestions * 100)|round %}
                                    <div class="progress-info mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span>Progress</span>
                                            <span>{{ correctAnswers|round }}/{{ totalQuestions }}</span>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar {% if percentage >= 70 %}bg-success{% elseif percentage >= 40 %}bg-warning{% else %}bg-danger{% endif %}"
                                                 role="progressbar"
                                                 style="width: {{ percentage }}%"
                                                 aria-valuenow="{{ percentage }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="no-progress mb-3">
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-info-circle me-1"></i>
                                            You haven't started this challenge yet
                                        </p>
                                    </div>
                                {% endif %}

                                <div class="challenge-actions d-flex gap-2">
                                    {% if totalQuestions == 0 %}
                                        <button type="button"
                                               class="btn btn-primary w-100 no-questions-btn"
                                               data-challenge-name="{{ challenge.getName() }}"
                                               data-bs-toggle="modal"
                                               data-bs-target="#noQuestionsModal">
                                            <i class="fas fa-play me-2"></i>Start Challenge
                                        </button>
                                    {% elseif progress and progress.progressnb > 0 and progress.progressnb < totalQuestions %}
                                        <a href="{{ path('app_challenge_take_quiz', {'id': challenge.id}) }}"
                                           class="btn btn-primary flex-grow-1">
                                            <i class="fas fa-play me-2"></i>Continue Challenge
                                        </a>
                                    {% elseif progress and progress.progressnb >= totalQuestions %}
                                        <a href="{{ path('app_challenge_take_quiz', {'id': challenge.id, 'restart': true}) }}"
                                           class="btn btn-primary flex-grow-1">
                                            <i class="fas fa-redo me-2"></i>Retry Challenge
                                        </a>
                                    {% else %}
                                        <a href="{{ path('app_challenge_take_quiz', {'id': challenge.id}) }}"
                                           class="btn btn-primary w-100">
                                            <i class="fas fa-play me-2"></i>Start Challenge
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <div class="d-flex justify-content-center mt-4">
                <div class="pagination-container">
                    {{ knp_pagination_render(pagination, null, {}, {
                        'filter': currentFilter
                    }) }}
                </div>
            </div>

            <style>
                .pagination-container {
                    margin: 20px 0;
                }

                .pagination {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 5px;
                }

                .pagination span {
                    display: inline-block;
                }

                .pagination .current {
                    background-color: var(--bs-primary);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: 500;
                }

                .pagination .page a,
                .pagination .next a,
                .pagination .last a,
                .pagination .first a,
                .pagination .previous a {
                    display: inline-block;
                    padding: 8px 16px;
                    background-color: #f8f9fa;
                    color: var(--bs-dark);
                    border-radius: 4px;
                    text-decoration: none;
                    transition: all 0.3s;
                }

                .pagination .page a:hover,
                .pagination .next a:hover,
                .pagination .last a:hover,
                .pagination .first a:hover,
                .pagination .previous a:hover {
                    background-color: rgba(var(--bs-primary-rgb), 0.1);
                    color: var(--bs-primary);
                }
            </style>

        </div>
    </div>
    <!-- Challenges End -->

    <style>
        .challenge-card {
            background: #fff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .challenge-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .challenge-image {
            position: relative;
            padding-top: 60%;
            overflow: hidden;
        }

        .challenge-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .progress-overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.5);
            border-radius: 50%;
            padding: 5px;
        }

        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 0.9rem;
        }

        .challenge-content {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .challenge-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--bs-dark);
        }

        .challenge-description {
            color: var(--bs-gray-600);
            margin-bottom: 1rem;
            flex-grow: 1;
        }

        .challenge-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--bs-gray-600);
        }

        .info-item i {
            font-size: 1rem;
        }

        .progress {
            background-color: rgba(var(--bs-primary-rgb), 0.1);
        }

        .no-progress {
            padding: 0.5rem;
            background-color: rgba(var(--bs-primary-rgb), 0.05);
            border-radius: 8px;
        }

        .challenge-actions {
            margin-top: auto;
        }

        /* Challenge Filter Styles */
        .challenge-filter .nav-pills .nav-link {
            color: var(--bs-gray-600);
            background: transparent;
            border-radius: 0;
            padding: 10px 20px;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .challenge-filter .nav-pills .nav-link.active,
        .challenge-filter .nav-pills .nav-link:hover {
            color: var(--bs-primary);
            background: transparent;
            border-bottom-color: var(--bs-primary);
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // No need for client-side filtering anymore as we're using server-side filtering
            // The links now navigate to the appropriate filtered URL

            // Add animation effect when filter links are clicked
            const filterLinks = document.querySelectorAll('.challenge-filter .nav-link');

            filterLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // We don't prevent default here to allow normal navigation

                    // Add a loading indicator or animation if desired
                    document.body.style.cursor = 'wait';

                    // Optional: Add a fade effect to the challenge items
                    const challengeItems = document.querySelectorAll('.challenge-item');
                    challengeItems.forEach(item => {
                        item.style.opacity = '0.5';
                        item.style.transition = 'opacity 0.3s';
                    });
                });
            });
        });
    </script>

    <!-- No Questions Modal -->
    <div class="modal fade" id="noQuestionsModal" tabindex="-1" aria-labelledby="noQuestionsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="noQuestionsModalLabel">
                        <i class="fas fa-info-circle me-2"></i>Challenge Not Available
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    </div>
                    <p class="mb-0">
                        The challenge "<span id="challengeNamePlaceholder"></span>" currently has no questions available.
                    </p>
                    <p class="mt-2">
                        Please check back later when the challenge has been updated with questions.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update modal with challenge name when opened
        document.addEventListener('DOMContentLoaded', function() {
            const noQuestionsButtons = document.querySelectorAll('.no-questions-btn');

            noQuestionsButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const challengeName = this.getAttribute('data-challenge-name');
                    document.getElementById('challengeNamePlaceholder').textContent = challengeName;
                });
            });
        });
    </script>
{% endblock %}