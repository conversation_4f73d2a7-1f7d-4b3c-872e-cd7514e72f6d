{% extends 'back/base.html.twig' %}

{% block title %}Registrations for {{ event.title }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Event Details Styles */
        .event-detail-card {
            background-color: #fff;
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .event-detail-item {
            padding: 1.25rem;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
            height: 100%;
            transition: all 0.3s ease;
        }

        .event-detail-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .event-detail-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .event-detail-label i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .event-detail-value {
            font-size: 1.25rem;
            font-weight: 500;
            color: #212529;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 40px;
            height: 40px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="page-header animate__animated animate__fadeIn">
        <div class="row align-items-center mb-4">
            <div class="col-auto">
                <a href="{{ path('app_admin_event_registration_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to All Registrations">
                    <i class="ri-arrow-left-line"></i>
                </a>
            </div>
            <div class="col">
                <h1 class="h3 mb-0 text-gray-800">Registrations for "{{ event.title }}"</h1>
                <p class="text-muted mb-0">View and manage registrations for this event</p>
            </div>
        </div>
    </div>

    <!-- Event Details Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="event-detail-item animate__animated animate__fadeIn">
                <div class="event-detail-label">
                    <i class="ri-calendar-line text-primary"></i> Date & Time
                </div>
                <div class="event-detail-value">
                    {{ event.date ? event.date|date('M d, Y') : 'Not set' }}
                </div>
                <div class="text-muted small">
                    {{ event.date ? event.date|date('H:i') : '' }}
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="event-detail-item animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                <div class="event-detail-label">
                    <i class="ri-map-pin-line text-danger"></i> Location
                </div>
                <div class="event-detail-value">
                    {{ event.location ?: 'Not set' }}
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="event-detail-item animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                <div class="event-detail-label">
                    <i class="ri-group-line text-success"></i> Max Participants
                </div>
                <div class="event-detail-value">
                    {{ event.maxParticipants ?: 'Unlimited' }}
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="event-detail-item animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                <div class="event-detail-label">
                    <i class="ri-user-follow-line text-info"></i> Current Registrations
                </div>
                <div class="event-detail-value">
                    {{ event.eventRegistrations|length }}
                </div>
                <div class="progress mt-2" style="height: 6px;">
                    {% set percentage = event.maxParticipants ? (event.eventRegistrations|length / event.maxParticipants * 100)|round : 0 %}
                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ percentage }}%;" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="text-muted small mt-1">
                    {% if event.maxParticipants %}
                        {{ percentage }}% capacity filled
                    {% else %}
                        No capacity limit
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Registration List Card -->
    <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0 fw-bold">Registration List</h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" id="registrationSearch" class="form-control" placeholder="Search users...">
                        <span class="input-group-text bg-primary text-white">
                            <i class="ri-search-line"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle border-0" id="registrationsTable">
                    <thead class="table-light">
                        <tr>
                            <th>User</th>
                            <th>Status</th>
                            <th>Registration Date</th>
                            {# <th class="text-end">Actions</th> #}
                        </tr>
                    </thead>
                    <tbody>
                        {% for registration in registrations %}
                            <tr class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2 bg-success-subtle rounded-circle">
                                            <span class="avatar-text text-success">{{ registration.userFullName|slice(0,1)|upper }}</span>
                                        </div>
                                        {{ registration.userFullName }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ registration.status == 'registered' ? 'success' : 'secondary' }} rounded-pill">
                                        <i class="ri-{{ registration.status == 'registered' ? 'check-line' : 'time-line' }} me-1"></i>
                                        {{ registration.status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-calendar-line text-muted me-2"></i>
                                        <div>
                                            <div>{{ registration.registrationDate|date('M d, Y') }}</div>
                                            <small class="text-muted">{{ registration.registrationDate|date('H:i') }}</small>
                                        </div>
                                    </div>
                                </td>
                                {# <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Status">
                                            <i class="ri-pencil-line"></i>
                                        </button>
                                    </div>
                                </td> #}
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="3" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-user-search-line empty-state-icon"></i>
                                        <h5>No registrations found</h5>
                                        <p class="text-muted">There are no registrations for this event yet</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Registration search functionality
        const searchInput = document.getElementById('registrationSearch');
        if (searchInput) {
            const table = document.getElementById('registrationsTable');
            const rows = table.querySelectorAll('tbody tr');

            searchInput.addEventListener('keyup', function() {
                const searchTerm = searchInput.value.toLowerCase();

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        }

        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.cursor = 'pointer';
            });
        });
    });
</script>
{% endblock %}