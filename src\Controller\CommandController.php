<?php

namespace App\Controller;

use App\Entity\Command;
use App\Entity\Product;
use App\Entity\User;
use App\Form\CommandType;
use App\Repository\CommandRepository;
use App\Repository\ProductRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/command')]
final class CommandController extends AbstractController
{
    #[Route(name: 'app_command_index', methods: ['GET'])]
    public function index(Request $request, CommandRepository $commandRepository, UserRepository $userRepository): Response
    {
        // Get search parameter from request
        $search = $request->query->get('search');

        // Get all commands
        $commands = $commandRepository->findAll();

        // Create an array to store user information
        $userNames = [];

        // Fetch user information for each command
        foreach ($commands as $command) {
            $userId = $command->getIdUser();
            if (!isset($userNames[$userId])) {
                $user = $userRepository->find($userId);
                $userNames[$userId] = $user ? $user->getFullName() : 'Unknown User';
            }
        }

        // Filter commands if search parameter is provided
        if ($search) {
            $filteredCommands = [];
            foreach ($commands as $command) {
                // Search in delivery address or notes
                if (
                    stripos($command->getDeliveryAddress(), $search) !== false ||
                    stripos($command->getNotes(), $search) !== false ||
                    stripos($userNames[$command->getIdUser()], $search) !== false
                ) {
                    $filteredCommands[] = $command;
                }
            }
            $commands = $filteredCommands;
        }

        return $this->render('command/index.html.twig', [
            'commands' => $commands,
            'userNames' => $userNames,
            'search' => $search,
        ]);
    }

    #[Route('/from-cart', name: 'app_command_from_cart', methods: ['POST'])]
    public function fromCart(
        Request $request,
        EntityManagerInterface $entityManager,
        ProductRepository $productRepository
    ): Response {
        $command = new Command();

        $command->setIdUser($this->getUser()->getId());
        $command->setCreateAt(new \DateTime());
        $command->setStatus('pending');

        $totalAmount = $request->request->get('totalAmount');
        $deliveryAddress = $request->request->get('deliveryAddress');
        $notes = $request->request->get('notes');

        $command->setTotalAmount($totalAmount);
        $command->setDeliveryAddress($deliveryAddress);
        $command->setNotes($notes);

        // Decode the products JSON
        $productsJson = $request->request->get('products');
        $productsData = json_decode($productsJson, true);

        if ($productsData) {
            foreach ($productsData as $prodData) {
                // Fetch the product entity by id
                $productEntity = $productRepository->find($prodData['id']);
                if ($productEntity) {
                    $command->addProduct($productEntity);
                    $currentStock=(int)$productEntity->getStock();
                    $newStock=$currentStock-1;
                    if($newStock<0){
                        $this->addFlas('error','Not enough stock for product: '.$productEntity->getNomP());
                        return $this->redirectToRoute('app_front_products');
                    }
                    $productEntity->setStock($newStock);
                    $entityManager->persist($productEntity);
                }
            }
        }

        $entityManager->persist($command);
        $entityManager->flush();

        $this->addFlash('success', 'Command added successfully!');
        return $this->redirectToRoute('app_front_products');
    }

    #[Route('/new', name: 'app_command_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $command = new Command();
        $form = $this->createForm(CommandType::class, $command);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($command);
            $entityManager->flush();

            return $this->redirectToRoute('app_command_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('command/new.html.twig', [
            'command' => $command,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_command_show', methods: ['GET'])]
    public function show(Command $command, UserRepository $userRepository): Response
    {
        // Get user information
        $user = $userRepository->find($command->getIdUser());
        $userName = $user ? $user->getFullName() : 'Unknown User';

        return $this->render('command/show.html.twig', [
            'command' => $command,
            'userName' => $userName,
        ]);
    }


    #[Route('/{id}/update-status', name: 'app_command_update_status', methods: ['POST'])]
    public function updateStatus(Request $request, Command $command, EntityManagerInterface $entityManager): Response
    {
        // Get the submitted status directly from the request
        $status = $request->request->get('status');
        if ($status !== null) {
            $command->setStatus($status);
            $entityManager->flush();
            $this->addFlash('success', 'Status updated successfully.');
        }
        return $this->redirectToRoute('app_command_index');
    }
    #[Route('/{id}/update-status2', name: 'app_command_update_status2', methods: ['POST'])]
    public function updateStatus2(Request $request, Command $command, EntityManagerInterface $entityManager): Response
    {
        // Get the submitted status directly from the request
        $status = $request->request->get('status');
        if ($status !== null) {
            $command->setStatus($status);
            $entityManager->flush();
            $this->addFlash('success', 'Status updated successfully.');
        }
        return $this->redirectToRoute('app_command_by_user',['userId'=>$this->getUser()->getId()]);
    }

    #[Route('/front/user/{userId}', name: 'app_command_by_user', methods: ['GET'])]
    public function listByUser(int $userId, CommandRepository $commandRepository): Response
    {
        $commands = $commandRepository->findBy(['id_user' => $userId]);
        $username=$this->getUser()->getFullName();
        return $this->render('front/front_command.html.twig', [
            'commands' => $commands,
            'userId'   => $userId,
            'username' => $username
        ]);
    }
    // (Keep your existing edit route if needed for a full form edit)
    #[Route('/{id}/edit', name: 'app_command_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Command $command, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(CommandType::class, $command);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();
            return $this->redirectToRoute('app_command_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('command/edit.html.twig', [
            'command' => $command,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_command_delete', methods: ['POST'])]
    public function delete(Request $request, Command $command, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete' . $command->getId(), $request->get('_token'))) {
            $entityManager->remove($command);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_command_index', [], Response::HTTP_SEE_OTHER);
    }
}
