{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Stats Card Styles */
        .stats-card {
            border-radius: 0.75rem;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Quiz Item Styles */
        .quiz-item {
            transition: all 0.3s ease;
        }

        .quiz-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .quiz-question {
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Choice Item Styles */
        .choice-item {
            display: flex;
            align-items: center;
            padding: 0.35rem 0.5rem;
            border-radius: 0.5rem;
            margin-bottom: 0.35rem;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
        }

        .choice-item:hover {
            background-color: #e9ecef;
        }

        .choice-item.correct {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .choice-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #0d6efd;
            color: #fff;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .choice-text {
            font-size: 0.875rem;
            color: #495057;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Quiz Management</h1>
                    <p class="text-muted mb-0">Manage and organize all quiz questions</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_quizz_new') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i> New Question
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Summary Cards -->
        <div class="row mb-4">
            <!-- Total Questions Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                                <i class="ri-question-line text-primary fs-4"></i>
                            </div>
                            <div>                                
                                <h6 class="mb-0 text-muted">Total Questions</h6>
                                <h3 class="mb-0">{{ quizzs|length }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions by Challenge -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-subtle text-success me-3">
                                <i class="ri-trophy-line"></i>
                            </div>
                            <div>
                                {% set challengeCount = 0 %}
                                {% set uniqueChallenges = [] %}
                                {% for quizz in quizzs %}
                                    {% if quizz.challenge and quizz.challenge.id not in uniqueChallenges %}
                                        {% set uniqueChallenges = uniqueChallenges|merge([quizz.challenge.id]) %}
                                        {% set challengeCount = challengeCount + 1 %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Challenges</h6>
                                <h3 class="mb-0">{{ challengeCount }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions with Choice 1 as Answer -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-info-subtle text-info me-3">
                                <i class="ri-checkbox-circle-line"></i>
                            </div>
                            <div>
                                {% set answerDistribution = {1: 0, 2: 0, 3: 0, 4: 0} %}
                                {% for quizz in quizzs %}
                                    {% if quizz.answer >= 1 and quizz.answer <= 4 %}
                                        {% set answerDistribution = answerDistribution|merge({(quizz.answer): answerDistribution[quizz.answer] + 1}) %}
                                    {% endif %}
                                {% endfor %}
                                {% set mostCommonAnswer = 1 %}
                                {% set maxCount = 0 %}
                                {% for answer, count in answerDistribution %}
                                    {% if count > maxCount %}
                                        {% set maxCount = count %}
                                        {% set mostCommonAnswer = answer %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Most Common Answer</h6>
                                <h3 class="mb-0">{{ mostCommonAnswer }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Choices Length -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning-subtle text-warning me-3">
                                <i class="ri-text-spacing"></i>
                            </div>
                            <div>
                                {% set totalQuestionLength = 0 %}
                                {% for quizz in quizzs %}
                                    {% set totalQuestionLength = totalQuestionLength + quizz.question|length %}
                                {% endfor %}
                                {% set avgQuestionLength = quizzs|length > 0 ? (totalQuestionLength / quizzs|length)|round : 0 %}
                                <h6 class="mb-0 text-muted">Avg Question Length</h6>
                                <h3 class="mb-0">{{ avgQuestionLength }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Questions List Card -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Questions List</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" id="quizz-search" class="form-control" placeholder="Search questions...">
                            <span class="input-group-text bg-primary text-white">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle border-0" id="quizzTable">
                        <thead class="table-light">
                            <tr>
                                <th>Question</th>
                                <th>Choices</th>
                                <th>Answer</th>
                                <th>Challenge</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for quizz in quizzs %}
                            <tr class="align-middle quiz-item">
                                <td>
                                    <div class="quiz-question">{{ quizz.question }}</div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="choice-item {% if quizz.answer == 1 %}correct{% endif %}">
                                            <div class="choice-number">1</div>
                                            <div class="choice-text">{{ quizz.choice1 }}</div>
                                        </div>
                                        <div class="choice-item {% if quizz.answer == 2 %}correct{% endif %}">
                                            <div class="choice-number">2</div>
                                            <div class="choice-text">{{ quizz.choice2 }}</div>
                                        </div>
                                        <div class="choice-item {% if quizz.answer == 3 %}correct{% endif %}">
                                            <div class="choice-number">3</div>
                                            <div class="choice-text">{{ quizz.choice3 }}</div>
                                        </div>
                                        <div class="choice-item {% if quizz.answer == 4 %}correct{% endif %}">
                                            <div class="choice-number">4</div>
                                            <div class="choice-text">{{ quizz.choice4 }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-success-subtle text-success rounded-pill px-3 py-2">
                                        <i class="ri-checkbox-circle-line me-1"></i> Choice {{ quizz.answer }}
                                    </span>
                                </td>
                                <td>
                                    {% if quizz.challenge %}
                                        <a href="{{ path('app_challenge_show', {'id': quizz.challenge.id}) }}" class="badge bg-primary-subtle text-primary rounded-pill px-3 py-2 text-decoration-none">
                                            <i class="ri-trophy-line me-1"></i> {{ quizz.challenge.name }}
                                        </a>
                                    {% else %}
                                        <span class="badge bg-secondary-subtle text-secondary rounded-pill px-3 py-2">
                                            <i class="ri-question-line me-1"></i> No Challenge
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_quizz_show', {'id': quizz.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="View Details">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <a href="{{ path('app_quizz_edit', {'id': quizz.id}) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="Edit Question">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <form method="post" action="{{ path('app_quizz_delete', {'id': quizz.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this question? This action cannot be undone.');">
                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ quizz.id) }}">
                                            <button class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete Question">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-question-line empty-state-icon"></i>
                                        <h5>No quiz questions found</h5>
                                        <p class="text-muted">There are no quiz questions yet</p>
                                        <a href="{{ path('app_quizz_new') }}" class="btn btn-primary mt-3">
                                            <i class="ri-add-line me-1"></i> Create New Question
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Search functionality
            const searchInput = document.getElementById('quizz-search');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    const searchValue = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('#quizzTable tbody tr');

                    tableRows.forEach(function(row) {
                        const questionElement = row.querySelector('.quiz-question');
                        const choiceElements = row.querySelectorAll('.choice-text');

                        if (questionElement) {
                            const questionText = questionElement.textContent.toLowerCase();
                            let choicesText = '';

                            choiceElements.forEach(function(choice) {
                                choicesText += ' ' + choice.textContent.toLowerCase();
                            });

                            const textToSearch = questionText + choicesText;

                            if (textToSearch.includes(searchValue)) {
                                row.style.display = '';
                            } else {
                                row.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('#quizzTable tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });

                // Make the entire row clickable to view question details
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons
                    if (e.target.closest('.btn') || e.target.closest('form')) {
                        return;
                    }

                    const viewLink = this.querySelector('a[title="View Details"]');
                    if (viewLink) {
                        viewLink.click();
                    }
                });
            });

            // Add animation to stats cards
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
{% endblock %}
