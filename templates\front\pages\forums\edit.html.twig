{% extends 'front/base.html.twig' %}

{% block content %}

<div class="container-fluid position-relative p-0">
    
    {% include 'front/includes/navbar.html.twig' %}

    <!-- Header Start -->
    <div class="container-fluid bg-breadcrumb-ptps">
        <div class="container text-center py-5" style="max-width: 900px">
            <h1 class="text-white display-3 mb-4 wow fadeInDown" data-wow-delay="0.1s">Edit Forums</h1>
            <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                <li class="breadcrumb-item">
                    <a class="text-white" href="{{path('app_home')}}">Home</a>
                </li>
                <li class="breadcrumb-item active text-white-50">Pages</li>
                <li class="breadcrumb-item active text-primary">Edit Forums</li>
            </ol>
        </div>
    </div>
    <!-- Header End -->
</div>

{{ include('front/pages/forums/_form.html.twig', {'button_label': 'Update Forum'}) }}


{% endblock %}

{# {% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h1 class="h3 mb-0">Edit Forum</h1>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ path('front_forums_edit', {'id': forum.id}) }}">
                        <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ forum.id) }}">
                        
                        <div class="mb-3">
                            <label for="forums_title" class="form-label">Title</label>
                            <input type="text" id="forums_title" name="forums[title]" class="form-control" value="{{ forum.title }}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="forums_content" class="form-label">Content</label>
                            <textarea id="forums_content" name="forums[content]" class="form-control" rows="5" required>{{ forum.content }}</textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ path('front_forums_index') }}" class="btn btn-secondary">Back to list</a>
                            <button type="submit" class="btn btn-primary">Save changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} #}