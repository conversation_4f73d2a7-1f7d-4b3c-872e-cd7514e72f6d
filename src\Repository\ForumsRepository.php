<?php

namespace App\Repository;

use App\Entity\Forums;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Forums>
 */
class ForumsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Forums::class);
    }

    /**
     * Get forums query with optional user filter
     */
    public function getForumsQuery(?int $userId = null): Query
    {
        $qb = $this->createQueryBuilder('f')
            ->orderBy('f.createdAt', 'DESC');

        if ($userId) {
            $qb->andWhere('f.user = :userId')
               ->setParameter('userId', $userId);
        }

        return $qb->getQuery();
    }

    /**
     * Get top contributors (users with most forum posts)
     */
    public function getTopContributors(int $limit = 3): array
    {
        $entityManager = $this->getEntityManager();
        $qb = $entityManager->createQueryBuilder();

        return $qb->select('u.id, u.full_name, u.image, COUNT(f.id) as postCount')
            ->from(User::class, 'u')
            ->join('u.forums', 'f')
            ->groupBy('u.id')
            ->orderBy('postCount', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find forums by tag
     */
    public function findByTag(string $tag): array
    {
        return $this->createQueryBuilder('f')
            ->where('f.content LIKE :tag')
            ->setParameter('tag', '%#' . $tag . '%')
            ->orderBy('f.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
