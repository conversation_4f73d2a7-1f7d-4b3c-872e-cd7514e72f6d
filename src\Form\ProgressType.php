<?php

namespace App\Form;

use App\Entity\Challenge;
use App\Entity\Progress;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Range;

class ProgressType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('score', NumberType::class, [
                'label' => 'Score',
                'attr' => [
                    'class' => 'form-control',
                    'min' => 0,
                    'max' => 100,
                    'placeholder' => 'Enter score (0-100)',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a score']),
                    new Range([
                        'min' => 0,
                        'max' => 100,
                        'notInRangeMessage' => 'Score must be between {{ min }} and {{ max }}',
                    ]),
                ],
                'empty_data' => false,
            ])
            ->add('progressnb', NumberType::class, [
                'label' => 'Progress Number',
                'attr' => [
                    'class' => 'form-control',
                    'min' => 0,
                    'placeholder' => 'Enter progress number',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a progress number']),
                    new Range([
                        'min' => 0,
                        'notInRangeMessage' => 'Progress number must be at least {{ min }}',
                    ]),
                ],
                'empty_data' => false,
            ])
            ->add('lastUpdated', DateTimeType::class, [
                'label' => 'Last Updated',
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'form-control',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select a date']),
                ],
                'empty_data' => false,
            ])
            ->add('user', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'full_name',
                'placeholder' => 'Select a user',
                'attr' => [
                    'class' => 'form-select',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select a user']),
                ],
                'empty_data' => false,
            ])
            ->add('challenge', EntityType::class, [
                'class' => Challenge::class,
                'choice_label' => 'name',
                'placeholder' => 'Select a challenge',
                'attr' => [
                    'class' => 'form-select',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select a challenge']),
                ],
                'empty_data' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Progress::class,
            'attr' => [
                'class' => 'needs-validation',
                'novalidate' => true,
            ],
        ]);
    }
}
