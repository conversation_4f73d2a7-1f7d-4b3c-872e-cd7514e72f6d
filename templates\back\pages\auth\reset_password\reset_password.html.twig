<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Admin Dashboard</title>
    
    {# Favicon #}
    <link rel="icon" type="image/x-icon" href="{{ asset('front/img/eco-net.png') }}">

    {# CSS Files #}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="{{ asset('back/css/auth.css') }}" rel="stylesheet">
</head>
<body>
    {% block body %}
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo mb-4">
                    <img src="{{ asset('front/img/eco_net.svg') }}" alt="eco.net" height="40">
                </div>
                <h1>Reset Password</h1>
                <p>Enter your new password below</p>
            </div>

            {% for flash_error in app.flashes('error') %}
                <div class="alert alert-danger" role="alert">{{ flash_error }}</div>
            {% endfor %}

            {% for flash_success in app.flashes('success') %}
                <div class="alert alert-success" role="alert">{{ flash_success }}</div>
            {% endfor %}

            {{ form_start(resetForm) }}
                <div class="form-floating mb-4">
                    {{ form_widget(resetForm.password.first, {
                        'attr': {
                            'class': 'form-control',
                            'placeholder': 'New Password'
                        }
                    }) }}
                    {{ form_label(resetForm.password.first) }}
                </div>

                <div class="form-floating mb-4">
                    {{ form_widget(resetForm.password.second, {
                        'attr': {
                            'class': 'form-control',
                            'placeholder': 'Confirm New Password'
                        }
                    }) }}
                    {{ form_label(resetForm.password.second) }}
                </div>

                <button class="auth-btn primary-btn" type="submit">
                    <i class="ri-lock-unlock-line"></i>
                    Reset Password
                </button>

                <div class="auth-footer">
                    <div class="auth-links mt-4">
                        <a href="{{ path('back_auth_login') }}" class="back-link">
                            <i class="ri-arrow-left-line"></i>
                            Back to Sign in
                        </a>
                    </div>
                </div>
            {{ form_end(resetForm) }}
        </div>
    </div>
    {% endblock %}

    {# JavaScript Files #}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
