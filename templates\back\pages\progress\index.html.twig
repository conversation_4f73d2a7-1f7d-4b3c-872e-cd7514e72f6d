{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Stats Card Styles */
        .stats-card {
            border-radius: 0.75rem;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 36px;
            height: 36px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Progress Item Styles */
        .progress-item {
            transition: all 0.3s ease;
        }

        .progress-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        /* Progress Bar Styles */
        .progress {
            height: 10px;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 0.5rem;
        }

        .progress-label {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
        }

        .progress-value {
            color: #6c757d;
        }

        /* Challenge Badge */
        .challenge-badge {
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 500;
            border-radius: 50rem;
            display: inline-flex;
            align-items: center;
        }

        .challenge-badge i {
            margin-right: 0.25rem;
        }

        /* Date Display */
        .date-display {
            display: flex;
            flex-direction: column;
        }

        .date-display .date {
            font-weight: 500;
        }

        .date-display .time {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Progress Tracking</h1>
                    <p class="text-muted mb-0">Monitor and manage user progress on challenges</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_progress_new') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i> New Progress Entry
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Summary Cards -->
        <div class="row mb-4">
            <!-- Total Progress Entries Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                                <i class="ri-bar-chart-line text-primary fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 text-muted">Total Entries</h6>
                                <h3 class="mb-0">{{ progress|length }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Users Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-danger-subtle rounded-3 p-3 me-3">
                                <i class="ri-user-line text-danger fs-4"></i>
                            </div>
                            <div>
                                {% set uniqueUsers = [] %}
                                {% for entry in progress %}
                                    {% if entry.user.id not in uniqueUsers %}
                                        {% set uniqueUsers = uniqueUsers|merge([entry.user.id]) %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Active Users</h6>
                                <h3 class="mb-0">{{ uniqueUsers|length }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Challenges in Progress Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                                <i class="ri-trophy-line text-success fs-4"></i>
                            </div>
                            <div>
                                {% set uniqueChallenges = [] %}
                                {% for entry in progress %}
                                    {% if entry.challenge.id not in uniqueChallenges %}
                                        {% set uniqueChallenges = uniqueChallenges|merge([entry.challenge.id]) %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Challenges</h6>
                                <h3 class="mb-0">{{ uniqueChallenges|length }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Completion Rate Card -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
                                <i class="ri-percent-line text-warning fs-4"></i>
                            </div>
                            <div>
                                {% set totalCompletionRate = 0 %}
                                {% set entryCount = 0 %}
                                {% for entry in progress %}
                                    {% set totalQuestions = entry.challenge.quizzs|length %}
                                    {% if totalQuestions > 0 %}
                                        {% set completionRate = (entry.progressnb / totalQuestions) * 100 %}
                                        {% set totalCompletionRate = totalCompletionRate + completionRate %}
                                        {% set entryCount = entryCount + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {% set avgCompletionRate = entryCount > 0 ? (totalCompletionRate / entryCount)|round : 0 %}
                                <h6 class="mb-0 text-muted">Avg Completion</h6>
                                <h3 class="mb-0">{{ avgCompletionRate }}%</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress List Card -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Progress Entries</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" id="progress-search" class="form-control" placeholder="Search progress entries...">
                            <span class="input-group-text bg-primary text-white">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle border-0" id="progressTable">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th>Challenge</th>
                                <th>Progress</th>
                                <th>Score</th>
                                <th>Last Updated</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for entry in progress %}
                            <tr class="align-middle progress-item">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-3 bg-primary-subtle">
                                            {% if entry.user.getImage() %}
                                                <img
                                                    src="http://localhost/img/{{ entry.user.getImage() }}"
                                                    alt="Profile Image"
                                                    style="width: 100%; height: 100%; object-fit: cover;"
                                                >
                                            {% else %}
                                                <span class="avatar-text text-primary">{{ entry.user.getFullName()|slice(0,1)|upper }}</span>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <div class="fw-semibold">{{ entry.user.getFullName() }}</div>
                                            <div class="text-muted small">{{ entry.user.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ path('app_challenge_show', {'id': entry.challenge.id}) }}" class="challenge-badge bg-primary-subtle text-primary rounded-pill text-decoration-none">
                                        <i class="ri-trophy-line"></i> {{ entry.challenge.name }}
                                    </a>
                                </td>
                                <td>
                                    {% set totalQuestions = entry.challenge.quizzs|length %}
                                    {% set progressPercent = totalQuestions > 0 ? (entry.progressnb / totalQuestions) * 100 : 0 %}
                                    <div class="progress-label">
                                        <span>Progress</span>
                                        <span class="progress-value">{{ entry.progressnb }} / {{ totalQuestions }}</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" role="progressbar"
                                             style="width: {{ progressPercent }}%;"
                                             aria-valuenow="{{ progressPercent }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% set totalQuestions = entry.challenge.quizzs|length %}
                                    {% set scorePercent = totalQuestions > 0 ? (entry.score / (totalQuestions * 10)) * 100 : 0 %}
                                    <div class="progress-label">
                                        <span>Score</span>
                                        <span class="progress-value">{{ entry.score }} / {{ totalQuestions * 10 }}</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: {{ scorePercent }}%;"
                                             aria-valuenow="{{ scorePercent }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if entry.lastUpdated %}
                                        <div class="date-display">
                                            <span class="date">{{ entry.lastUpdated|date('M d, Y') }}</span>
                                            <span class="time">{{ entry.lastUpdated|date('H:i') }}</span>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">Not updated</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_progress_show', {'id': entry.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="View Details">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <a href="{{ path('app_progress_edit', {'id': entry.id}) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="Edit Progress">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <form method="post" action="{{ path('app_progress_delete', {'id': entry.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this progress entry? This action cannot be undone.');">
                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ entry.id) }}">
                                            <button class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete Progress">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-bar-chart-line empty-state-icon"></i>
                                        <h5>No progress entries found</h5>
                                        <p class="text-muted">There are no progress entries yet</p>
                                        <a href="{{ path('app_progress_new') }}" class="btn btn-primary mt-3">
                                            <i class="ri-add-line me-1"></i> Create New Progress Entry
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Search functionality
            const searchInput = document.getElementById('progress-search');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    const searchValue = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('#progressTable tbody tr');

                    tableRows.forEach(function(row) {
                        const userName = row.querySelector('td:nth-child(1)')?.textContent.toLowerCase() || '';
                        const challengeName = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
                        const progressText = row.querySelector('td:nth-child(3)')?.textContent.toLowerCase() || '';
                        const scoreText = row.querySelector('td:nth-child(4)')?.textContent.toLowerCase() || '';
                        const dateText = row.querySelector('td:nth-child(5)')?.textContent.toLowerCase() || '';

                        const textToSearch = userName + ' ' + challengeName + ' ' + progressText + ' ' + scoreText + ' ' + dateText;

                        if (textToSearch.includes(searchValue)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('#progressTable tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });

                // Make the entire row clickable to view progress details
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons
                    if (e.target.closest('.btn') || e.target.closest('form') || e.target.closest('a')) {
                        return;
                    }

                    const viewLink = this.querySelector('a[title="View Details"]');
                    if (viewLink) {
                        viewLink.click();
                    }
                });
            });

            // Add animation to stats cards
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
{% endblock %}
