security:
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
        App\Entity\User:
            algorithm: auto
            cost: 12

    providers:
        users_in_memory: { memory: null }
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            lazy: true
            provider: app_user_provider
            custom_authenticators:
                - App\Security\LoginFormAuthenticator
                - App\Security\GoogleAuthenticator
                - App\Security\GithubAuthenticator
            entry_point: App\Security\LoginFormAuthenticator
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800
                path: /
                always_remember_me: true
                remember_me_parameter: login[remember_me]
            logout:
                path: back_auth_logout
                target: back_auth_login
            access_denied_handler: App\Security\AccessDeniedHandler

    access_control:
        - { path: ^/back, roles: ROLE_ADMIN }
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/connect, roles: PUBLIC_ACCESS }  # Allow OAuth routes
        - { path: ^/, roles: PUBLIC_ACCESS }

    role_hierarchy:
        ROLE_ADMIN: ROLE_USER

when@test:
    security:
        password_hashers:
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4
                time_cost: 3
                memory_cost: 10