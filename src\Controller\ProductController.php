<?php

namespace App\Controller;

use App\Entity\Product;
use App\Form\ProductType;
use App\Repository\ProductRepository;
use App\Service\QrCodeGenerator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/product')]
final class ProductController extends AbstractController
{
    private QrCodeGenerator $qrCodeGenerator;
    public function __construct(QrCodeGenerator $qrCodeGenerator)
    {
        $this->qrCodeGenerator = $qrCodeGenerator;
    }
    #[Route(name: 'app_product_index', methods: ['GET'])]
    public function index(ProductRepository $productRepository): Response
    {
        return $this->render('product/index.html.twig', [
            'products' => $productRepository->findAll(),
        ]);
    }
    #[Route('/front', name: 'app_front_products',methods: ['GET'])]
    public function frontProducts(ProductRepository $productRepository): Response
    {
        $products=$productRepository->findAll();
        foreach($products as $product){
            $content=$this->buildProductQrContent($product);
            $qrCodePath=$this->qrCodeGenerator->generate($content,$product->getId(),'produit');
            
        }
        return $this->render('front/front_products.html.twig', [
            'products' => $products,
        ]);
    }
    private function buildProductQrContent(Product $product): string
    {
        $content = "Product Information:\n";
        $content .= "ID: " . $product->getId() . "\n";
        $content .= "Name: " . $product->getNomP() . "\n";
        $content .= "Price: " . $product->getPrice() . "\n";
        $content .= "Description: " . $product->getDescription() . "\n";
        $content .= "Categorie: " . $product->getCategorie() . "\n";
        $content .= "Stock: " . $product->getStock() . "\n";
        return $content;
    }
    #[Route('/cart', name: 'app_cart',methods: ['GET'])]
    public function cartIndex(): Response
    {
        return $this->render('front/cart.html.twig');
    }
    #[Route('/new', name: 'app_product_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $product = new Product();
        $form = $this->createForm(ProductType::class, $product);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $imageFile=$form->get('image')->getData();
            if($imageFile){
                $fileName=uniqid().'.'.$imageFile->guessExtension();
                $imageFile->move($this->getParameter('products_images_directory'), $fileName);
                $product->setImage($fileName);
            }
            $entityManager->persist($product);
            $entityManager->flush();

            return $this->redirectToRoute('app_product_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('product/new.html.twig', [
            'product' => $product,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_product_show', methods: ['GET'])]
    public function show(Product $product): Response
    {
        return $this->render('product/show.html.twig', [
            'product' => $product,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_product_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Product $product, EntityManagerInterface $entityManager): Response
    {
        $form= $this->createForm(ProductType::class,$product,['is_edit'=> true]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $imageFile=$form->get('image')->getData();
            if($imageFile){
                $fileName=uniqid().'.'.$imageFile->guessExtension();
                $imageFile->move($this->getParameter('products_images_directory'), $fileName);
                $product->setImage($fileName);
            }
            $entityManager->flush();

            return $this->redirectToRoute('app_product_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('product/edit.html.twig', [
            'product' => $product,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_product_delete', methods: ['POST'])]
    public function delete(Request $request, Product $product, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$product->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($product);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_product_index', [], Response::HTTP_SEE_OTHER);
    }
}
