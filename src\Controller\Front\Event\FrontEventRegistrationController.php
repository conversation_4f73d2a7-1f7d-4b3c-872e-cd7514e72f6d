<?php

namespace App\Controller\Front\Event;

use App\Entity\Event;
use App\Entity\EventRegistration;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Service\CalendarService;

#[Route('/events')]
class FrontEventRegistrationController extends AbstractController
{
    // #[Route('/{id}/register', name: 'app_event_register', methods: ['POST'])]
    // #[IsGranted('ROLE_USER')]
    // public function register(Request $request, Event $event, EntityManagerInterface $entityManager): Response
    // {
    //     if (!$this->isCsrfTokenValid('register-event', $request->request->get('token'))) {
    //         return new JsonResponse([
    //             'success' => false,
    //             'message' => 'Invalid token'
    //         ]);
    //     }

    //     // Check if user is already registered
    //     $existingRegistration = $entityManager->getRepository(EventRegistration::class)->findOneBy([
    //         'event' => $event,
    //         'user_id' => $this->getUser()->getId()
    //     ]);

    //     if ($existingRegistration) {
    //         return new JsonResponse([
    //             'success' => false,
    //             'message' => 'You are already registered for this event'
    //         ]);
    //     }

    //     // Check if event is full
    //     if ($event->getMaxParticipants() !== null && 
    //         $event->getEventRegistrations()->count() >= $event->getMaxParticipants()) {
    //         return new JsonResponse([
    //             'success' => false,
    //             'message' => 'This event is already full'
    //         ]);
    //     }

    //     // Create new registration
    //     $registration = new EventRegistration();
    //     $registration->setEvent($event);
    //     $registration->setUserId($this->getUser()->getId());
    //     $registration->setStatus('registered');
    //     $registration->setRegistrationDate(new \DateTimeImmutable());

    //     $entityManager->persist($registration);
    //     $entityManager->flush();

    //     return new JsonResponse([
    //         'success' => true,
    //         'message' => 'You have successfully registered for this event'
    //     ]);
    // }


    #[Route('/{id}/register', name: 'app_event_register', methods: ['POST'])]
    public function register(
        Request $request,
        Event $event,
        EntityManagerInterface $entityManager,
        CalendarService $calendarService
    ): JsonResponse {
        try {
            $content = $request->getContent();
            $data = json_decode($content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON data received: ' . json_last_error_msg());
            }

            $email = $data['email'] ?? null;
            if (!$email) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Email is required'
                ], Response::HTTP_BAD_REQUEST);
            }

            $respo = [
                'name' => $event->getTitle(),
                'email' => $email
            ];

            // Add event to user's calendar
            // $calendarResponse = $calendarService->addEventToCalendar($event, $email);
            // $calendarResponse = $calendarService->createEvent(
            //     $event->getId(), 
            //     $event->getDate(), 
            //     $event->getDate(),  
            //     $respo
            // );
            
            // if (!$calendarResponse['success']) {
            //     throw new \Exception($calendarResponse['error'] ?? 'Failed to add event to calendar');
            // }

            // Create event registration
            $registration = new EventRegistration();
            $registration->setEvent($event);
            $registration->setEmail($email);
            // $registration->setRegistrationDate(new \DateTime());
            $registration->setRegistrationDate(new \DateTimeImmutable());
            
            $entityManager->persist($registration);
            $entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Registration successful! The event has been added to your calendar.',
                // 'calendarDetails' => $calendarResponse['data'] ?? null
            ]);
        } catch (\Exception $e) {
            // Log the error
            error_log('Event registration error: ' . $e->getMessage());
            error_log('Request content: ' . $request->getContent());
            
            return new JsonResponse([
                'success' => false,
                'error' => 'Registration failed: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    // #[Route('/r', name: 'app_event_register', methods: ['GET'])]
    // public function temp(
    //     Request $request,
    //     EntityManagerInterface $entityManager,
    //     CalendarService $calendarService
    // ): Response {
    //     try {
    //         $content = $request->getContent();
    //         $data = json_decode($content, true);
            
    //         // if (json_last_error() !== JSON_ERROR_NONE) {
    //         //     throw new \Exception('Invalid JSON data received: ' . json_last_error_msg());
    //         // }

    //         // $email = $data['email'] ?? null;
    //         // if (!$email) {
    //         //     return new JsonResponse([
    //         //         'success' => false,
    //         //         'error' => 'Email is required'
    //         //     ], Response::HTTP_BAD_REQUEST);
    //         // }

    //         $email = '<EMAIL>';

    //         $event = $entityManager->getRepository(Event::class)->find(2);
    //         // Add event to user's calendar
    //         // $calendarResponse = $calendarService->addEventToCalendar($event, '<EMAIL>');
            
    //         // Minimum required fields based on the API documentation
    //         $eventTypeId = 2323232;  // Make sure this is a valid eventTypeId in your Cal.com account
    //         $start = "2023-05-24T13:00:00.000Z";
    //         $end = "2023-05-24T13:30:00.000Z";
    //         $responses = [
    //             'name' => 'Hello Hello',
    //             'email' => '<EMAIL>'
    //             // Remove other fields to keep it minimal
    //         ];

    //         // Call with just the required parameters
    //         $calendarResponse = $calendarService->createEvent(
    //             $eventTypeId, 
    //             $start, 
    //             $end, 
    //             $responses
    //         );

    //         var_dump($calendarResponse);
    //         return new Response(

    //         );
            
    //         // if (!$calendarResponse['success']) {
    //         //     throw new \Exception($calendarResponse['error'] ?? 'Failed to add event to calendar');
    //         // }

    //         // // Create event registration
    //         // $registration = new EventRegistration();
    //         // $registration->setEvent($event);
    //         // $registration->setEmail($email);
    //         // // $registration->setRegistrationDate(new \DateTime());
    //         // $registration->setRegistrationDate(new \DateTimeImmutable());
            
    //         // $entityManager->persist($registration);
    //         // $entityManager->flush();

    //         // return new JsonResponse([
    //         //     'success' => true,
    //         //     'message' => 'Registration successful! The event has been added to your calendar.',
    //         //     //'calendarDetails' => $calendarResponse['data'] ?? null
    //         // ]);
    //     } catch (\Exception $e) {
    //         // Log the error
    //         error_log('Event registration error: ' . $e->getMessage());
    //         error_log('Request content: ' . $request->getContent());
            
    //         return new JsonResponse([
    //             'success' => false,
    //             'error' => 'Registration failed: ' . $e->getMessage()
    //         ], Response::HTTP_INTERNAL_SERVER_ERROR);
    //     }
    // }


} 