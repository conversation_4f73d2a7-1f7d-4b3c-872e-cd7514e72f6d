<?php

namespace App\Service;

use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class FaceRecognitionService
{
    public function detectFace(): string
    {
        // Use the absolute path to the script
        $pythonScriptPath = realpath(__DIR__ . '/../python_scripts/py_face_recognation/face_rec.py');

        if (!$pythonScriptPath) {
            return 'Python script not found! Path: ' . $pythonScriptPath;
        }

        // Debugging: Check the resolved path
        // echo 'Resolved Python script path: ' . $pythonScriptPath . PHP_EOL;

        // Ensure the Python executable is called correctly
        $process = new Process(['python', $pythonScriptPath]);

        try {
            $process->mustRun();
            return $process->getOutput();
        } catch (ProcessFailedException $exception) {
            return 'Error: ' . $exception->getMessage();
        }
    }

    public function extract_id($string): string {
        // Split the string by ':' and get the second part
        $parts = explode(':', $string);
        // Get the ID from the second part, trim any whitespace
        $id = trim($parts[1]);
        return $id;
    }
    
}

