/* Modern Navbar Styles */

/* Navbar Container */
.navbar {
    padding: 15px 20px;
    background: #ffffff;
    border: none;
    border-radius: 0;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    background: rgba(107, 183, 72, 0.1);
    border: none;
    color: var(--eco-primary);
    font-size: 1.25rem;
    padding: 8px 12px;
    border-radius: 10px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: rgba(107, 183, 72, 0.2);
    transform: translateY(-2px);
}

.sidebar-toggle:active {
    transform: translateY(0);
}

/* Page Title */
.page-title h4 {
    color: #333;
    font-weight: 600;
    font-size: 1.2rem;
    margin: 0;
}

/* User Dropdown */
.user-dropdown {
    display: flex;
    align-items: center;
    background: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 50px;
    transition: all 0.3s;
}

.user-dropdown:hover {
    background: rgba(107, 183, 72, 0.1);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--eco-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.user-avatar-lg {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--eco-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.user-name {
    font-weight: 500;
    color: #333;
}

/* Dropdown Menu */
.dropdown-menu {
    border: none;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 0;
    min-width: 280px;
    margin-top: 10px !important;
    overflow: hidden;
}

.dropdown-header {
    background: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.dropdown-item {
    padding: 12px 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.2s;
}

.dropdown-item i {
    font-size: 1.2rem;
    color: var(--eco-primary);
}

.dropdown-item:hover {
    background: rgba(107, 183, 72, 0.1);
    color: var(--eco-primary);
}

.dropdown-item.text-danger i {
    color: #dc3545;
}

.dropdown-item.text-danger:hover {
    background: rgba(220, 53, 69, 0.1);
}

.dropdown-divider {
    margin: 0;
    border-color: #eee;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .navbar {
        padding: 10px 15px;
    }
    
    .user-dropdown {
        padding: 5px;
    }
}
