{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'quizz-form'}}) }}
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-question-line text-primary me-2"></i> Question Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="form-group">
                            {{ form_label(form.question, 'Question', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                            <div class="input-group">
                                <span class="input-group-text bg-primary-subtle text-primary">
                                    <i class="ri-question-answer-line"></i>
                                </span>
                                {{ form_widget(form.question, {
                                    'attr': {
                                        'class': 'form-control form-field rounded-end',
                                        'placeholder': 'Enter your question here...',
                                        'required': 'required',
                                        'style': 'border-color: #dee2e6;'
                                    }
                                }) }}
                            </div>
                            <div class="invalid-feedback">
                                Please enter a question
                            </div>
                            <div class="text-danger">
                                {{ form_errors(form.question) }}
                            </div>
                            <small class="form-text text-muted mt-2">
                                <i class="ri-information-line me-1"></i> Enter a clear and concise question
                            </small>
                        </div>
                    </div>

                    <div class="mb-4">
                        <button class="btn btn-secondary w-100 d-flex align-items-center justify-content-center" type="button" onclick="generate()">
                            <i class="ri-bard-line me-2"></i> Generate Answers with AI
                            <div class="spinner-border spinner-border-sm ms-2 d-none" id="generate-spinner" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </button>
                        <small class="form-text text-muted mt-2 text-center">
                            <i class="ri-information-line me-1"></i> AI will generate answer choices based on your question
                        </small>
                    </div>

                    <h5 class="mb-3 fw-bold d-flex align-items-center">
                        <i class="ri-list-check text-primary me-2"></i> Answer Choices
                    </h5>
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="form-group">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="choice-number me-2">1</div>
                                    {{ form_label(form.choice1, 'Choice 1', {'label_attr': {'class': 'form-label fw-semibold mb-0'}}) }}
                                </div>
                                {{ form_widget(form.choice1, {
                                    'attr': {
                                        'class': 'form-control form-field rounded',
                                        'placeholder': 'Enter choice 1',
                                        'required': 'required',
                                        'style': 'border-color: #dee2e6;'
                                    }
                                }) }}
                                <div class="invalid-feedback">
                                    Please enter choice 1
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.choice1) }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-group">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="choice-number me-2">2</div>
                                    {{ form_label(form.choice2, 'Choice 2', {'label_attr': {'class': 'form-label fw-semibold mb-0'}}) }}
                                </div>
                                {{ form_widget(form.choice2, {
                                    'attr': {
                                        'class': 'form-control form-field rounded',
                                        'placeholder': 'Enter choice 2',
                                        'required': 'required',
                                        'style': 'border-color: #dee2e6;'
                                    }
                                }) }}
                                <div class="invalid-feedback">
                                    Please enter choice 2
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.choice2) }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-group">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="choice-number me-2">3</div>
                                    {{ form_label(form.choice3, 'Choice 3', {'label_attr': {'class': 'form-label fw-semibold mb-0'}}) }}
                                </div>
                                {{ form_widget(form.choice3, {
                                    'attr': {
                                        'class': 'form-control form-field rounded',
                                        'placeholder': 'Enter choice 3',
                                        'required': 'required',
                                        'style': 'border-color: #dee2e6;'
                                    }
                                }) }}
                                <div class="invalid-feedback">
                                    Please enter choice 3
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.choice3) }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-group">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="choice-number me-2">4</div>
                                    {{ form_label(form.choice4, 'Choice 4', {'label_attr': {'class': 'form-label fw-semibold mb-0'}}) }}
                                </div>
                                {{ form_widget(form.choice4, {
                                    'attr': {
                                        'class': 'form-control form-field rounded',
                                        'placeholder': 'Enter choice 4',
                                        'required': 'required',
                                        'style': 'border-color: #dee2e6;'
                                    }
                                }) }}
                                <div class="invalid-feedback">
                                    Please enter choice 4
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.choice4) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-group">
                            {{ form_label(form.challenge, 'Associated Challenge', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                            <div class="input-group">
                                <span class="input-group-text bg-success-subtle text-success">
                                    <i class="ri-trophy-line"></i>
                                </span>
                                {{ form_widget(form.challenge, {
                                    'attr': {
                                        'class': 'form-control form-field rounded-end',
                                        'style': 'border-color: #dee2e6;'
                                    }
                                }) }}
                            </div>
                            <div class="text-danger">
                                {{ form_errors(form.challenge) }}
                            </div>
                            <small class="form-text text-muted mt-2">
                                <i class="ri-information-line me-1"></i> Select the challenge this question belongs to
                            </small>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ path('app_quizz_index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i> Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i> {{ button_label|default('Save Question') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.1s; border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-checkbox-circle-line text-success me-2"></i> Correct Answer
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-label fw-semibold mb-3">{{ form_label(form.answer, 'Select the correct answer') }}</div>
                        <div class="answer-options">
                            {% for choice in form.answer %}
                                <div class="answer-option mb-3">
                                    <div class="form-check custom-radio">
                                        {{ form_widget(choice, {
                                            'attr': {
                                                'class': 'form-check-input form-field',
                                                'required': 'required'
                                            }
                                        }) }}
                                        <label class="form-check-label d-flex align-items-center" for="{{ choice.vars.id }}">
                                            <div class="choice-number me-2">{{ loop.index }}</div>
                                            <span>Choice {{ loop.index }}</span>
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="invalid-feedback">
                            Please select the correct answer
                        </div>
                        <div class="text-danger">
                            {{ form_errors(form.answer) }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-information-line text-primary me-2"></i> Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0 ps-3">
                        <li class="mb-2">Write clear and concise questions</li>
                        <li class="mb-2">Make sure one answer is clearly correct</li>
                        <li class="mb-2">Keep answer choices similar in length</li>
                        <li>Use the AI generator for help with answer choices</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
{{ form_end(form) }}

<style>
    /* Choice Number Style */
    .choice-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #0d6efd;
        color: #fff;
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* Custom Radio Styling */
    .answer-option {
        transition: all 0.3s ease;
    }

    .answer-option:hover {
        transform: translateY(-2px);
    }

    .form-check-input:checked ~ .form-check-label .choice-number {
        background-color: #198754;
    }

    .form-check-input:checked ~ .form-check-label {
        color: #198754;
        font-weight: 600;
    }
</style>

<script>
 async function generate() {
    try {
        const generateButton = document.querySelector('.btn-secondary');
        const generateSpinner = document.getElementById('generate-spinner');
        const questionInput = document.querySelector('#{{ form.question.vars.id }}');

        const chx1Input = document.querySelector('#{{ form.choice1.vars.id }}');
        const chx2Input = document.querySelector('#{{ form.choice2.vars.id }}');
        const chx3Input = document.querySelector('#{{ form.choice3.vars.id }}');
        const chx4Input = document.querySelector('#{{ form.choice4.vars.id }}');

        const answerRadios = document.querySelectorAll('[name="{{ form.answer.vars.full_name }}"]');

        const questionValue = questionInput.value;

        if (!questionValue || questionValue.trim() === '') {
            alert('Please enter a question first before generating answers.');
            return;
        }

        // Show loading spinner
        generateSpinner.classList.remove('d-none');
        generateButton.disabled = true;

        const GEMINI_API_KEY = "AIzaSyDNWoXkSzVLRNgWeEJnqjcrTTIkQKwGWOs";
        const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

        const prompt = `You are an AI that generates multiple-choice quiz answers.
                        Generate four answer choices and the correct answer for the following question: "${questionValue}". Format the response as JSON: {\"choices\": [\"choice1\", \"choice2\", \"choice3\", \"choice4\"], \"answer\": \"correct_choice\" as a number (first choice = 1, and the choice 4 = 4)}.
                        NOTE: keep the choices short and simple.`;

        const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: prompt,
                  },
                ],
              },
            ],
          }),
        });

        const data = await response.json();
        let rawText = data.candidates[0].content.parts[0].text;

        // Remove backticks and unnecessary formatting
        rawText = rawText.replace(/```json/g, '').replace(/```/g, '').trim();

        const parsedData = JSON.parse(rawText);

        // Populate the input fields
        if (parsedData.choices) {
            chx1Input.value = parsedData.choices[0];
            chx2Input.value = parsedData.choices[1];
            chx3Input.value = parsedData.choices[2];
            chx4Input.value = parsedData.choices[3];

            // Add valid class to inputs
            chx1Input.classList.add('is-valid');
            chx2Input.classList.add('is-valid');
            chx3Input.classList.add('is-valid');
            chx4Input.classList.add('is-valid');
        }

        // Select the correct answer radio button
        if (parsedData.answer >= 1 && parsedData.answer <= 4) {
            answerRadios[parsedData.answer - 1].checked = true;
        }

        // Hide loading spinner
        generateSpinner.classList.add('d-none');
        generateButton.disabled = false;

        // Show success message
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success mt-2';
        successAlert.innerHTML = '<i class="ri-check-line me-1"></i> Answers generated successfully!';
        generateButton.parentNode.appendChild(successAlert);

        // Remove success message after 3 seconds
        setTimeout(() => {
            successAlert.remove();
        }, 3000);

    } catch (error) {
        console.error("Error :", error);

        // Hide loading spinner
        document.getElementById('generate-spinner').classList.add('d-none');
        document.querySelector('.btn-secondary').disabled = false;

        // Show error message
        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger mt-2';
        errorAlert.innerHTML = '<i class="ri-error-warning-line me-1"></i> Error generating answers. Please try again.';
        document.querySelector('.btn-secondary').parentNode.appendChild(errorAlert);

        // Remove error message after 3 seconds
        setTimeout(() => {
            errorAlert.remove();
        }, 3000);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const formFields = document.querySelectorAll('.form-field');

    formFields.forEach(field => {
        // Validate on input (while typing)
        field.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Form submission validation
    document.getElementById('quizz-form').addEventListener('submit', function(event) {
        let isValid = true;
        formFields.forEach(field => {
            if (!field.checkValidity()) {
                isValid = false;
                field.classList.add('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});
</script>
