.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #76b852 0%, #5a9540 100%);
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background-image: 
        radial-gradient(circle at 50% 50%, rgba(255,255,255,0.1) 0%, transparent 5%),
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.05) 0%, transparent 10%);
    background-size: 50px 50px;
    transform: rotate(30deg);
    animation: patternMove 60s linear infinite;
    pointer-events: none;
}

@keyframes patternMove {
    0% {
        transform: rotate(30deg) translateY(0);
    }
    100% {
        transform: rotate(30deg) translateY(-50px);
    }
}

.auth-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 420px;
    padding: 2.5rem;
    position: relative;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.auth-logo img {
    height: 40px;
    width: auto;
    transition: transform 0.3s ease;
}

.auth-logo img:hover {
    transform: scale(1.05);
}

.auth-header h1 {
    color: #2c3e2d;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.auth-header p {
    color: #5c735f;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.form-floating input {
    border-radius: 10px;
    border: 1.5px solid #e8f0e9;
    padding: 1rem 0.75rem;
    height: calc(3.5rem + 2px);
    font-size: 0.95rem;
    background-color: #f8faf8;
}

.form-floating input:focus {
    border-color: #76b852;
    box-shadow: 0 0 0 0.25rem rgba(118, 184, 82, 0.1);
    background-color: #fff;
}

.form-floating label {
    color: #5c735f;
    padding: 1rem 0.75rem;
}

.form-check {
    padding-left: 1.75rem;
}

.form-check-input {
    width: 1.1rem;
    height: 1.1rem;
    margin-left: -1.75rem;
    border: 1.5px solid #e8f0e9;
    background-color: #f8faf8;
}

.form-check-input:checked {
    background-color: #76b852;
    border-color: #76b852;
}

.form-check-label {
    color: #5c735f;
    font-size: 0.95rem;
}

.auth-btn {
    width: 100%;
    padding: 0.875rem;
    border-radius: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    font-size: 0.95rem;
}

.auth-btn i {
    font-size: 1.25rem;
}

.primary-btn {
    background: linear-gradient(135deg, #76b852 0%, #5a9540 100%);
    color: #fff;
}

.primary-btn:hover {
    background: linear-gradient(135deg, #5a9540 0%, #4a6741 100%);
    transform: translateY(-1px);
}

.auth-divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e8f0e9;
}

.auth-divider span {
    background: #fff;
    padding: 0 1rem;
    color: #5c735f;
    position: relative;
    font-size: 0.875rem;
    font-weight: 500;
}

.social-auth-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.social-auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem;
    padding-bottom: 0.875rem;
    padding-top: 0.875rem;
    border-radius: 10px;
    color: #2c3e2d;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    background: #f8faf8;
    border: 1.5px solid #e8f0e9;
}

.social-auth-btn:hover {
    transform: translateY(-1px);
    background: #fff;
    border-color: #76b852;
    color: #76b852;
}

.github-btn i {
    color: #24292e;
}

.google-btn i {
    color: #ea4335;
}

.face-id-btn i {
    color: #76b852;
}

.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.auth-links {
    font-size: 0.95rem;
}

.auth-links a {
    color: #76b852;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s ease;
}

.auth-links a:hover {
    color: #5a9540;
    transform: translateY(-1px);
}

.text-muted {
    color: #5c735f;
}

.forgot-link, .signup-link {
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.forgot-link:hover, .signup-link:hover {
    background: #f8faf8;
}

.terms-link {
    color: #76b852;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.terms-link:hover {
    color: #5a9540;
    text-decoration: underline;
}

.password-requirements {
    background: #f8faf8;
    padding: 1rem;
    border-radius: 10px;
    border: 1.5px solid #e8f0e9;
}

.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirements-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #5c735f;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.requirements-list li:last-child {
    margin-bottom: 0;
}

.requirements-list li i {
    color: #76b852;
    font-size: 1rem;
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #5c735f;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.back-link:hover {
    background: #f8faf8;
    color: #76b852;
}

.login-link {
    color: #76b852;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s ease;
    padding: 0.5rem;
    border-radius: 8px;
}

.login-link:hover {
    background: #f8faf8;
    transform: translateY(-1px);
}
