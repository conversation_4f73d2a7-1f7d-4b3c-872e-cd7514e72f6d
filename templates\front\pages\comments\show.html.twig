{% extends 'back/pages/home/<USER>' %}

{% block dash %} {% endblock %}
{% block forum %}{% endblock %}
{% block cmt %}active{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Comment Details</h1>
            <div class="btn-group">
                <a href="{{ path('app_comments_index') }}" class="btn btn-secondary">
                    <i class="ri-arrow-left-line"></i> Back to List
                </a>
                <a href="{{ path('app_comments_edit', {'id': comment.id}) }}" class="btn btn-warning">
                    <i class="ri-edit-box-line"></i> Edit
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="ri-message-3-line"></i> Comment #{{ comment.id }}
                        </h6>
                        <div>
                            <span class="badge bg-primary me-2">
                                <i class="ri-chat-3-line"></i> Forum #{{ comment.postid.id }}
                            </span>
                            <span class="badge bg-success">
                                <i class="ri-thumb-up-line"></i> {{ comment.upvotes }} Upvotes
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="border-left-info p-3 bg-light rounded">
                                    <h5 class="text-primary mb-3">Comment Content</h5>
                                    <p class="mb-0">{{ comment.content|nl2br }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">
                                            <i class="ri-chat-3-line"></i> Related Forum
                                        </h6>
                                        <p class="card-text">
                                            <strong>Title:</strong> {{ comment.postid.title }}<br>
                                            <strong>Created:</strong> {{ comment.postid.createdat ? comment.postid.createdat|date('F d, Y h:i A') : 'Not specified' }}
                                        </p>
                                        <a href="{{ path('app_forums_show', {'id': comment.postid.id}) }}" class="btn btn-sm btn-info">
                                            <i class="ri-external-link-line"></i> View Forum
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">
                                            <i class="ri-thumb-up-line"></i> Engagement
                                        </h6>
                                        <div class="d-flex align-items-center">
                                            <div class="display-4 me-3">{{ comment.upvotes }}</div>
                                            <div class="text-muted">Upvotes received</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                <i class="ri-information-line"></i> Comment ID: {{ comment.id }}
                            </div>
                            {{ include('back/pages/comments/_delete_form.html.twig', {
                                'button_class': 'btn btn-danger',
                                'button_icon': 'ri-delete-bin-line',
                                'button_text': 'Delete Comment'
                            }) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
