<?php

namespace App\Service;

use App\Entity\Event;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpFoundation\Response;

class CalendarService
{
    private string $calApiKey;
    private HttpClientInterface $httpClient;

    public function __construct(
        HttpClientInterface $httpClient,
        string $calApiKey = 'cal_live_3e273a9233e02ec67b96ab936d0a9f91'
    ) {
        $this->httpClient = $httpClient;
        $this->calApiKey = $calApiKey;
    }

    public function addEventToCalendar(Event $event, string $userEmail): array
    {
        try {
            // Format date for Cal.com
            $startDate = $event->getDate();
            $endDate = clone $event->getDate();
            $endDate->modify('+1 hour');

            $eventData = [
                'apiKey' => $this->calApiKey,
                'title' => $event->getTitle(),
                'description' => $event->getDescription(),
                'startTime' => $startDate->format('Y-m-d\TH:i:s\Z'),
                'endTime' => $endDate->format('Y-m-d\TH:i:s\Z'),
                'location' => $event->getLocation() ?: 'Online',
                'attendees' => [
                    ['email' => $userEmail]
                ]
            ];

            $response = $this->httpClient->request('POST', 'https://api.cal.com/v1/scheduling/create-event', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $this->calApiKey
                ],
                'json' => $eventData,
                'verify_peer' => false,
                'verify_host' => false
            ]);

            $statusCode = $response->getStatusCode();
            $content = $response->toArray(false);

            if ($statusCode !== Response::HTTP_OK && $statusCode !== Response::HTTP_CREATED) {
                throw new \Exception('Failed to create event: ' . ($content['message'] ?? 'Unknown error'));
            }

            return [
                'success' => true,
                'data' => $content
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    // public function createSchedule(string $name, string $timeZone): array
    // {
    //     try {
    //         // Prepare the payload
    //         $scheduleData = [
    //             'name' => $name,
    //             'timeZone' => $timeZone
    //         ];

    //         // Make the API request
    //         $response = $this->httpClient->request('POST', 'https://api.cal.com/v1/schedules', [
    //             'headers' => [
    //                 'Content-Type' => 'application/json',
    //                 'Authorization' => 'Bearer ' . $this->calApiKey
    //             ],
    //             'json' => $scheduleData,
    //             'verify_peer' => false, // Disable SSL verification (not recommended for production)
    //             'verify_host' => false   // Disable SSL verification (not recommended for production)
    //         ]);

    //         // Get the response status code and content
    //         $statusCode = $response->getStatusCode();
    //         $content = $response->getContent(false); // Get raw response content

    //         // Log the raw response for debugging
    //         error_log("API Response: " . $content);

    //         // Handle empty response
    //         if (empty($content)) {
    //             throw new \Exception('Response body is empty.');
    //         }

    //         // Decode the JSON response
    //         $contentArray = json_decode($content, true);
    //         if (json_last_error() !== JSON_ERROR_NONE) {
    //             throw new \Exception('Failed to decode JSON response: ' . json_last_error_msg());
    //         }

    //         // Check for errors in the response
    //         if ($statusCode !== Response::HTTP_OK && $statusCode !== Response::HTTP_CREATED) {
    //             throw new \Exception('Failed to create schedule: ' . ($contentArray['message'] ?? 'Unknown error'));
    //         }

    //         // Return success response
    //         return [
    //             'success' => true,
    //             'data' => $contentArray
    //         ];

    //     } catch (\Exception $e) {
    //         // Log the error for debugging
    //         error_log("Error: " . $e->getMessage());

    //         // Return error response
    //         return [
    //             'success' => false,
    //             'error' => $e->getMessage()
    //         ];
    //     }
    // }

    public function createEvent(int $eventTypeId, string $start, string $end, array $responses): array
    {
        try {
            // Prepare the payload with only required fields
            $eventData = [
                'eventTypeId' => $eventTypeId,
                'start' => $start,
                'end' => $end,
                'responses' => $responses
            ];

            // Your API key
            $apiKey = 'cal_live_1e5501207c584db5e81332df9553cf9b'; // Use your actual API key

            // Make the API request - add apiKey as a query parameter
            $response = $this->httpClient->request('POST', 'https://api.cal.com/v1/bookings?apiKey=' . $apiKey, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    // Authorization header is included but the apiKey as query param is the key requirement
                    'Authorization' => 'Bearer ' . $apiKey
                ],
                'json' => $eventData,
                'verify_peer' => false, // Disable SSL verification (not recommended for production)
                'verify_host' => false  // Disable SSL verification (not recommended for production)
            ]);

            // Get the response status code and content
            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false); // Get raw response content

            // Log the raw response for debugging
            error_log("API Response: " . $content);

            // Handle empty response
            if (empty($content)) {
                throw new \Exception('Response body is empty.');
            }

            // Decode the JSON response
            $contentArray = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Failed to decode JSON response: ' . json_last_error_msg());
            }

            // Check for errors in the response
            if ($statusCode !== Response::HTTP_OK && $statusCode !== Response::HTTP_CREATED) {
                throw new \Exception('Failed to create event: ' . ($contentArray['message'] ?? 'Unknown error'));
            }

            // Return success response
            return [
                'success' => true,
                'data' => $contentArray
            ];

        } catch (\Exception $e) {
            // Log the error for debugging
            error_log("Error: " . $e->getMessage());

            // Return error response
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
