<?php

namespace App\Entity;

use App\Repository\DonationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Entity(repositoryClass: DonationRepository::class)]
class Donation
{
    public const VALID_TYPES = ['Monetary', 'Material', 'Service'];
    public const VALID_STATUSES = ['pending', 'approved', 'completed', 'cancelled'];
    public const VALID_PAYMENT_METHODS = ['cash', 'bank_transfer', 'check', 'credit_card'];

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column]
    #[Assert\NotBlank(message: 'Le montant ne peut pas être vide')]
    #[Assert\Type(
        type: 'integer',
        message: 'Le montant doit être un nombre entier'
    )]
    #[Assert\Positive(message: 'Le montant doit être positif')]
    #[Assert\Range(
        min: 1,
        max: 1000000,
        notInRangeMessage: 'Le montant doit être entre {{ min }} et {{ max }} TND'
    )]
    #[Assert\Regex(
        pattern: '/^\d+$/',
        message: 'Le montant ne doit contenir que des chiffres'
    )]
    private ?int $amount = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Assert\NotNull(message: 'La date ne peut pas être vide')]
    #[Assert\Type(
        type: '\DateTimeInterface',
        message: 'La date n\'est pas valide'
    )]
    #[Assert\LessThanOrEqual(
        'today',
        message: 'La date ne peut pas être dans le futur'
    )]
    private ?\DateTimeInterface $donation_date;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Le type ne peut pas être vide')]
    #[Assert\Choice(
        choices: self::VALID_TYPES,
        message: 'Choisissez un type valide : {{ choices }}'
    )]
    private ?string $type = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Le statut ne peut pas être vide')]
    #[Assert\Choice(
        choices: self::VALID_STATUSES,
        message: 'Choisissez un statut valide : {{ choices }}'
    )]
    private ?string $status = 'pending';

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'La méthode de paiement ne peut pas être vide')]
    #[Assert\Choice(
        choices: self::VALID_PAYMENT_METHODS,
        message: 'Choisissez une méthode de paiement valide : {{ choices }}'
    )]
    private ?string $payment_method = null;

    #[ORM\ManyToOne(inversedBy: 'donations')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'Le partenaire ne peut pas être vide')]
    private ?Partners $partner_id = null;

    public function __construct()
    {
        $this->donation_date = new \DateTime();
        $this->status = 'pending';
    }

    #[Assert\Callback]
    public function validatePaymentMethod(ExecutionContextInterface $context): void
    {
        if ($this->type === 'Material' && $this->payment_method !== null) {
            $context->buildViolation('La méthode de paiement ne doit pas être spécifiée pour les dons matériels')
                ->atPath('payment_method')
                ->addViolation();
        }

        if ($this->type === 'Service' && $this->payment_method !== null) {
            $context->buildViolation('La méthode de paiement ne doit pas être spécifiée pour les dons de service')
                ->atPath('payment_method')
                ->addViolation();
        }

        if ($this->type === 'Monetary' && $this->payment_method === null) {
            $context->buildViolation('La méthode de paiement est requise pour les dons monétaires')
                ->atPath('payment_method')
                ->addViolation();
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setAmount(?int $amount): static
    {
        $this->amount = $amount;
        return $this;
    }

    public function getDonationDate(): ?\DateTimeInterface
    {
        return $this->donation_date;
    }

    public function setDonationDate(?\DateTimeInterface $donation_date): static
    {
        $this->donation_date = $donation_date ?? new \DateTime();
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->payment_method;
    }

    public function setPaymentMethod(?string $payment_method): static
    {
        $this->payment_method = $payment_method;
        return $this;
    }

    public function getPartnerId(): ?Partners
    {
        return $this->partner_id;
    }

    public function setPartnerId(?Partners $partner_id): static
    {
        $this->partner_id = $partner_id;
        return $this;
    }
}
