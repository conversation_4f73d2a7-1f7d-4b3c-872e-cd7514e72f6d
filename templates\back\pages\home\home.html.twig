{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link href="{{ asset('back/css/dashboard-home.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
{% endblock %}

{% block content %}
    <div class="dashboard-container">
        <!-- Welcome Header -->
        <div class="welcome-header animate__animated animate__fadeIn">
            <div class="d-flex align-items-center flex-wrap">
                <h1>Welcome, {{ app.user.fullName }}</h1>
                <span class="system-status">
                    <i class="ri-checkbox-circle-line me-1"></i> System Online
                </span>
            </div>
            <p class="text-muted">Here's what's happening with your eco.net platform today.</p>
        </div>

        <!-- Stats Overview -->
        <div class="row mb-4">
            <!-- User Management Section -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm section-card animate__animated animate__fadeIn" style="animation-delay: 0.4s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">User Management</h5>
                        <a href="{{ path('back_admin_users_index') }}" class="btn btn-sm btn-primary">
                            <i class="ri-user-settings-line me-1"></i> Manage Users
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ users|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Total Users</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ adminCount|default(0) }}</h3>
                                    <p class="text-muted mb-0">Admins</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ regularCount|default(0) + partnerCount|default(0) }}</h3>
                                    <p class="text-muted mb-0">Regular Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="userRoleChart"></canvas>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ path('back_admin_users_index') }}" class="btn btn-outline-primary">
                                <i class="ri-user-line me-1"></i> Go to Users
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Events Management -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm section-card animate__animated animate__fadeIn" style="animation-delay: 0.5s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Events Management</h5>
                        <a href="{{ path('app_admin_event_index') }}" class="btn btn-sm btn-primary">
                            <i class="ri-calendar-line me-1"></i> Manage Events
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ events|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Total Events</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ upcomingEvents|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Upcoming</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ events|default([])|length - upcomingEvents|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Past Events</p>
                                </div>
                            </div>
                        </div>

                        {% if upcomingEvents is defined and upcomingEvents|length > 0 %}
                            <h6 class="mb-3">Upcoming Events</h6>
                            <div class="table-responsive">
                                <table class="table table-hover dashboard-table">
                                    <thead>
                                        <tr>
                                            <th>Event</th>
                                            <th>Date</th>
                                            <th>Location</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for event in upcomingEvents|slice(0, 3) %}
                                            <tr>
                                                <td>
                                                    <p class="text-decoration-none">
                                                        {{ event.title }}
                                                    </p>
                                                </td>
                                                <td>{{ event.date ? event.date|date('M d, Y') : '' }}</td>
                                                <td>{{ event.location }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="ri-information-line me-2"></i> No upcoming events scheduled.
                            </div>
                        {% endif %}

                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ path('app_admin_event_new') }}" class="btn btn-outline-primary">
                                <i class="ri-calendar-line me-1"></i> Add New Event
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <!-- Forums Overview -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm section-card animate__animated animate__fadeIn" style="animation-delay: 0.6s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Forums Overview</h5>
                        <a href="{{ path('app_forums_index') }}" class="btn btn-sm btn-primary">
                            <i class="ri-discuss-line me-1"></i> Manage Forums
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ forums|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Forums</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ comments|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Comments</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ todayForums|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Today's Posts</p>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <canvas id="forumActivityChart"></canvas>
                        </div>

                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ path('app_forums_index') }}" class="btn btn-outline-primary">
                                <i class="ri-discuss-line me-1"></i> Go to Forums
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Challenges Section -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm section-card animate__animated animate__fadeIn" style="animation-delay: 0.7s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Challenges</h5>
                        <div>
                            <a href="{{ path('app_challenge_index') }}" class="btn btn-sm btn-primary me-2">
                                <i class="ri-trophy-line me-1"></i> Challenges
                            </a>
                            <a href="{{ path('app_quizz_index') }}" class="btn btn-sm btn-primary">
                                <i class="ri-question-line me-1"></i> Quizzes
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ challenges|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Total Challenges</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ activeCount|default(0) }}</h3>
                                    <p class="text-muted mb-0">Active</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ progressEntries|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Participations</p>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <canvas id="challengeStatusChart"></canvas>
                        </div>

                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ path('app_challenge_new') }}" class="btn btn-outline-primary">
                                <i class="ri-trophy-line me-1"></i> Add New Challenge
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <!-- Partners & Donations Section -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm section-card animate__animated animate__fadeIn" style="animation-delay: 0.8s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Partners & Donations</h5>
                        <a href="{{ path('app_partners_index') }}" class="btn btn-sm btn-primary">
                            <i class="ri-team-line me-1"></i> Manage Partners
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ partners|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Total Partners</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ totalDonationAmount|default(0)|number_format(2) }} TND</h3>
                                    <p class="text-muted mb-0">Total Donations</p>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <canvas id="donationChart"></canvas>
                        </div>

                        <div class="d-grid gap-2 mt-3">
                            <a href="{{ path('app_back_donation_index') }}" class="btn btn-outline-primary">
                                <i class="ri-hand-heart-line me-1"></i> View Donations
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products and Commands Section -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm section-card animate__animated animate__fadeIn" style="animation-delay: 0.9s">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Products & Orders</h5>
                        <div>
                            <a href="{{ path('app_product_index') }}" class="btn btn-sm btn-primary me-2">
                                <i class="ri-shopping-bag-line me-1"></i> Products
                            </a>
                            <a href="{{ path('app_command_index') }}" class="btn btn-sm btn-primary">
                                <i class="ri-shopping-cart-line me-1"></i> Orders
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ products|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Products</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ commands|default([])|length }}</h3>
                                    <p class="text-muted mb-0">Orders</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h3 class="mb-1">{{ totalSalesAmount|default(0)|number_format(2) }} TND</h3>
                                    <p class="text-muted mb-0">Total Sales</p>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-3">Recent Orders</h6>
                        <div class="table-responsive">
                            <table class="table table-hover dashboard-table">
                                <thead>
                                    <tr>
                                        <th>Order Ref</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in recentOrders|default([])|slice(0, 3) %}
                                        <tr>
                                            <td>
                                                <a href="{{ path('app_command_show', {'id': order.id}) }}" class="text-decoration-none">
                                                    #{{ order.createAt ? order.createAt|date('Ymd') : '' }}-{{ order.id }}
                                                </a>
                                            </td>
                                            <td>{{ order.createAt ? order.createAt|date('M d, Y') : '' }}</td>
                                            <td>{{ order.totalAmount|number_format(2) }} TND</td>
                                            <td>
                                                <span class="badge bg-{{ order.status == 'completed' ? 'success' : (order.status == 'cancelled' ? 'danger' : 'warning') }} rounded-pill">
                                                    {{ order.status|capitalize }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // User Role Chart
            const userRoleCtx = document.getElementById('userRoleChart').getContext('2d');
            new Chart(userRoleCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Administrators', 'Regular Users'],
                    datasets: [{
                        data: [{{ adminCount|default(0) }}, {{ regularCount|default(0) }}, {{ partnerCount|default(0) }}],
                        backgroundColor: ['#dc3545', '#0d6efd', '#ffc107'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    }
                }
            });

            // Forum Activity Chart
            const forumActivityCtx = document.getElementById('forumActivityChart').getContext('2d');
            new Chart(forumActivityCtx, {
                type: 'bar',
                data: {
                    labels: ['Forums', 'Comments', 'Today\'s Posts'],
                    datasets: [{
                        label: 'Count',
                        data: [{{ forums|default([])|length }}, {{ comments|default([])|length }}, {{ todayForums|default([])|length }}],
                        backgroundColor: ['rgba(13, 110, 253, 0.7)', 'rgba(25, 135, 84, 0.7)', 'rgba(255, 193, 7, 0.7)'],
                        borderColor: ['#0d6efd', '#198754', '#ffc107'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });

            // Challenge Status Chart
            const challengeStatusCtx = document.getElementById('challengeStatusChart').getContext('2d');
            new Chart(challengeStatusCtx, {
                type: 'pie',
                data: {
                    labels: ['Active', 'Inactive'],
                    datasets: [{
                        data: [{{ activeCount|default(0) }}, {{ inactiveCount|default(0) }}],
                        backgroundColor: ['#198754', '#6c757d'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        }
                    }
                }
            });

            // Donation Chart
            const donationCtx = document.getElementById('donationChart').getContext('2d');
            new Chart(donationCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Donations (TND)',
                        data: [1500, 2300, 3200, 2800, 4100, 3700],
                        fill: true,
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderColor: '#0d6efd',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
{% endblock %}