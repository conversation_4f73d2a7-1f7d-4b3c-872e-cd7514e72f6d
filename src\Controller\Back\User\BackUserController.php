<?php

namespace App\Controller\Back\User;

use App\Entity\User;
use App\Form\UserEditType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;

// excel
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;

// pdf
use Dompdf\Dompdf;
use Dompdf\Options;



#[Route('/admin/users', name: 'back_admin_users_')]
#[IsGranted('ROLE_ADMIN', message: 'You need admin privileges to access this area.')]
class BackUserController extends AbstractController
{
    // #[Route('/', name: 'index')]
    // public function index(EntityManagerInterface $entityManager): Response
    // {
    //     $users = $entityManager->getRepository(User::class)->findAll();

    //     return $this->render('back/pages/users/index.html.twig', [
    //         'users' => $users
    //     ]);
    // }
    #[Route('/', name: 'index')]
    public function index(Request $request, EntityManagerInterface $entityManager): Response
    {
        $search = $request->query->get('search');
        $sortField = $request->query->get('sort', 'id'); // Default sorting by ID
        $sortOrder = $request->query->get('order', 'ASC'); // Default order

        $queryBuilder = $entityManager->getRepository(User::class)->createQueryBuilder('u');

        // Search filter
        if (!empty($search)) {
            $queryBuilder
                ->where('u.full_name LIKE :search OR u.email LIKE :search')
                ->setParameter('search', '%' . $search . '%');
        }

        // Sorting
        if (in_array($sortField, ['id', 'full_name', 'email', 'role', 'created_at', 'banned']) && in_array($sortOrder, ['ASC', 'DESC'])) {
            $queryBuilder->orderBy('u.' . $sortField, $sortOrder);
        }

        $users = $queryBuilder->getQuery()->getResult();

        return $this->render('back/pages/users/index.html.twig', [
            'users' => $users,
            'search' => $search,
            'sort' => $sortField,
            'order' => $sortOrder
        ]);
    }


    #[Route('/edit/{id}', name: 'edit')]
    public function edit(
        Request $request, 
        EntityManagerInterface $entityManager,
        ?User $user
    ): Response {
        // Check if user exists
        if (!$user) {
            $this->addFlash('error', 'User not found');
            return $this->redirectToRoute('back_admin_users_index');
        }

        // Check if current user is trying to edit themselves
        if ($user === $this->getUser()) {
            $this->addFlash('error', 'Please use the profile section to edit your own account');
            return $this->redirectToRoute('back_admin_users_index');
        }

        $form = $this->createForm(UserEditType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $entityManager->flush();
                $this->addFlash('success', 'User updated successfully!');
                return $this->redirectToRoute('back_admin_users_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating user: ' . $e->getMessage());
            }
        }

        return $this->render('back/pages/users/edit.html.twig', [
            'form' => $form->createView(),
            'user' => $user
        ]);
    }

    #[Route('/delete/{id}', name: 'delete', methods: ['POST'])]
    public function delete(
        Request $request,
        User $user,
        EntityManagerInterface $entityManager,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response {
        // Prevent admin from deleting themselves
        if ($user === $this->getUser()) {
            $this->addFlash('error', 'You cannot delete your own account.');
            return $this->redirectToRoute('back_admin_users_index');
        }

        // Verify CSRF token
        $token = $request->request->get('_token');
        if (!$csrfTokenManager->isTokenValid(new \Symfony\Component\Security\Csrf\CsrfToken('delete'.$user->getId(), $token))) {
            $this->addFlash('error', 'Invalid CSRF token.');
            return $this->redirectToRoute('back_admin_users_index');
        }

        try {
            // Remove the user
            $entityManager->remove($user);
            $entityManager->flush();

            $this->addFlash('success', 'User deleted successfully.');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Error deleting user: ' . $e->getMessage());
        }

        return $this->redirectToRoute('back_admin_users_index');
    }

    #[Route('/toggle-ban/{id}', name: 'toggle_ban', methods: ['POST'])]
    public function toggleBan(
        Request $request,
        User $user,
        EntityManagerInterface $entityManager
    ): Response {
        if ($user === $this->getUser()) {
            $this->addFlash('error', 'You cannot ban yourself');
            return $this->redirectToRoute('back_admin_users_index');
        }

        // Verify CSRF token
        if (!$this->isCsrfTokenValid('toggle_ban'.$user->getId(), $request->request->get('_token'))) {
            $this->addFlash('error', 'Invalid CSRF token');
            return $this->redirectToRoute('back_admin_users_index');
        }

        try {
            $user->setBanned(!$user->isBanned());
            $entityManager->flush();

            $this->addFlash(
                'success',
                sprintf(
                    'User "%s" has been %s successfully',
                    $user->getFullName(),
                    $user->isBanned() ? 'unbanned' : 'banned'
                )
            );
        } catch (\Exception $e) {
            $this->addFlash('error', 'An error occurred while updating user status');
        }

        return $this->redirectToRoute('back_admin_users_index');
    }

    #[Route('/export', name: 'export')]
    public function export(EntityManagerInterface $entityManager): StreamedResponse
    {
        $users = $entityManager->getRepository(User::class)->findAll();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Full Name');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Role');
        $sheet->setCellValue('E1', 'Created At');
        $sheet->setCellValue('F1', 'Status');

        // Fill data
        $row = 2;
        foreach ($users as $user) {
            $sheet->setCellValue('A' . $row, $user->getId());
            $sheet->setCellValue('B' . $row, $user->getFullName());
            $sheet->setCellValue('C' . $row, $user->getEmail());
            $sheet->setCellValue('D' . $row, str_replace('ROLE_', '', $user->getRole()));
            $sheet->setCellValue('E' . $row, $user->getCreatedAt()->format('Y-m-d H:i'));
            $sheet->setCellValue('F' . $row, $user->isBanned() ? 'Banned' : 'Active');
            $row++;
        }

        // Prepare response
        $response = new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        });

        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="users.xlsx"');

        return $response;
    }

    #[Route('/export/users/pdf', name: 'export_users_pdf')]
    public function exportUsersToPDF(EntityManagerInterface $entityManager): Response
    {
        // Fetch all users from the database
        $users = $entityManager->getRepository(User::class)->findAll();

        // Create HTML content for the PDF
        $html = $this->renderView('back/pages/users/users_pdf.html.twig', [
            'users' => $users
        ]);

        // Configure DomPDF
        $pdfOptions = new Options();
        $pdfOptions->set('defaultFont', 'Arial');

        $dompdf = new Dompdf($pdfOptions);
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        // Generate the PDF response
        return new Response(
            $dompdf->output(),
            200,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="users.pdf"'
            ]
        );
    }

} 