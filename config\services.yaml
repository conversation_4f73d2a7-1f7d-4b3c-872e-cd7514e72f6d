# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    products_images_directory: 'C:\xampp\htdocs\img'
    partners_logos_directory: 'C:\xampp\htdocs\images'
    challenges_directory: 'C:\xampp\htdocs\img'
    users_directory: 'C:\xampp\htdocs\img\profiles'
    cal_api_key: 'cal_live_3e273a9233e02ec67b96ab936d0a9f91'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\EventListener\ProfileSetupListener:
        tags:
            - { name: kernel.event_listener, event: kernel.request }

    App\EventListener\UserBannedListener:
        tags:
            - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }

    App\Service\CalendarService:
        arguments:
            $calApiKey: '%cal_api_key%'

    App\Service\TagManager:
        arguments:
            $projectDir: '%kernel.project_dir%'
