<?php

namespace App\Controller\Back\Auth;

use App\Dto\LoginDTO;
use App\Entity\User;
use App\Form\ResetPasswordType;
use App\Form\UserType;
use App\Form\LoginType;
use App\Form\ProfileAuthSetUpType;
use App\Service\FaceRecognitionService;
use App\Service\SendEmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;

#[Route('/auth', name: 'back_auth_')]
class AuthController extends AbstractController
{
    public function __construct(
        private CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    #[Route('/signup', name: 'register')]
    public function register(
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager
    ): Response {
        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('app_home');
        }

        $user = new User();
        $form = $this->createForm(UserType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $hashedPassword = $passwordHasher->hashPassword(
                    $user,
                    $form->get('password')->getData()
                );
                $user->setPassword($hashedPassword);

                $user->setRole('user');
                $user->setAuthMethod('econet');
                $user->setVerified(false);
                $user->setBanned(false);
                $user->setCreatedAt(new \DateTimeImmutable());
                $user->setImage('');
                $user->setAccountSetup(true);

                $entityManager->persist($user);
                $entityManager->flush();

                $this->addFlash('success', 'Your account has been created! Please check your email to verify your account.');
                return $this->redirectToRoute('back_auth_login');
            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while creating your account. Please try again.');
            }
        } elseif ($form->isSubmitted()) {
            foreach ($form->getErrors(true) as $error) {
                $this->addFlash('error', $error->getMessage());
            }
        }

        return $this->render('back/pages/auth/register/register.html.twig', [
            'registrationForm' => $form->createView(),
        ]);
    }

    #[Route('/login', name: 'login')]
    public function login(
        Request $request,
        AuthenticationUtils $authenticationUtils
    ): Response {
        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            /** @var User $user */
            $user = $this->getUser();
            if (!$user->isAccountSetup()) {
                return $this->redirectToRoute('back_auth_profile');
            }
            return $this->redirectToRoute('app_home');
        }

        $loginDTO = new LoginDTO();
        $form = $this->createForm(LoginType::class, $loginDTO);

        $lastUsername = $authenticationUtils->getLastUsername();
        if ($lastUsername) {
            $loginDTO->email = $lastUsername;
        }

        return $this->render('back/pages/auth/login/login.html.twig', [
            'form' => $form->createView(),
            'error' => $authenticationUtils->getLastAuthenticationError(),
            'last_username' => $lastUsername,
        ]);
    }

    #[Route('/profile', name: 'profile')]
    public function profile(Request $request): Response
    {
        if (!$this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('back_auth_login');
        }

        /** @var User $user */
        $user = $this->getUser();

        // If account is already set up, redirect to home
        if ($user->isAccountSetup()) {
            return $this->redirectToRoute('app_home');
        }

        $form = $this->createForm(ProfileAuthSetUpType::class, $user);

        return $this->render('back/pages/auth/profile/profile.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/profile/update', name: 'profile_update', methods: ['POST'])]
    public function profileUpdate(
        Request $request,
        EntityManagerInterface $entityManager,
        UserPasswordHasherInterface $passwordHasher
    ): Response {
        if (!$this->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirectToRoute('back_auth_login');
        }

        /** @var User $user */
        $user = $this->getUser();
        $form = $this->createForm(ProfileAuthSetUpType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                if ($newPassword = $form->get('newPassword')->getData()) {
                    $hashedPassword = $passwordHasher->hashPassword($user, $newPassword);
                    $user->setPassword($hashedPassword);
                }

                $user->setAccountSetup(true);

                $entityManager->flush();
                $this->addFlash('success', 'Profile updated successfully');

                return $this->redirectToRoute('app_home');
            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while updating your profile');
            }
        } elseif ($form->isSubmitted()) {
            foreach ($form->getErrors(true) as $error) {
                $this->addFlash('error', $error->getMessage());
            }
        }

        return $this->redirectToRoute('back_auth_profile');
    }

    #[Route('/oauth/google', name: 'oauth_google')]
    public function connectGoogle(ClientRegistry $clientRegistry): Response
    {
        return $clientRegistry->getClient('google')->redirect([
            'email',
            'profile'
        ]);
    }

    #[Route('/oauth/google/check', name: 'oauth_google_check')]
    public function connectGoogleCheck(): Response
    {
        return new Response();
    }

    #[Route('/oauth/github', name: 'oauth_github')]
    public function connectGithub(ClientRegistry $clientRegistry): Response
    {
        return $clientRegistry->getClient('github')->redirect([
            'user:email',
            'read:user'
        ]);
    }

    #[Route('/oauth/github/check', name: 'oauth_github_check')]
    public function connectGithubCheck(): Response
    {
        return new Response();
    }

    #[Route('/forgot-password', name: 'forgot_password')]
    public function forgotPassword(Request $request, SendEmailService $sendEmailService, EntityManagerInterface $entityManager): Response
    {
        $message = '';
        $error = null;

        if ($request->isMethod('POST')) {
            try {
                $email = $request->request->get('email');
                // Fetch the user by email
                $user = $entityManager->getRepository(User::class)->findOneBy(['email' => $email]);

                if (!$user) {
                    $error = 'No account found with this email address.';
                } else {
                    // Generate a secure token
                    $resetToken = bin2hex(random_bytes(32));

                    // Update user entity
                    $user->setResetToken($resetToken);
                    $user->setResetTokenCreatedAt(new \DateTime());

                    // Persist changes
                    $entityManager->persist($user);
                    $entityManager->flush();

                    // Send the email with the token
                    $message = $sendEmailService->sendEmail($email, 'reset_password', $resetToken);

                    if (strpos($message, 'Error') !== false) {
                        $error = 'Failed to send reset email. Please try again later.';
                    } else {
                        $this->addFlash('success', 'If an account exists with this email, you will receive a password reset link shortly.');
                        return $this->redirectToRoute('back_auth_forgot_password');
                    }
                }
            } catch (\Exception $e) {
                $error = 'An unexpected error occurred. Please try again later.';
            }
        }

        return $this->render('back/pages/auth/forgot_password/forgot_password.html.twig', [
            'message' => $message,
            'error' => $error
        ]);
    }

    #[Route('/reset-password/{token}', name: 'reset_password')]
    public function resetPassword(
        string $token,
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager
    ): Response {
        // Find user by token
        $user = $entityManager->getRepository(User::class)->findOneBy(['resetToken' => $token]);

        if (!$user) {
            $this->addFlash('error', 'Invalid reset token');
            return $this->redirectToRoute('back_auth_login');
        }

        // Check if token is expired (1 hour)
        $tokenCreatedAt = $user->getResetTokenCreatedAt();
        if (!$tokenCreatedAt || $tokenCreatedAt->modify('+1 hour') < new \DateTime()) {
            $this->addFlash('error', 'Reset token has expired');
            return $this->redirectToRoute('back_auth_login');
        }

        // Create and handle form
        $form = $this->createForm(ResetPasswordType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Hash the new password
            $hashedPassword = $passwordHasher->hashPassword(
                $user,
                $form->get('password')->getData()
            );

            // Update the user
            $user->setPassword($hashedPassword);
            $user->setResetToken(null);
            $user->setResetTokenCreatedAt(null);

            $entityManager->flush();

            $this->addFlash('success', 'Your password has been reset successfully');
            return $this->redirectToRoute('back_auth_login');
        }

        return $this->render('back/pages/auth/reset_password/reset_password.html.twig', [
            'resetForm' => $form->createView(),
        ]);
    }

    #[Route('/detect-face', name: 'detect_face_login')]
    public function detectFaceLogin(Request $request, FaceRecognitionService $faceRecognitionService, EntityManagerInterface $entityManager): Response
    {
        $message = '';
        $error = null;

        try {
            $email = $request->request->get('email');
            // Fetch the user by email
            $user = $entityManager->getRepository(User::class)->findOneBy(['email' => $email]);

            // Send the email with the token
            $user_detect_output = $faceRecognitionService->detectFace();

            if ($user_detect_output == "closed") { // closed
                return $this->redirectToRoute('back_auth_login');
            } else if (strpos($user_detect_output, "detected :") === 0) {
                $detected_user_id = $faceRecognitionService->extract_id($user_detect_output);
                
                // Get the user by detected face ID
                $detected_user = $entityManager->getRepository(User::class)->find($detected_user_id);
                
                if (!$detected_user || !$detected_user->getFaceId()) {
                    $this->addFlash('error', 'Face ID not registered for this user.');
                    return $this->redirectToRoute('back_auth_login');
                }

                if ($user && $user->getId() !== $detected_user->getId()) {
                    $this->addFlash('error', 'Face ID does not match the provided email.');
                    return $this->redirectToRoute('back_auth_login');
                }

                // Log the user in
                $token = new UsernamePasswordToken(
                    $detected_user,
                    'main',
                    $detected_user->getRoles()
                );

                $this->container->get('security.token_storage')->setToken($token);
                $this->container->get('session')->set('_security_main', serialize($token));

                // Check if account setup is needed
                if (!$detected_user->isAccountSetup()) {
                    return $this->redirectToRoute('back_auth_profile');
                }

                return $this->redirectToRoute('app_home');
            } else {
                $this->addFlash('error', 'Error from python: ' . $user_detect_output);
                return $this->redirectToRoute('back_auth_login');
            }


        } catch (\Exception $e) {
            $error = 'An unexpected error occurred. Please try again later.';
            return $this->redirectToRoute('back_auth_login');
        }

    }

    #[Route('/logout', name: 'logout')]
    public function logout(): void
    {
    }
}
