{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Command Management</h1>
      <p class="text-muted">Manage and monitor all customer orders in the system</p>
    </div>
  </div>

  {% for message in app.flashes('success') %}
    <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
      <i class="ri-checkbox-circle-line me-2"></i> {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endfor %}

  {% for message in app.flashes('error') %}
    <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
      <i class="ri-error-warning-line me-2"></i> {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endfor %}

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-md-3 mb-4 mb-md-0">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
              <i class="ri-shopping-cart-line text-primary fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Total Orders</h6>
              <h3 class="mb-0">{{ commands|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-4 mb-md-0">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
              <i class="ri-check-line text-success fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Completed Orders</h6>
              <h3 class="mb-0">{{ commands|filter(c => c.status == 'completed')|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-4 mb-md-0">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
              <i class="ri-time-line text-warning fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Pending Orders</h6>
              <h3 class="mb-0">{{ commands|filter(c => c.status == 'pending')|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-info-subtle rounded-3 p-3 me-3">
              <i class="ri-money-dollar-circle-line text-info fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Total Revenue</h6>
              <h3 class="mb-0">{{ commands|reduce((sum, command) => sum + command.totalAmount, 0)|number_format(2, '.', ',') }} DT</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Include html2pdf.js from a CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

  <!-- Styles for print and PDF -->
  <style>
    @media print {
      .noPdf {
        display: none !important;
      }
    }

    /* Special styles for PDF output */
    .pdf-table {
      width: 100%;
      font-size: 12px; /* Larger font for better readability */
      table-layout: fixed;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    .pdf-table th {
      background-color: #4e73df !important;
      color: white !important;
      font-weight: bold;
      text-align: left;
      padding: 10px;
      border: 1px solid #e3e6f0;
      font-size: 13px; /* Slightly larger headers */
    }

    .pdf-table td {
      padding: 10px;
      border: 1px solid #e3e6f0;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      vertical-align: middle;
    }

    .pdf-table tr:nth-child(even) {
      background-color: #f8f9fc;
    }

    /* Ensure text is clearly visible */
    .pdf-table td, .pdf-table th {
      line-height: 1.4;
      letter-spacing: 0.01em;
    }

    /* Stats Card Styles */
    .stats-card {
      border-radius: 0.75rem;
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-5px);
    }

    .stats-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Status Badge Styles */
    .status-badge {
      padding: 0.35em 0.65em;
      font-size: 0.75em;
      font-weight: 700;
      border-radius: 0.25rem;
      text-transform: capitalize;
    }

    .status-pending {
      background-color: #0d6efd;
      color: #fff;
    }

    .status-processing {
      background-color: #fd7e14;
      color: #fff;
    }

    .status-completed {
      background-color: #198754;
      color: #fff;
    }

    .status-cancelled {
      background-color: #dc3545;
      color: #fff;
    }

    /* Empty State Styles */
    .empty-state {
      padding: 2rem;
      text-align: center;
    }

    .empty-state-icon {
      font-size: 3rem;
      color: #d1d5db;
      margin-bottom: 1rem;
    }
  </style>

  <div class="row mb-4">
    <div class="col-md-8">
      <!-- Chart.js (Pie Chart) -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.4s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Orders by Status</h5>
        </div>
        <div class="card-body">
          <canvas id="statusPieChart" height="300"></canvas>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.5s">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 class="mb-0 fw-bold">Actions</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button class="btn btn-primary btn-block w-100 py-2" id="downloadButton">
              <i class="ri-file-download-line me-1"></i> Download PDF Report
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Chart code
      let statusCounts = {
        'pending': 0,
        'processing': 0,
        'completed': 0,
        'cancelled': 0
      };

      {% for command in commands %}
        var status = "{{ command.status|e('js') }}";
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      {% endfor %}

      const labels = Object.keys(statusCounts);
      const data = Object.values(statusCounts);
      const colors = {
        'pending': '#0d6efd',    /* Bright blue - matches the Pending button */
        'processing': '#fd7e14',  /* Orange - matches the Processing text */
        'completed': '#198754',  /* Green - matches the Completed checkmark */
        'cancelled': '#dc3545'   /* Red - matches the Cancelled X */
      };

      const backgroundColor = labels.map(label => colors[label]);

      const ctx = document.getElementById('statusPieChart').getContext('2d');
      new Chart(ctx, {
        type: 'pie',
        data: {
          labels: labels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
          datasets: [{
            data: data,
            backgroundColor: backgroundColor
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              labels: {
                padding: 20,
                usePointStyle: true,
                pointStyle: 'circle'
              }
            }
          },
          cutout: '0%',
          borderWidth: 0
        }
      });

      // PDF download handler
      document.getElementById('downloadButton').addEventListener('click', function() {
        // Show loading indicator
        const loadingMessage = document.createElement('div');
        loadingMessage.innerHTML = '<div class="alert alert-info">Generating PDF, please wait...</div>';
        document.querySelector('.container-fluid').prepend(loadingMessage);

        // Create a PDF title and header
        const pdfHeader = document.createElement('div');
        pdfHeader.innerHTML = `
          <div style="text-align: center; margin-bottom: 25px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-bottom: 3px solid #4e73df;">
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
              <div style="font-size: 24px; margin-right: 10px; color: #4e73df;">
                <i class="ri-shopping-cart-line"></i>
              </div>
              <h2 style="margin: 0; color: #4e73df; font-size: 24px; font-weight: 700;">Command Management Report</h2>
            </div>
            <p style="margin: 5px 0 0; color: #6c757d; font-size: 14px;">
              Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
            </p>
            <p style="margin: 5px 0 0; color: #6c757d; font-size: 14px;">
              eco.net - Order Management System
            </p>
          </div>
        `;

        // Create a clone of the content to avoid modifying the original
        const contentClone = document.getElementById('pdfContent').cloneNode(true);

        // Add the header to the clone
        contentClone.insertBefore(pdfHeader, contentClone.firstChild);

        // Remove action buttons from the clone
        const actionButtons = contentClone.querySelectorAll('.noPdf');
        actionButtons.forEach(el => {
          el.style.display = 'none';
        });

        // Apply PDF-specific class to the table for styling
        const table = contentClone.querySelector('table');
        table.classList.add('pdf-table');

        // Add statistics to the PDF
        const statsSection = document.createElement('div');
        statsSection.style.marginBottom = '20px';
        statsSection.style.padding = '10px';
        statsSection.style.backgroundColor = '#f8f9fa';
        statsSection.style.borderRadius = '5px';

        statsSection.innerHTML = `
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div style="text-align: center; padding: 15px; background-color: #e8f4fc; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #4e73df; font-size: 16px; font-weight: 600;">Total Orders</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(1) h3').textContent}</p>
            </div>
            <div style="text-align: center; padding: 15px; background-color: #e8fcf8; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #1cc88a; font-size: 16px; font-weight: 600;">Completed Orders</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(2) h3').textContent}</p>
            </div>
            <div style="text-align: center; padding: 15px; background-color: #fff8e8; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #f6c23e; font-size: 16px; font-weight: 600;">Pending Orders</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(3) h3').textContent}</p>
            </div>
            <div style="text-align: center; padding: 15px; background-color: #e8f8fc; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #36b9cc; font-size: 16px; font-weight: 600;">Total Revenue</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(4) h3').textContent}</p>
            </div>
          </div>
        `;

        contentClone.insertBefore(statsSection, contentClone.querySelector('.table-responsive'));

        // html2pdf configuration with landscape orientation and margins
        const opt = {
          margin: [15, 10, 15, 10],
          filename: 'command_management_report.pdf',
          image: { type: 'jpeg', quality: 1.0 },
          html2canvas: {
            scale: 3, // Higher scale for better quality
            useCORS: true,
            allowTaint: true, // Allow tainted canvas for better image handling
            logging: false, // Disable logging for better performance
            letterRendering: true,
            imageTimeout: 5000, // Longer timeout for images
            backgroundColor: '#ffffff' // Ensure white background
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'landscape',
            compress: true,
            precision: 16 // Higher precision for better text rendering
          }
        };

        // Generate the PDF
        if (typeof html2pdf !== 'undefined') {
          html2pdf().set(opt).from(contentClone).save().then(() => {
            // Remove the loading message
            loadingMessage.remove();
          }).catch(err => {
            console.error('PDF generation error:', err);
            loadingMessage.innerHTML = '<div class="alert alert-danger">PDF generation failed. Please try again.</div>';
            setTimeout(() => loadingMessage.remove(), 3000);
          });
        } else {
          console.error('html2pdf library not loaded');
          loadingMessage.innerHTML = '<div class="alert alert-danger">PDF library not loaded. Please refresh and try again.</div>';
          setTimeout(() => loadingMessage.remove(), 3000);
        }
      });
    });
  </script>

  <!-- Search and Filter Section -->
  <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.6s">
    <div class="card-header bg-white py-3">
      <h5 class="mb-0 fw-bold">Order List</h5>
    </div>
    <div class="card-body">
      <div class="row mb-4">
        <div class="col-md-8">
          <form method="get" class="mb-0">
            <div class="input-group">
              <span class="input-group-text bg-light border-end-0">
                <i class="ri-search-line"></i>
              </span>
              <input type="text" name="search" value="{{ search }}" class="form-control border-start-0" placeholder="Search by customer name, delivery address or notes...">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i> Search
              </button>
            </div>
          </form>
        </div>
        <div class="col-md-4 d-flex justify-content-end align-items-center mt-3 mt-md-0">
          <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="ri-filter-3-line me-1"></i> Filter
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><h6 class="dropdown-header">Filter by Status</h6></li>
              <li><a class="dropdown-item" href="?status=pending">Pending</a></li>
              <li><a class="dropdown-item" href="?status=processing">Processing</a></li>
              <li><a class="dropdown-item" href="?status=completed">Completed</a></li>
              <li><a class="dropdown-item" href="?status=cancelled">Cancelled</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="{{ path('app_command_index') }}">Clear Filters</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- The content we want to include in the PDF -->
      <div id="pdfContent">
        <div class="table-responsive">
          <table class="table table-hover align-middle mb-0">
            <thead class="table-light">
              <tr>
                <th scope="col">Order Reference</th>
                <th scope="col">Customer</th>
                <th scope="col">Created At</th>
                <th scope="col">Status</th>
                <th scope="col">Total Amount</th>
                <th scope="col">Delivery Address</th>
                <th scope="col">Notes</th>
                <th scope="col" class="noPdf text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for command in commands %}
                <tr class="align-middle">
                  <td>
                    <div class="fw-semibold">Order #{{ command.createAt ? command.createAt|date('Ymd') : '' }}-{{ loop.index }}</div>
                  </td>
                  <td>
                    <div class="fw-semibold">{{ userNames[command.idUser] }}</div>
                  </td>
                  <td>
                    <div>{{ command.createAt ? command.createAt|date('Y-m-d') : '' }}</div>
                    <small class="text-muted">{{ command.createAt ? command.createAt|date('H:i:s') : '' }}</small>
                  </td>
                  <td>
                    <!-- Status badge and dropdown for updating -->
                    <div class="d-flex align-items-center">
                      <span class="status-badge status-{{ command.status }}">{{ command.status }}</span>
                      <div class="dropdown ms-2">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                          <i class="ri-edit-line"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li><h6 class="dropdown-header">Update Status</h6></li>
                          <li>
                            <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post">
                              <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
                              <input type="hidden" name="status" value="pending">
                              <button type="submit" class="dropdown-item {% if command.status == 'pending' %}active{% endif %}">
                                <i class="ri-time-line me-2" style="color: #0d6efd;"></i> Pending
                              </button>
                            </form>
                          </li>
                          <li>
                            <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post">
                              <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
                              <input type="hidden" name="status" value="processing">
                              <button type="submit" class="dropdown-item {% if command.status == 'processing' %}active{% endif %}">
                                <i class="ri-loader-4-line me-2" style="color: #fd7e14;"></i> Processing
                              </button>
                            </form>
                          </li>
                          <li>
                            <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post">
                              <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
                              <input type="hidden" name="status" value="completed">
                              <button type="submit" class="dropdown-item {% if command.status == 'completed' %}active{% endif %}">
                                <i class="ri-check-line me-2" style="color: #198754;"></i> Completed
                              </button>
                            </form>
                          </li>
                          <li>
                            <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post">
                              <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
                              <input type="hidden" name="status" value="cancelled">
                              <button type="submit" class="dropdown-item {% if command.status == 'cancelled' %}active{% endif %}">
                                <i class="ri-close-line me-2" style="color: #dc3545;"></i> Cancelled
                              </button>
                            </form>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </td>
                  <td>
                    <span class="fw-semibold">{{ command.totalAmount }} DT</span>
                  </td>
                  <td>
                    <div class="text-truncate" style="max-width: 200px;" title="{{ command.deliveryAddress }}">
                      {{ command.deliveryAddress|length > 30 ? command.deliveryAddress|slice(0, 30) ~ '...' : command.deliveryAddress }}
                    </div>
                  </td>
                  <td>
                    <div class="text-truncate" style="max-width: 200px;" title="{{ command.notes }}">
                      {{ command.notes|length > 30 ? command.notes|slice(0, 30) ~ '...' : command.notes }}
                    </div>
                  </td>
                  <td class="noPdf text-end">
                    <div class="d-flex justify-content-end gap-2">
                      <a href="{{ path('app_command_show', {'id': command.id}) }}"
                         class="btn btn-sm btn-outline-primary"
                         data-bs-toggle="tooltip"
                         data-bs-placement="top"
                         title="View Details">
                        <i class="ri-eye-line"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% else %}
                <tr>
                  <td colspan="7" class="text-center py-5">
                    <div class="empty-state">
                      <i class="ri-shopping-cart-line empty-state-icon"></i>
                      <h5>No orders found</h5>
                      <p class="text-muted">There are no orders in the system yet</p>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add animation to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
      card.classList.add('animate__animated', 'animate__fadeIn');
      card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add hover effect to table rows
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
      row.addEventListener('mouseenter', function() {
        this.style.cursor = 'pointer';
      });
    });
  });
</script>
{% endblock %}
