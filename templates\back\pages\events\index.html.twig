{% extends 'back/base.html.twig' %}

{% block title %}Event Management{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0 text-gray-800">Event Management</h1>
            <p class="text-muted">Manage and organize all eco.net events</p>
        </div>
        <div class="col-auto">
            <a href="{{ path('app_admin_event_new') }}" class="btn btn-primary rounded-pill">
                <i class="ri-add-line me-1"></i> Create Event
            </a>
        </div>
    </div>

    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="ri-checkbox-circle-line me-2"></i> {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    <!-- Event Statistics Cards -->
    <div class="row mb-4">
        <!-- Total Events Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                            <i class="ri-calendar-event-line text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted">Total Events</h6>
                            <h3 class="mb-0">{{ events|length }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upcoming Events Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-danger-subtle rounded-3 p-3 me-3">
                            <i class="ri-calendar-check-line text-danger fs-4"></i>
                        </div>
                        <div>
                            {% set upcomingCount = 0 %}
                            {% set now = "now"|date('Y-m-d H:i:s') %}
                            {% for event in events %}
                                {% if event.date|date('Y-m-d H:i:s') > now %}
                                    {% set upcomingCount = upcomingCount + 1 %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Upcoming Events</h6>
                            <h3 class="mb-0">{{ upcomingCount }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Past Events Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                            <i class="ri-history-line text-success fs-4"></i>
                        </div>
                        <div>
                            {% set pastCount = 0 %}
                            {% for event in events %}
                                {% if event.date|date('Y-m-d H:i:s') < now %}
                                    {% set pastCount = pastCount + 1 %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Past Events</h6>
                            <h3 class="mb-0">{{ pastCount }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Participants Card -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
                            <i class="ri-user-line text-warning fs-4"></i>
                        </div>
                        <div>
                            {% set totalParticipants = 0 %}
                            {% for event in events %}
                                {% if event.maxParticipants %}
                                    {% set totalParticipants = totalParticipants + event.maxParticipants %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Total Capacity</h6>
                            <h3 class="mb-0">{{ totalParticipants }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow border-0 mb-4">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0 fw-bold">Events List</h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" id="eventSearch" class="form-control" placeholder="Search events...">
                        <span class="input-group-text bg-primary text-white">
                            <i class="ri-search-line"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle border-0" id="eventsTable">
                    <thead class="table-light">
                        <tr>
                            <th>Event Details</th>
                            <th>Location</th>
                            <th>Date & Time</th>
                            <th>Capacity</th>
                            <th>Status</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for event in events %}
                            <tr class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="event-image me-3">
                                            {% if event.imagePath %}
                                                <img src="http://localhost/img/event/{{ event.imagePath }}" alt="{{ event.title }}" class="rounded" width="60" height="60" style="object-fit: cover;">
                                            {% else %}
                                                <div class="event-image-placeholder">
                                                    <i class="ri-image-line"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ event.title }}</h6>
                                            <small class="text-muted">{{ event.description|striptags|slice(0, 50) }}{% if event.description|length > 50 %}...{% endif %}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-map-pin-line text-muted me-2"></i>
                                        {{ event.location }}
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-calendar-line text-muted me-2"></i>
                                        <div>
                                            <div>{{ event.date|date('M d, Y') }}</div>
                                            <small class="text-muted">{{ event.date|date('H:i') }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-user-line text-muted me-2"></i>
                                        <span>{{ event.maxParticipants }} participants</span>
                                    </div>
                                </td>
                                <td>
                                    {% set now = "now"|date('Y-m-d H:i:s') %}
                                    {% if event.date|date('Y-m-d H:i:s') > now %}
                                        <span class="badge bg-success rounded-pill">
                                            <i class="ri-time-line me-1"></i> Upcoming
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary rounded-pill">
                                            <i class="ri-check-line me-1"></i> Completed
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_admin_event_edit', {'id': event.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="Edit Event">
                                            <i class="ri-pencil-line"></i>
                                        </a>
                                        <form method="post" action="{{ path('app_admin_event_delete', {'id': event.id}) }}" class="d-inline delete-form">
                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ event.id) }}">
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger delete-btn"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    data-event-title="{{ event.title }}"
                                                    title="Delete Event">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-calendar-event-line empty-state-icon"></i>
                                        <h5>No events found</h5>
                                        <p class="text-muted">Create your first event to get started</p>
                                        <a href="{{ path('app_admin_event_new') }}" class="btn btn-primary mt-3">
                                            <i class="ri-add-line me-1"></i> Create Event
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-4">
                    <i class="ri-error-warning-line text-danger" style="font-size: 4rem;"></i>
                </div>
                <h5>Are you sure you want to delete this event?</h5>
                <p class="text-muted" id="eventTitleToDelete"></p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete Event</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Stats Card Styles */
.stats-card {
    border-radius: 0.75rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
}

/* Background Subtle Colors */
.bg-primary-subtle {
    background-color: rgba(13, 110, 253, 0.1);
}

.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}

.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}

.bg-info-subtle {
    background-color: rgba(13, 202, 240, 0.1);
}

.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #0dcaf0 !important;
}

/* Event Image Placeholder */
.event-image-placeholder {
    width: 60px;
    height: 60px;
    background-color: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #adb5bd;
    font-size: 24px;
}

/* Empty State Styles */
.empty-state {
    padding: 2rem;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

/* Animation Classes */
.animate__animated {
    animation-duration: 0.5s;
}

.animate__fadeIn {
    animation-name: fadeIn;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add animation to stats cards
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.classList.add('animate__animated', 'animate__fadeIn');
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // Event search functionality
        const searchInput = document.getElementById('eventSearch');
        const table = document.getElementById('eventsTable');
        const rows = table.querySelectorAll('tbody tr');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Delete confirmation modal
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const deleteButtons = document.querySelectorAll('.delete-btn');
        const confirmDeleteButton = document.getElementById('confirmDelete');
        let formToSubmit = null;

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const eventTitle = this.getAttribute('data-event-title');
                document.getElementById('eventTitleToDelete').textContent = eventTitle;
                formToSubmit = this.closest('form');
                deleteModal.show();
            });
        });

        confirmDeleteButton.addEventListener('click', function() {
            if (formToSubmit) {
                formToSubmit.submit();
            }
            deleteModal.hide();
        });
    });
</script>
{% endblock %}
