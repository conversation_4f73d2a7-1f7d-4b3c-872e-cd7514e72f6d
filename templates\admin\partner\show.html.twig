{% extends 'base.html.twig' %}

{% block title %}Détails du partenaire{% endblock %}

{% block body %}
    <div class="container mt-4">
        <h1>Détails du partenaire</h1>

        <div class="card">
            <div class="card-body">
                <table class="table">
                    <tbody>
                        <tr>
                            <th>ID</th>
                            <td>{{ partner.partnerId }}</td>
                        </tr>
                        <tr>
                            <th>Nom</th>
                            <td>{{ partner.name }}</td>
                        </tr>
                        <tr>
                            <th>Type</th>
                            <td>{{ partner.type }}</td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ partner.description }}</td>
                        </tr>
                        <tr>
                            <th>Localisation</th>
                            <td>{{ partner.geoLocation }}</td>
                        </tr>
                    </tbody>
                </table>

                <div class="mt-3">
                    <a href="{{ path('admin_partner_index') }}" class="btn btn-secondary">Retour à la liste</a>
                    <a href="{{ path('admin_partner_edit', {'partner_id': partner.partnerId}) }}" class="btn btn-warning">Modifier</a>
                    
                    <form method="post" action="{{ path('admin_partner_delete', {'partner_id': partner.partnerId}) }}" style="display: inline-block" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce partenaire ?');">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ partner.partnerId) }}">
                        <button class="btn btn-danger">Supprimer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
