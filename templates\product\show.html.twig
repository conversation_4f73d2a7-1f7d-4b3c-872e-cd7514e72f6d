{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col-auto">
      <a href="{{ path('app_product_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Products">
        <i class="ri-arrow-left-line"></i>
      </a>
    </div>
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Product Details</h1>
      <p class="text-muted mb-0">View detailed information about this product</p>
    </div>
    <div class="col-auto">
      <div class="d-flex gap-2">
        <a href="{{ path('app_product_edit', {'id': product.id}) }}" class="btn btn-primary">
          <i class="ri-pencil-line me-1"></i> Edit
        </a>
        <form method="post"
              action="{{ path('app_product_delete', {'id': product.id}) }}"
              class="d-inline"
              onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.');">
          <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ product.id) }}">
          <button type="submit" class="btn btn-outline-danger">
            <i class="ri-delete-bin-line me-1"></i> Delete
          </button>
        </form>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Product Image Column -->
    <div class="col-lg-4 mb-4">
      <div class="card border-0 shadow-sm product-image-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        {% if product.image %}
          <img src="http://localhost/img/{{  product.image }}" class="card-img-top" alt="{{ product.nomP }}" style="max-height: 300px; object-fit: cover;">
        {% else %}
          <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 300px;">
            <i class="ri-image-line" style="font-size: 5rem; color: #d1d5db;"></i>
          </div>
        {% endif %}
        <div class="card-body text-center">
          <h4 class="card-title mb-0">{{ product.nomP }}</h4>
          <p class="text-muted mb-3">{{ product.categorie }}</p>

          <div class="product-badges mb-3">
            {% if product.isEcological %}
              <span class="badge bg-success rounded-pill">
                <i class="ri-leaf-line me-1"></i> Ecological
              </span>
            {% endif %}

            {% if product.stock > 10 %}
              <span class="badge bg-primary rounded-pill">
                <i class="ri-stack-line me-1"></i> In Stock
              </span>
            {% elseif product.stock > 0 %}
              <span class="badge bg-warning rounded-pill">
                <i class="ri-error-warning-line me-1"></i> Low Stock
              </span>
            {% else %}
              <span class="badge bg-danger rounded-pill">
                <i class="ri-close-circle-line me-1"></i> Out of Stock
              </span>
            {% endif %}
          </div>

          <div class="product-price">
            <span class="fs-3 fw-bold">{{ product.price }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Details Column -->
    <div class="col-lg-8">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Product Information</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-12 mb-4">
              <h6 class="text-uppercase text-muted small mb-2">Description</h6>
              <p class="mb-0">{{ product.description }}</p>
            </div>

            <div class="col-md-6 mb-3">
              <h6 class="text-uppercase text-muted small mb-2">Category</h6>
              <p class="mb-0">
                <span class="badge bg-primary-subtle text-primary rounded-pill">{{ product.categorie }}</span>
              </p>
            </div>

            <div class="col-md-6 mb-3">
              <h6 class="text-uppercase text-muted small mb-2">Origin</h6>
              <p class="mb-0">
                <span class="badge bg-info-subtle text-info rounded-pill">{{ product.origin }}</span>
              </p>
            </div>

            <div class="col-md-6 mb-3">
              <h6 class="text-uppercase text-muted small mb-2">Stock</h6>
              <p class="mb-0">
                {% if product.stock > 10 %}
                  <span class="badge bg-success rounded-pill">{{ product.stock }} units</span>
                {% elseif product.stock > 0 %}
                  <span class="badge bg-warning rounded-pill">{{ product.stock }} units</span>
                {% else %}
                  <span class="badge bg-danger rounded-pill">Out of stock</span>
                {% endif %}
              </p>
            </div>

            <div class="col-md-6 mb-3">
              <h6 class="text-uppercase text-muted small mb-2">Ecological</h6>
              <p class="mb-0">
                {% if product.isEcological %}
                  <span class="badge bg-success rounded-pill">
                    <i class="ri-check-line me-1"></i> Yes
                  </span>
                {% else %}
                  <span class="badge bg-danger rounded-pill">
                    <i class="ri-close-line me-1"></i> No
                  </span>
                {% endif %}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Card -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.3s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Actions</h5>
        </div>
        <div class="card-body">
          <div class="d-flex gap-2">
            <a href="{{ path('app_product_index') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line me-1"></i> Back to List
            </a>
            <a href="{{ path('app_product_edit', {'id': product.id}) }}" class="btn btn-primary">
              <i class="ri-pencil-line me-1"></i> Edit
            </a>
            <form method="post"
                  action="{{ path('app_product_delete', {'id': product.id}) }}"
                  class="d-inline"
                  onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.');">
              <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ product.id) }}">
              <button type="submit" class="btn btn-outline-danger">
                <i class="ri-delete-bin-line me-1"></i> Delete
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
  }

  .product-image-card {
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .product-badges .badge {
    margin-right: 0.5rem;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
{% endblock %}
