:root {
    --primary-color: #6BB748;
    --primary-dark: #5a9a3d;
    --secondary-color: #4a6741;
    --success-color: #1cc88a;
    --text-color: #2c3e2d;
    --text-muted: #5c735f;
    --sidebar-width: 260px;
    --header-height: 70px;
    --border-color: #e8f0e9;
    --background-light: #f8faf8;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-light);
    color: var(--text-color);
}

/* Wrapper */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

/* Sidebar */
.sidebar {
    min-width: var(--sidebar-width);
    max-width: var(--sidebar-width);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: #fff;
    transition: all 0.3s;
    min-height: 100vh;
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.05);
}

.sidebar.active {
    margin-left: calc(-1 * var(--sidebar-width));
}

.sidebar .sidebar-header {
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .sidebar-header h3 {
    color: #fff;
    font-size: 1.5rem;
    margin: 0;
    font-weight: 600;
}

.sidebar ul.components {
    padding: 20px 0;
}

.sidebar ul li a {
    padding: 12px 20px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.sidebar ul li a i {
    font-size: 1.25rem;
}

.sidebar ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-left-color: #fff;
}

.sidebar ul li.active > a {
    background: rgba(255, 255, 255, 0.15);
    border-left-color: #fff;
    color: #fff;
}

/* Main Content */
#content {
    width: 100%;
    min-height: 100vh;
    transition: all 0.3s;
    background: var(--background-light);
}

/* Navbar */
.navbar {
    padding: 15px 25px;
    background: #fff;
    border: none;
    border-radius: 0;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

#sidebarCollapse {
    background: transparent;
    border: none;
    color: var(--primary-color);
    font-size: 1.25rem;
    padding: 5px;
    border-radius: 8px;
    transition: all 0.3s;
}

#sidebarCollapse:hover {
    background: var(--background-light);
}

/* Content Area */
.content-area {
    padding: 25px;
}

/* Dropdown Styles */
.dropdown-menu {
    border: none;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    padding: 8px;
}

.dropdown-item {
    padding: 8px 15px;
    color: var(--text-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dropdown-item:hover {
    background-color: var(--background-light);
    color: var(--primary-color);
}

.dropdown-item:active {
    background-color: var(--primary-color);
    color: #fff;
}

.dropdown-divider {
    border-color: var(--border-color);
    margin: 8px 0;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    background: #fff;
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem;
    border-radius: 12px 12px 0 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
    }
    .sidebar.active {
        margin-left: 0;
    }
    .content-area {
        padding: 15px;
    }
}
