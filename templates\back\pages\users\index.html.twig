{% extends 'back/base.html.twig' %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
        <div class="col">
            <h1 class="h3 mb-0 text-gray-800">User Management</h1>
            <p class="text-muted">Manage and monitor all users in the system</p>
        </div>
    </div>

    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="ri-checkbox-circle-line me-2"></i> {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="ri-error-warning-line me-2"></i> {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    <!-- User Statistics Cards -->
    <div class="row mb-4">
        <!-- Total Users Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                            <i class="ri-user-line text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted">Total Users</h6>
                            <h3 class="mb-0">{{ users|length }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Users Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-danger-subtle rounded-3 p-3 me-3">
                            <i class="ri-admin-line text-danger fs-4"></i>
                        </div>
                        <div>
                            {% set adminCount = 0 %}
                            {% for user in users %}
                                {% if user.role == 'ROLE_ADMIN' or user.role == 'admin' %}
                                    {% set adminCount = adminCount + 1 %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Administrators</h6>
                            <h3 class="mb-0">{{ adminCount }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Regular Users Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                            <i class="ri-user-follow-line text-success fs-4"></i>
                        </div>
                        <div>
                            {% set regularCount = 0 %}
                            {% for user in users %}
                                {% if user.role != 'ROLE_ADMIN' and user.role != 'admin' %}
                                    {% set regularCount = regularCount + 1 %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Regular Users</h6>
                            <h3 class="mb-0">{{ regularCount }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Banned Users Card -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
                            <i class="ri-user-forbid-line text-warning fs-4"></i>
                        </div>
                        <div>
                            {% set bannedCount = 0 %}
                            {% for user in users %}
                                {% if user.banned %}
                                    {% set bannedCount = bannedCount + 1 %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Banned Users</h6>
                            <h3 class="mb-0">{{ bannedCount }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <!-- Chart.js (Pie Chart) -->
            <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.4s">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold">Users by Role</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height: 300px;">
                        <canvas id="userRolePieChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.5s">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <div class="dropdown">
                            <button class="btn btn-primary btn-block w-100 py-2 dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="ri-download-line me-1"></i> Export Data
                            </button>
                            <ul class="dropdown-menu w-100" aria-labelledby="exportDropdown">
                                <li>
                                    <a href="{{ path('back_admin_users_export') }}" class="dropdown-item">
                                        <i class="ri-file-excel-2-line me-2 text-success"></i> Export to Excel
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ path('back_admin_users_export_users_pdf') }}" class="dropdown-item">
                                        <i class="ri-file-pdf-line me-2 text-danger"></i> Export to PDF
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.6s">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0 fw-bold">User Management</h5>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-8">
                    <form method="get" class="mb-0">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="ri-search-line"></i>
                            </span>
                            <input type="text" name="search" value="{{ search }}" class="form-control border-start-0" placeholder="Search by name, email or role...">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-search-line me-1"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
                <div class="col-md-4 d-flex justify-content-end align-items-center mt-3 mt-md-0">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="ri-filter-3-line me-1"></i> Filter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">Filter by Role</h6></li>
                            <li><a class="dropdown-item" href="?sort=role&role=admin">Administrators</a></li>
                            <li><a class="dropdown-item" href="?sort=role&role=user">Regular Users</a></li>
                            <li><a class="dropdown-item" href="?sort=role&role=Partner">Partners</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">Filter by Status</h6></li>
                            <li><a class="dropdown-item" href="?sort=banned&banned=0">Active Users</a></li>
                            <li><a class="dropdown-item" href="?sort=banned&banned=1">Banned Users</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ path('back_admin_users_index') }}">Clear Filters</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="table-responsive" id="pdfContent">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <a href="{{ path('back_admin_users_index', { search: search, sort: 'full_name', order: order == 'ASC' ? 'DESC' : 'ASC' }) }}" class="text-decoration-none text-dark d-flex align-items-center">
                                    Full Name
                                    {% if sort == 'full_name' %}
                                        <i class="ri-arrow-{{ order == 'ASC' ? 'up' : 'down' }}-s-line ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ path('back_admin_users_index', { search: search, sort: 'email', order: order == 'ASC' ? 'DESC' : 'ASC' }) }}" class="text-decoration-none text-dark d-flex align-items-center">
                                    Email
                                    {% if sort == 'email' %}
                                        <i class="ri-arrow-{{ order == 'ASC' ? 'up' : 'down' }}-s-line ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ path('back_admin_users_index', { search: search, sort: 'role', order: order == 'ASC' ? 'DESC' : 'ASC' }) }}" class="text-decoration-none text-dark d-flex align-items-center">
                                    Role
                                    {% if sort == 'role' %}
                                        <i class="ri-arrow-{{ order == 'ASC' ? 'up' : 'down' }}-s-line ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ path('back_admin_users_index', { search: search, sort: 'created_at', order: order == 'ASC' ? 'DESC' : 'ASC' }) }}" class="text-decoration-none text-dark d-flex align-items-center">
                                    Created At
                                    {% if sort == 'created_at' %}
                                        <i class="ri-arrow-{{ order == 'ASC' ? 'up' : 'down' }}-s-line ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ path('back_admin_users_index', { search: search, sort: 'banned', order: order == 'ASC' ? 'DESC' : 'ASC' }) }}" class="text-decoration-none text-dark d-flex align-items-center">
                                    Status
                                    {% if sort == 'banned' %}
                                        <i class="ri-arrow-{{ order == 'ASC' ? 'up' : 'down' }}-s-line ms-1"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-3 bg-primary-subtle rounded-circle">
                                        {% if user.image is defined and user.image %}
                                            <img
                                                src="http://localhost/img/{{  user.image }}"
                                                alt="Profile Image"
                                                class="rounded-circle"
                                                style="width: 40px; height: 40px; object-fit: cover;"
                                            >
                                        {% else %}
                                            <span class="avatar-text">{{ user.fullName|slice(0,1)|upper }}</span>
                                        {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ user.fullName }}</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge {% if user.role == 'ROLE_ADMIN' or user.role == 'admin' %}bg-danger{% else %}bg-primary{% endif %} rounded-pill">
                                        <i class="ri-{% if user.role == 'ROLE_ADMIN' or user.role == 'admin' %}admin{% else %}user{% endif %}-line me-1"></i>
                                        {{ user.role|replace({'ROLE_': ''}) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-calendar-line text-muted me-2"></i>
                                        {{ user.createdAt|date('M d, Y') }}
                                        <span class="text-muted ms-2 small">{{ user.createdAt|date('H:i') }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge {% if user.banned %}bg-danger{% else %}bg-success{% endif %} rounded-pill">
                                        <i class="ri-{% if user.banned %}close-circle{% else %}check-line{% endif %}-line me-1"></i>
                                        {{ user.banned ? 'Banned' : 'Active' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        {% if app.user and user.id != app.user.id %}
                                            <a href="{{ path('back_admin_users_edit', {'id': user.id}) }}"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               data-bs-placement="top"
                                               title="Edit User">
                                                <i class="ri-pencil-line"></i>
                                            </a>
                                            <form action="{{ path('back_admin_users_toggle_ban', {'id': user.id}) }}" method="POST" class="d-inline">
                                                <input type="hidden" name="_token" value="{{ csrf_token('toggle_ban' ~ user.id) }}">
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-{{ user.banned ? 'success' : 'danger' }}"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="{{ user.banned ? 'Unban User' : 'Ban User' }}">
                                                    <i class="ri-{{ user.banned ? 'user-follow' : 'user-forbid' }}-line"></i>
                                                </button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-user-search-line empty-state-icon"></i>
                                        <h5>No users found</h5>
                                        <p class="text-muted">Try adjusting your search or filter criteria</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
/* Avatar Styles */
.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: #fff;
    background-color: var(--primary-color);
    border-radius: 50%;
    overflow: hidden;
}

.avatar-text {
    font-size: 16px;
    font-weight: 600;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

/* Table Styles */
.table > :not(caption) > * > * {
    padding: 1rem 0.75rem;
}

.table-hover tbody tr {
    transition: all 0.2s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.02);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,.05);
}

/* Card Styles */
.card {
    border: none;
    margin-bottom: 24px;
    box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
    transition: all 0.3s ease;
    border-radius: 0.75rem;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,.05);
}

/* Stats Card Styles */
.stats-card {
    border-radius: 0.75rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
}

/* Empty State Styles */
.empty-state {
    padding: 2rem;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

/* Background Subtle Colors */
.bg-primary-subtle {
    background-color: rgba(13, 110, 253, 0.1);
}

.bg-success-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}

.bg-danger-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-warning-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}

.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

/* Animation Classes */
.animate__animated {
    animation-duration: 0.5s;
}

.animate__fadeIn {
    animation-name: fadeIn;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal Styles */
.modal-backdrop {
    opacity: 0.5;
    background-color: #000;
}

.modal {
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
    display: block;
}

.modal-dialog {
    margin: 1.75rem auto;
    max-width: 500px;
}

.modal-content {
    position: relative;
    background-color: #fff;
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Prevent page scrolling when modal is open */
body.modal-open {
    overflow: hidden;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add animation to stats cards
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.classList.add('animate__animated', 'animate__fadeIn');
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.cursor = 'pointer';
            });
        });

        // Chart code for user roles
        const ctx = document.getElementById('userRolePieChart').getContext('2d');

        // Calculate role counts directly from the template variables
        // Get the counts for different role types
        {% set adminCount = 0 %}
        {% set regularCount = 0 %}
        {% for user in users %}
            {% if user.role == 'ROLE_ADMIN' or user.role == 'admin' %}
                {% set adminCount = adminCount + 1 %}
            {% else %}
                {% set regularCount = regularCount + 1 %}
            {% endif %}
        {% endfor %}

        // Create arrays for chart data
        const labels = ['Admin', 'Regular'];
        const data = [{{ adminCount }}, {{ regularCount }}];

        // Check if we have any data to display
        const hasData = data.some(value => value > 0);

        // Add a "No data available" message if there's no data
        if (!hasData) {
            const noDataMessage = document.createElement('div');
            noDataMessage.style.position = 'absolute';
            noDataMessage.style.top = '50%';
            noDataMessage.style.left = '50%';
            noDataMessage.style.transform = 'translate(-50%, -50%)';
            noDataMessage.style.textAlign = 'center';
            noDataMessage.innerHTML = '<i class="ri-information-line" style="font-size: 3rem; color: #d1d5db;"></i><p style="color: #6c757d; margin-top: 10px;">No user data available</p>';
            ctx.canvas.parentNode.style.position = 'relative';
            ctx.canvas.parentNode.appendChild(noDataMessage);
        }

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: ['#dc3545', '#0d6efd'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            pointStyle: 'circle',
                            font: {
                                size: 12,
                                weight: 'bold'
                            },
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map(function(label, i) {
                                        const meta = chart.getDatasetMeta(0);
                                        const style = meta.controller.getStyle(i);
                                        const value = data.datasets[0].data[i];

                                        return {
                                            text: `${label} (${value})`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            strokeStyle: '#fff',
                                            lineWidth: 2,
                                            hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
                                            index: i,
                                            pointStyle: 'circle'
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '0%',
                animation: {
                    animateScale: true,
                    animateRotate: true,
                    duration: 1000
                }
            }
        });
    });
</script>
{% endblock %}