<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Admin Dashboard</title>

    {# Favicon #}
    <link rel="icon" type="image/x-icon" href="{{asset('front/img/eco-net.png')}}">

    {# CSS Files #}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link href="{{ asset('back/css/auth.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/profile-modern.css') }}" rel="stylesheet">

    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-profile-container {
            width: 100%;
            max-width: 600px;
            margin: 2rem auto;
            padding: 0;
        }

        .auth-profile-card {
            background: var(--bg-white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: transform var(--transition-normal), box-shadow var(--transition-normal);
        }

        .auth-profile-header {
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-light);
        }

        .auth-profile-header h1 {
            color: var(--primary-color);
            font-size: 1.75rem;
            margin: 1rem 0 0.5rem;
        }

        .auth-profile-header p {
            color: var(--text-light);
            margin-bottom: 0;
        }

        .auth-profile-body {
            padding: 2rem;
        }

        .auth-profile-form-group {
            margin-bottom: 1.5rem;
        }

        .auth-profile-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-normal);
            border: none;
            text-decoration: none;
            background: var(--primary-color);
            color: white;
        }

        .auth-profile-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .auth-profile-btn i {
            margin-right: 0.5rem;
        }

        .password-requirements {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #6BB748;
        }
    </style>
</head>
<body>
    {% block body %}
    <div class="auth-profile-container">
        <div class="auth-profile-card">
            <div class="auth-profile-header">
                <div class="auth-logo mb-3">
                    <img src="{{ asset('front/img/eco_net.svg') }}" alt="eco.net" height="50">
                </div>
                <h1>Complete Your Profile</h1>
                <p>Please provide your information to complete your account setup</p>
            </div>

            <div class="auth-profile-body">
                {% for label, messages in app.flashes %}
                    {% for message in messages %}
                        <div class="profile-alert profile-alert-{{ label }}">
                            <i class="fas fa-{{ label == 'success' ? 'check-circle' : 'exclamation-circle' }}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endfor %}

                {{ form_start(form, {
                    'action': path('back_auth_profile_update'),
                    'method': 'POST',
                    'attr': {'class': 'auth-profile-form', 'novalidate': 'novalidate'}
                }) }}
                    <div class="profile-form-group">
                        {{ form_label(form.full_name, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        {{ form_widget(form.full_name, {'attr': {'class': 'profile-form-control'}}) }}
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.full_name) }}
                        </span>
                    </div>

                    <div class="profile-form-group">
                        {{ form_label(form.email, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        {{ form_widget(form.email, {'attr': {'class': 'profile-form-control'}}) }}
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.email) }}
                        </span>
                    </div>

                    <div class="profile-form-group">
                        {{ form_label(form.gender, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        {{ form_widget(form.gender, {'attr': {'class': 'profile-form-control profile-form-select'}}) }}
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.gender) }}
                        </span>
                    </div>

                    <div class="profile-form-group">
                        {{ form_label(form.newPassword.first, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        {{ form_widget(form.newPassword.first, {'attr': {'class': 'profile-form-control'}}) }}
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.newPassword.first) }}
                        </span>
                    </div>

                    <div class="profile-form-group">
                        {{ form_label(form.newPassword.second, null, {'label_attr': {'class': 'profile-form-label'}}) }}
                        {{ form_widget(form.newPassword.second, {'attr': {'class': 'profile-form-control'}}) }}
                        <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                            {{ form_errors(form.newPassword.second) }}
                        </span>
                    </div>

                    <div class="password-requirements">
                        <h4 style="font-size: 1rem; color: #333; margin-bottom: 0.75rem;">Password Requirements:</h4>
                        <ul style="margin-bottom: 0; padding-left: 1.25rem; color: #555;">
                            <li>At least 8 characters long</li>
                            <li>Include at least one uppercase letter</li>
                            <li>Include at least one lowercase letter</li>
                            <li>Include at least one number</li>
                            <li>Include at least one special character</li>
                        </ul>
                    </div>

                    <button class="auth-profile-btn" type="submit">
                        <i class="fas fa-save"></i>
                        Complete Profile Setup
                    </button>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
    {% endblock %}
</body>
</html>