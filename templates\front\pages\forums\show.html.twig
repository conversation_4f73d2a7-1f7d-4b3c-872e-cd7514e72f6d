{% extends 'front/base.html.twig' %}


{% block content %}
		<nav class="navbar navbar-expand-lg navbar-light px-4 px-lg-5 py-3 py-lg-0">
			<a
				href="" class="navbar-brand p-0">
				<!-- <h1 clas="text-primary"><i class="fas fa-recycle me-3"></i>eco.net</h1> -->
				<img src="{{asset('front/img/eco_net.png')}}" alt="Logo"/>
			</a>
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
				<span class="fa fa-bars"></span>
			</button>
			<div class="collapse navbar-collapse" id="navbarCollapse">
				<div class="navbar-nav ms-auto py-0">
					<a href="{{path('app_home')}}" class="nav-item nav-link">Home</a>
					<a href="{{path('app_about')}}" class="nav-item nav-link">About</a>
					<a href="{{path('app_service')}}" class="nav-item nav-link">Services</a>
					<div class="nav-item dropdown">
						<a href="#" class="nav-link active" data-bs-toggle="dropdown">
							<span class="dropdown-toggle">Pages</span>
						</a>
						<div class="dropdown-menu m-0">
							<a href="{{path('app_partnerships')}}" class="dropdown-item">Partnerships</a>
							<a href="{{path('app_events')}}" class="dropdown-item">Events</a>
							<a href="{{path('app_products')}}" class="dropdown-item">Products</a>
							<a href="{{path('front_forums_index')}}" class="dropdown-item">Forums</a>
							<a href="{{path('app_challenges')}}" class="dropdown-item">Challenges</a>
						</div>
					</div>
					<a href="{{path('app_contact')}}" class="nav-item nav-link">Contact Us</a>
				</div>
				<a href="{{path('app_register')}}" class="btn btn-primary rounded-pill py-2 px-4 my-3 my-lg-0 flex-shrink-0">Get Started</a>
			</div>
		</nav>
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Forum Details</h1>
            <div class="btn-group">
                <a href="{{ path('front_forums_index') }}" class="btn btn-secondary">
                    <i class="ri-arrow-left-line"></i> Back to List
                </a>
                <a href="{{ path('front_forums_edit', {'id': forum.id}) }}" class="btn btn-warning">
                    <i class="ri-edit-box-line"></i> Edit
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="ri-chat-3-line"></i> {{ forum.title }}
                        </h6>
                        <span class="badge bg-primary">
                            ID: {{ forum.id }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="border-left-primary p-3 bg-light rounded">
                                    <h5 class="text-primary mb-3">Content</h5>
                                    <p class="mb-0">{{ forum.content|nl2br }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">
                                            <i class="ri-time-line"></i> Created At
                                        </h6>
                                        <p class="card-text">
                                            {{ forum.createdat ? forum.createdat|date('F d, Y h:i A') : 'Not specified' }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header py-3">
                                        <h6 class="m-0 font-weight-bold text-primary">
                                            <i class="ri-chat-1-line"></i> Comments
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover" id="commentsTable">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th>ID</th>
                                                        <th width="50%">Content</th>
                                                        <th>Upvotes</th>
                                                        <th class="text-center">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                {% if forum.comments is defined and forum.comments|length > 0 %}
                                                    {% for comment in forum.comments %}
                                                        <tr>
                                                            <td>{{ comment.id }}</td>
                                                            <td>{{ comment.content|length > 150 ? comment.content|slice(0, 150) ~ '...' : comment.content }}</td>
                                                            <td>
                                                                <span class="badge bg-success">
                                                                    <i class="ri-thumb-up-line"></i> {{ comment.upvotes }}
                                                                </span>
                                                            </td>
                                                            <td class="text-center">
                                                                <a href="{{ path('front_comments_show', {'id': comment.id}) }}" class="btn btn-info btn-sm" title="View">
                                                                    <i class="ri-eye-line"></i>
                                                                </a>
                                                                <a href="{{ path('front_comments_edit', {'id': comment.id}) }}" class="btn btn-warning btn-sm" title="Edit">
                                                                    <i class="ri-edit-box-line"></i>
                                                                </a>
                                                                <form method="post" action="{{ path('front_comments_delete', {'id': comment.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ comment.id) }}">
                                                                    <button class="btn btn-danger btn-sm" title="Delete">
                                                                        <i class="ri-delete-bin-line"></i>
                                                                    </button>
                                                                </form>
                                                            </td>
                                                        </tr>
                                                    {% endfor %}
                                                {% else %}
                                                    <tr>
                                                        <td colspan="4" class="text-center">No comments found for this forum</td>
                                                    </tr>
                                                {% endif %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                <i class="ri-information-line"></i> Last updated: {{ forum.createdat ? forum.createdat|date('F d, Y h:i A') : 'Not available' }}
                            </div>
                            {{ include('back/pages/forums/_delete_form.html.twig', {
                                'button_class': 'btn btn-danger',
                                'button_icon': 'ri-delete-bin-line',
                                'button_text': 'Delete Forum'
                            }) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $('#commentsTable').DataTable({
                "order": [[ 0, "desc" ]],
                "pageLength": 5,
                "language": {
                    "lengthMenu": "Show _MENU_ comments per page",
                    "zeroRecords": "No comments found",
                    "info": "Showing page _PAGE_ of _PAGES_",
                    "infoEmpty": "No comments available",
                    "infoFiltered": "(filtered from _MAX_ total comments)"
                }
            });
        });
    </script>
{% endblock %}
