import smtplib
import sys
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def get_subject(email_type="reset_password"):
    if email_type == "reset_password":
        return "Password Reset Request"
    else:
        ""

def get_body(email_type="reset_password", reset_token=""):
    if email_type == "reset_password":
        reset_link = f"http://localhost:8000/auth/reset-password/{reset_token}"
        return f"Hello,\n\nThis is an automated email for your password reset request. Click the link below to reset your password:\n\n{reset_link}\n\nThis link will expire in 1 hour. If you did not request a password reset, please ignore this email."
    else:
        ""

def send_email(sender_email, email_type="reset_password", reset_token=""):
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    smtp_user = "<EMAIL>"
    smtp_pass = "xgio xvso nefk boby"
    
    recipient_email = sender_email  # Assuming we're sending to the provided sender
    subject = get_subject(email_type)
    body = get_body(email_type, reset_token)
    
    msg = MIMEMultipart()
    msg["From"] = smtp_user
    msg["To"] = recipient_email
    msg["Subject"] = subject
    
    msg.attach(MIMEText(body, "plain"))
    
    try:
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_user, smtp_pass)
            server.sendmail(smtp_user, recipient_email, msg.as_string())
            print("Email sent successfully!")
    except Exception as e:
        print(f"Error sending email: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python send_email.py <from_email> [type]")
        sys.exit(1)
    
    from_email = sys.argv[1]
    email_type = sys.argv[2] if len(sys.argv) > 2 else "reset_password"
    reset_token = sys.argv[3] if len(sys.argv) > 3 else ""
    
    send_email(from_email, email_type, reset_token)
