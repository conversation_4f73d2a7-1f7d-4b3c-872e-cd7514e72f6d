{% extends 'back/base.html.twig' %}

{% block title %}Edit User{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('back_admin_users_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Users">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Edit User</h1>
                    <p class="text-muted mb-0">Update user information and settings</p>
                </div>
                <div class="col-auto">
                    <div class="user-status-badge">
                        <span class="badge {% if user.banned %}bg-danger{% else %}bg-success{% endif %} rounded-pill">
                            <i class="ri-{% if user.banned %}close-circle{% else %}check-line{% endif %}-line me-1"></i>
                            {{ user.banned ? 'Banned' : 'Active' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        {% for message in app.flashes('success') %}
            <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                <i class="ri-checkbox-circle-line me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}

        {% for message in app.flashes('error') %}
            <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                <i class="ri-error-warning-line me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}

        <div class="row">
            <!-- User Profile Card -->
            <div class="col-lg-4 mb-4">
                <div class="card user-profile-card shadow-sm border-0 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body text-center">
                        <div class="profile-image-container mb-4">
                            {% if user.image %}
                                <img
                                    src="http://localhost/img/{{ user.image }}"
                                    alt="Profile Image"
                                    class="profile-image"
                                >
                            {% else %}
                                <img
                                    src="{{ asset('front/img/default user.png') }}"
                                    alt="Profile Image"
                                    class="profile-image"
                                >
                            {% endif %}
                            <div class="profile-image-overlay">
                                <i class="ri-camera-line"></i>
                            </div>
                        </div>

                        <h5 class="user-name mb-1">{{ user.fullName }}</h5>
                        <p class="user-email text-muted mb-3">{{ user.email }}</p>

                        <div class="user-role mb-3">
                            <span class="badge {% if user.role == 'ROLE_ADMIN' %}bg-danger{% else %}bg-primary{% endif %} rounded-pill">
                                <i class="ri-{% if user.role == 'ROLE_ADMIN' %}admin{% else %}user{% endif %}-line me-1"></i>
                                {{ user.role|replace({'ROLE_': ''}) }}
                            </span>
                        </div>

                        <div class="user-meta">
                            <div class="meta-item">
                                <i class="ri-calendar-line text-muted me-2"></i>
                                <span>Joined: {{ user.createdAt|date('M d, Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Edit Form -->
            <div class="col-lg-8">
                <div class="card shadow-sm border-0 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold">User Information</h5>
                    </div>
                    <div class="card-body">
                        {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
                            <div class="row g-3">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.full_name, {'attr': {'class': 'form-control', 'placeholder': 'Full Name', 'onChange': 'handleChange(this)'}}) }}
                                        {{ form_label(form.full_name, 'Full Name') }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.full_name) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.email, {'attr': {'class': 'form-control', 'placeholder': 'Email Address', 'onChange': 'handleChange(this)'}}) }}
                                        {{ form_label(form.email, 'Email Address') }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.email) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.role, {'attr': {'class': 'form-select', 'placeholder': 'Select Role'}}) }}
                                        {{ form_label(form.role, 'User Role') }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.role) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <a href="{{ path('back_admin_users_index') }}" class="btn btn-outline-secondary">
                                    <i class="ri-close-line me-1"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary" id="submit_btn">
                                    <i class="ri-save-line me-1"></i> Save Changes
                                </button>
                            </div>
                        {{ form_end(form) }}
                    </div>
                </div>
            </div>
        </div>
    </div>

<style>
/* Page Header */
.page-header {
    margin-bottom: 1.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* User Profile Card */
.user-profile-card {
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.user-profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

.profile-image-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.profile-image-container:hover .profile-image-overlay {
    opacity: 1;
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 4px solid #fff;
}

.profile-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
    border-radius: 50%;
}

.user-name {
    font-weight: 600;
    margin-top: 1rem;
}

.user-email {
    font-size: 0.9rem;
}

.user-role {
    margin: 1rem 0;
}

.user-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.meta-item {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Form Styling */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(118, 184, 82, 0.25);
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Animation Classes */
.animate__animated {
    animation-duration: 0.5s;
}

.animate__fadeIn {
    animation-name: fadeIn;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // Handle form changes
        window.handleChange = function(input) {
            input.classList.remove('is-invalid');
            const feedbackElement = input.nextElementSibling;
            if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                feedbackElement.textContent = '';
            }
        };
    });
</script>
{% endblock %}