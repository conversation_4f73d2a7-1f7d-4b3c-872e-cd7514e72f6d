<?php

namespace App\Form;

use App\Entity\Challenge;
use App\Entity\Quizz;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Range;

class QuizzType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('question', TextareaType::class, [
                'label' => 'Question',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 3,
                    'placeholder' => 'Enter your question here',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a question']),
                    new Length([
                        'min' => 10,
                        'minMessage' => 'Your question should be at least {{ limit }} characters long',
                    ]),
                ],
                'empty_data' => false,
            ])
            ->add('choice1', TextareaType::class, [
                'label' => 'Choice 1',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 2,
                    'placeholder' => 'Enter the first choice',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter choice 1']),
                ],
                'empty_data' => false,
            ])
            ->add('choice2', TextareaType::class, [
                'label' => 'Choice 2',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 2,
                    'placeholder' => 'Enter the second choice',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter choice 2']),
                ],
                'empty_data' => false,
            ])
            ->add('choice3', TextareaType::class, [
                'label' => 'Choice 3',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 2,
                    'placeholder' => 'Enter the third choice',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter choice 3']),
                ],
                'empty_data' => false,
            ])
            ->add('choice4', TextareaType::class, [
                'label' => 'Choice 4',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 2,
                    'placeholder' => 'Enter the fourth choice',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter choice 4']),
                ],
                'empty_data' => false,
            ])
            ->add('answer', ChoiceType::class, [
                'label' => 'Correct Answer',
                'choices' => [
                    'Choice 1' => 1,
                    'Choice 2' => 2,
                    'Choice 3' => 3,
                    'Choice 4' => 4,
                ],
                'expanded' => true,
                'multiple' => false,
                'attr' => [
                    'class' => 'form-check-input',
                ],
                'label_attr' => [
                    'class' => 'form-check-label',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select the correct answer']),
                    new Range([
                        'min' => 1,
                        'max' => 4,
                        'notInRangeMessage' => 'Please select a valid answer (1-4)',
                    ]),
                ],
                'empty_data' => null,
            ])
            ->add('challenge', EntityType::class, [
                'class' => Challenge::class,
                'choice_label' => 'name',
                'placeholder' => 'Select a challenge',
                'attr' => [
                    'class' => 'form-select',
                ],
                'label' => 'Associated Challenge',
                'constraints' => [
                    new NotBlank(['message' => 'Please select a challenge']),
                ],
                'empty_data' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Quizz::class,
            'attr' => [
                'class' => 'needs-validation',
                'novalidate' => true,
            ],
        ]);
    }
}
