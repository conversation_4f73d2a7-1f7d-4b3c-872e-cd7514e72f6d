{% block sidebar %}
        <nav id="sidebar" class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="ri-recycle-fill"></i> eco.net</h3>
                </div>
                <ul class="list-unstyled components">
                    <li class="{% if app.request.get('_route') == 'back_admin_dashboard' %}active{% endif %}">
                        <a href="{{ path('back_admin_dashboard') }}" class="menu-item">
                            <i class="ri-dashboard-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>

                    <li class="{% if 'users' in app.request.get('_route') %}active{% endif %}">
                        <a href="{{ path('back_admin_users_index') }}" class="menu-item">
                            <i class="ri-user-line"></i>
                            <span>User Management</span>
                        </a>
                    </li>

                    <li class="{% if 'event' in app.request.get('_route') %}active{% endif %}">
                        <a href="#eventSubmenu" data-bs-toggle="collapse" aria-expanded="{{ 'event' in app.request.get('_route') ? 'true' : 'false' }}" class="dropdown-toggle menu-item">
                            <i class="ri-calendar-line"></i>
                            <span>Event Management</span>
                        </a>
                        <ul class="collapse list-unstyled {{ 'event' in app.request.get('_route') ? 'show' : '' }}" id="eventSubmenu">
                            <li>
                                <a href="{{ path('app_admin_event_index') }}" class="submenu-item">
                                    <i class="ri-calendar-event-line"></i>
                                    <span>Events</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ path('app_admin_event_registration_index') }}" class="submenu-item">
                                    <i class="ri-calendar-check-fill"></i>
                                    <span>Registrations</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="{% if 'partners' in app.request.get('_route') or 'donation' in app.request.get('_route') %}active{% endif %}">
                        <a href="#partnerSubmenu" data-bs-toggle="collapse" aria-expanded="{{ 'partners' in app.request.get('_route') or 'donation' in app.request.get('_route') ? 'true' : 'false' }}" class="dropdown-toggle menu-item">
                            <i class="ri-user-heart-line"></i>
                            <span>Partners & Donations</span>
                        </a>
                        <ul class="collapse list-unstyled {{ 'partners' in app.request.get('_route') or 'donation' in app.request.get('_route') ? 'show' : '' }}" id="partnerSubmenu">
                            <li>
                                <a href="{{ path('app_partners_index') }}" class="submenu-item">
                                    <i class="ri-service-line"></i>
                                    <span>Partners</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ path('app_back_donation_index') }}" class="submenu-item">
                                    <i class="ri-hand-heart-line"></i>
                                    <span>Donations</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="{% if 'forums' in app.request.get('_route') %}active{% endif %}">
                        <a href="{{ path('app_forums_index') }}" class="menu-item">
                            <i class="ri-discuss-line"></i>
                            <span>Forums</span>
                        </a>
                    </li>

                    <li class="{% if 'challenge' in app.request.get('_route') or 'quizz' in app.request.get('_route') or 'progress' in app.request.get('_route') %}active{% endif %}">
                        <a href="#challengeSubmenu" data-bs-toggle="collapse" aria-expanded="{{ 'challenge' in app.request.get('_route') or 'quizz' in app.request.get('_route') or 'progress' in app.request.get('_route') ? 'true' : 'false' }}" class="dropdown-toggle menu-item">
                            <i class="ri-seedling-line"></i>
                            <span>Eco Challenges</span>
                        </a>
                        <ul class="collapse list-unstyled {{ 'challenge' in app.request.get('_route') or 'quizz' in app.request.get('_route') or 'progress' in app.request.get('_route') ? 'show' : '' }}" id="challengeSubmenu">
                            <li>
                                <a href="{{ path('app_challenge_index') }}" class="submenu-item">
                                    <i class="ri-trophy-line"></i>
                                    <span>Challenges</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ path('app_quizz_index') }}" class="submenu-item">
                                    <i class="ri-questionnaire-line"></i>
                                    <span>Quizzes</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ path('app_progress_index') }}" class="submenu-item">
                                    <i class="ri-bar-chart-2-line"></i>
                                    <span>Progress</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="{% if 'product' in app.request.get('_route') or 'command' in app.request.get('_route') %}active{% endif %}">
                        <a href="#productSubmenu" data-bs-toggle="collapse" aria-expanded="{{ 'product' in app.request.get('_route') or 'command' in app.request.get('_route') ? 'true' : 'false' }}" class="dropdown-toggle menu-item">
                            <i class="ri-shopping-bag-line"></i>
                            <span>Products & Orders</span>
                        </a>
                        <ul class="collapse list-unstyled {{ 'product' in app.request.get('_route') or 'command' in app.request.get('_route') ? 'show' : '' }}" id="productSubmenu">
                            <li>
                                <a href="{{ path('app_product_index') }}" class="submenu-item">
                                    <i class="ri-archive-line"></i>
                                    <span>Products</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ path('app_command_index') }}" class="submenu-item">
                                    <i class="ri-truck-line"></i>
                                    <span>Orders</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>

                <div class="sidebar-footer">
                    <p>© {{ "now"|date("Y") }} eco.net - All rights reserved</p>
                </div>
        </nav>
{% endblock %}