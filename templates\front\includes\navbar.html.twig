{% block navbar %}
                <nav class="navbar navbar-expand-lg navbar-light px-4 px-lg-5 py-3 py-lg-0">
			<a
				href="{{path('app_home')}}" class="navbar-brand p-0">
				<!-- <h1 clas="text-primary"><i class="fas fa-recycle me-3"></i>eco.net</h1> -->
				<img src="{{asset('front/img/eco_net.png')}}" alt="Logo"/>
			</a>
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
				<span class="fa fa-bars"></span>
			</button>
			<div class="collapse navbar-collapse" id="navbarCollapse">
				<div class="navbar-nav ms-auto py-0">
					<a href="{{path('app_home')}}" class="nav-item nav-link {% if app.request.get('_route') == 'app_home' %}active{% endif %}">
						Home
					</a>
					<a href="{{path('app_about')}}" class="nav-item nav-link {% if app.request.get('_route') == 'app_about' %}active{% endif %}">
						About
					</a>
					<a href="{{path('app_service')}}" class="nav-item nav-link {% if app.request.get('_route') == 'app_service' %}active{% endif %}">
						Services
					</a>
					<div class="nav-item dropdown">
						<a href="#" class="nav-link {% if app.request.get('_route') in ['app_partnerships', 'app_events', 'app_products', 'app_forums', 'app_challenges_list'] %}active{% endif %}" data-bs-toggle="dropdown">
							Pages <i class="ri-arrow-down-s-line"></i>
						</a>
						<div class="dropdown-menu m-0">
							<a href="{{path('app_front_donation_index')}}" class="dropdown-item {% if app.request.get('_route') == 'app_partnerships' %}active{% endif %}">
								Partnerships
							</a>
							<a href="{{path('app_event_list')}}" class="dropdown-item {% if app.request.get('_route') == 'app_events' %}active{% endif %}">
								Events
							</a>
							<a href="{{path('app_front_products')}}" class="dropdown-item {% if app.request.get('_route') == 'app_products' %}active{% endif %}">
								Products
							</a>
							<a href="{{path('front_forums_index')}}" class="dropdown-item {% if app.request.get('_route') == 'app_forums' %}active{% endif %}">
								Forums
							</a>
							<a href="{{path('app_challenges_list')}}" class="dropdown-item {% if app.request.get('_route') == 'app_challenges_list' %}active{% endif %}">
								Challenges
							</a>
						</div>
					</div>
					<a href="{{path('app_contact')}}" class="nav-item nav-link {% if app.request.get('_route') == 'app_contact' %}active{% endif %}">
						Contact Us
					</a>
				</div>

				{% if is_granted('IS_AUTHENTICATED_FULLY') %}

					<a href="{{ path('app_cart') }}" class="btn btn-success rounded-pill px-4 py-2">
						<i class="fa-solid fa-cart-shopping"></i>
					</a>
					
					<div class="nav-item dropdown ms-3">
						<a href="#" class="btn btn-success rounded-pill px-4 py-2 dropdown-toggle" data-bs-toggle="dropdown">
							<i class="fa-solid fa-user"></i>
							<span class="d-none d-md-inline"> {{ app.user.fullName }}</span>
						</a>
						<div class="dropdown-menu dropdown-menu-end mt-2">
							<div class="dropdown-header">
								Welcome!<br /> {{ app.user.email }}
							</div>
							<a href="{{ path('back_profile_edit') }}" class="dropdown-item">
								<i class="fa-solid fa-user-pen"></i> Profile
							</a>

							<a href="{{ path('app_command_by_user', {'userId': app.user.id}) }}" class="dropdown-item">
								<i class="fa-solid fa-calendar-days"></i> My Commands
							</a>
							
							{% if app.user.role|lower == 'admin' %}
								<a href="{{ path('back_admin_dashboard') }}" class="dropdown-item">
									<i class="fas fa-table"></i> Dashboard
								</a>
							{% endif %}
							
							<div class="dropdown-divider"></div>
							<a href="{{ path('back_auth_logout') }}" class="dropdown-item text-danger">
								<i class="fa-solid fa-right-from-bracket"></i> Logout
							</a>
						</div>
					</div>
				{% else %}
					<div class="ms-3">
						<a href="{{path('back_auth_login')}}" class="btn btn-success rounded-pill px-4 py-2">
							<i class="ri-login-circle-line me-1"></i>
							<span class="d-none d-md-inline">Sign In</span>
						</a>
						<a href="{{path('back_auth_register')}}" class="btn btn-outline-success rounded-pill px-4 py-2 ms-2">
							<i class="ri-user-add-line me-1"></i>
							<span class="d-none d-md-inline">Register</span>
						</a>
					</div>
				{% endif %}
			</div>
		</nav>
{% endblock %}