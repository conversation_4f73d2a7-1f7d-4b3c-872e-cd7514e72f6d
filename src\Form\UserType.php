<?php

namespace App\Form;

use App\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\IsTrue;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class UserType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('full_name', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Full Name',
                    'novalidate' => true
                ],
                'label' => 'Full Name',
                'constraints' => [
                    new NotBlank(['message' => 'Please enter your full name'])
                ]
            ])
            ->add('email', EmailType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Email address',
                    'novalidate' => true
                ],
                'label' => 'Email address',
                'constraints' => [
                    new NotBlank(['message' => 'Please enter your email address'])
                ]
            ])
            ->add('gender', ChoiceType::class, [
                'choices' => [
                    'Male' => 'male',
                    'Female' => 'female'
                ],
                'expanded' => true,
                'multiple' => false,
                'label' => 'Gender',
                'required' => true,
                'label_attr' => ['class' => 'form-label'],
                'choice_attr' => function($choice, $key, $value) {
                    return ['class' => 'form-check-input', 'novalidate' => true];
                },
                'constraints' => [
                    new NotBlank(['message' => 'Please select your gender'])
                ]
            ])
            ->add('password', RepeatedType::class, [
                'type' => PasswordType::class,
                'invalid_message' => 'The password fields must match.',
                'required' => true,
                'first_options'  => [
                    'label' => 'Password',
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'Password',
                        'novalidate' => true
                    ]
                ],
                'second_options' => [
                    'label' => 'Confirm Password',
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'Confirm Password',
                        'novalidate' => true
                    ],
                    'constraints' => [
                        new NotBlank(['message' => 'Please confirm your password'])
                    ]
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a password']),
                    new Length([
                        'min' => 6,
                        'minMessage' => 'Your password should be at least {{ limit }} characters',
                        'max' => 4096,
                    ]),
                ],
            ])
            ->add('terms', CheckboxType::class, [
                'mapped' => true,
                'required' => true,
                'label' => 'I agree to the Terms of Service and Privacy Policy',
                'attr' => ['class' => 'form-check-input', 'novalidate' => true],
                'constraints' => [
                    new IsTrue(['message' => 'You must agree to the Terms of Service and Privacy Policy'])
                ]
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => User::class,
        ]);
    }
}
