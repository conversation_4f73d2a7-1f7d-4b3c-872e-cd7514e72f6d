{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div id="logo-preview-container" class="mb-3" style="display: none;">
                            <img id="logo-preview" src="#" alt="Logo Preview" 
                                 style="max-width: 200px; max-height: 200px; object-fit: contain;"
                                 class="img-thumbnail">
                        </div>
                        <div id="logo-placeholder" class="border rounded p-4 mb-3">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="text-muted mt-2 mb-0">Sélectionnez une image</p>
                        </div>
                    </div>
                    <div class="form-group">
                        {{ form_label(form.logo, 'Logo', {'label_attr': {'class': 'form-label'}}) }}
                        {{ form_widget(form.logo, {'attr': {'class': 'form-control', 'data-preview': 'logo-preview'}}) }}
                        {{ form_help(form.logo) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.logo) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="form-group mb-3">
                        {{ form_label(form.name, 'Name', {'label_attr': {'class': 'form-label'}}) }}
                        {{ form_widget(form.name, {'attr': {'class': 'form-control', 'placeholder': 'Enter partner name'}}) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.name) }}
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        {{ form_label(form.type, 'Type', {'label_attr': {'class': 'form-label'}}) }}
                        {{ form_widget(form.type, {'attr': {'class': 'form-control', 'placeholder': 'Enter partner type'}}) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.type) }}
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        {{ form_label(form.description, 'Description', {'label_attr': {'class': 'form-label'}}) }}
                        {{ form_widget(form.description, {'attr': {'class': 'form-control', 'placeholder': 'Enter description', 'rows': '4'}}) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.description) }}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                {{ form_label(form.latitude, 'Latitude', {'label_attr': {'class': 'form-label'}}) }}
                                {{ form_widget(form.latitude, {'attr': {'class': 'form-control', 'placeholder': 'Enter latitude'}}) }}
                                {{ form_help(form.latitude) }}
                                <div class="invalid-feedback d-block">
                                    {{ form_errors(form.latitude) }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                {{ form_label(form.longitude, 'Longitude', {'label_attr': {'class': 'form-label'}}) }}
                                {{ form_widget(form.longitude, {'attr': {'class': 'form-control', 'placeholder': 'Enter longitude'}}) }}
                                {{ form_help(form.longitude) }}
                                <div class="invalid-feedback d-block">
                                    {{ form_errors(form.longitude) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 text-end">
        <a href="{{ path('app_partners_index') }}" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>Annuler
        </a>
        <button type="submit" class="btn btn-primary ms-2">
            <i class="fas fa-save me-2"></i>{{ button_label|default('Enregistrer') }}
        </button>
    </div>
{{ form_end(form) }}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logoInput = document.querySelector('input[type="file"][data-preview="logo-preview"]');
    const previewImg = document.getElementById('logo-preview');
    const previewContainer = document.getElementById('logo-preview-container');
    const placeholder = document.getElementById('logo-placeholder');

    logoInput.addEventListener('change', function(e) {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                previewContainer.style.display = 'block';
                placeholder.style.display = 'none';
            }
            
            reader.readAsDataURL(this.files[0]);
        } else {
            previewContainer.style.display = 'none';
            placeholder.style.display = 'block';
        }
    });
});
</script>
