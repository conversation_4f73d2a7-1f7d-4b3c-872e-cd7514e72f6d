{% extends 'back/pages/home/<USER>' %}

{% block dash %} {% endblock %}
{% block forum %}{% endblock %}
{% block cmt %}active{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Stats Card Styles */
        .stats-card {
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0,0,0,.05);
            transition: all 0.3s ease;
            border: none;
            background-color: #fff;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,.1);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 24px;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 0;
            line-height: 1.2;
        }

        .stats-label {
            font-size: 14px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 36px;
            height: 36px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Comment Item Styles */
        .comment-item {
            transition: all 0.3s ease;
        }

        .comment-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .comment-content {
            color: #6c757d;
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Comments Management</h1>
                    <p class="text-muted mb-0">Manage and monitor all user comments</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_comments_new') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i> Create New Comment
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Summary Cards -->
        <div class="row mb-4">
            <!-- Total Comments Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card h-100 animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-subtle text-primary me-3">
                                <i class="ri-chat-1-line"></i>
                            </div>
                            <div>
                                <p class="stats-value">{{ comments|length }}</p>
                                <p class="stats-label mb-0">Total Comments</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Upvotes Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-subtle text-success me-3">
                                <i class="ri-thumb-up-line"></i>
                            </div>
                            <div>
                                {% set totalUpvotes = 0 %}
                                {% for comment in comments %}
                                    {% set totalUpvotes = totalUpvotes + comment.upvotes %}
                                {% endfor %}
                                <p class="stats-value">{{ totalUpvotes }}</p>
                                <p class="stats-label mb-0">Total Upvotes</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Upvotes Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-info-subtle text-info me-3">
                                <i class="ri-bar-chart-line"></i>
                            </div>
                            <div>
                                {% set totalComments = comments|length %}
                                {% set totalUpvotes = 0 %}
                                {% for comment in comments %}
                                    {% set totalUpvotes = totalUpvotes + comment.upvotes %}
                                {% endfor %}
                                {% set avgUpvotes = totalComments > 0 ? (totalUpvotes / totalComments)|round(1) : 0 %}
                                <p class="stats-value">{{ avgUpvotes }}</p>
                                <p class="stats-label mb-0">Avg. Upvotes</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Most Upvoted Card -->
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stats-card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning-subtle text-warning me-3">
                                <i class="ri-trophy-line"></i>
                            </div>
                            <div>
                                {% set mostUpvotes = 0 %}
                                {% for comment in comments %}
                                    {% if comment.upvotes > mostUpvotes %}
                                        {% set mostUpvotes = comment.upvotes %}
                                    {% endif %}
                                {% endfor %}
                                <p class="stats-value">{{ mostUpvotes }}</p>
                                <p class="stats-label mb-0">Most Upvoted</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comments List Card -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Comments List</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" id="commentSearch" class="form-control" placeholder="Search comments...">
                            <span class="input-group-text bg-primary text-white">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle border-0" id="commentsTable">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th>Content</th>
                                <th>Forum</th>
                                <th>Upvotes</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for comment in comments %}
                            <tr class="align-middle comment-item">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2 bg-primary-subtle rounded-circle">
                                            <span class="avatar-text text-primary">
                                                {% if comment.user %}
                                                    {{ comment.user.getFullName()|slice(0,1)|upper }}
                                                {% else %}
                                                    ?
                                                {% endif %}
                                            </span>
                                        </div>
                                        <div>
                                            {% if comment.user %}
                                                {{ comment.user.getFullName() }}
                                            {% else %}
                                                Unknown User
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="comment-content">{{ comment.content|length > 100 ? comment.content|slice(0, 100) ~ '...' : comment.content }}</div>
                                </td>
                                <td>
                                    <a href="{{ path('app_forums_show', {'id': comment.postid.id}) }}" class="text-decoration-none">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm me-2 bg-info-subtle rounded-circle">
                                                <i class="ri-discuss-line text-info"></i>
                                            </div>
                                            <span class="text-truncate" style="max-width: 150px;" title="{{ comment.postid.title }}">
                                                {{ comment.postid.title }}
                                            </span>
                                        </div>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-success-subtle text-success rounded-pill">
                                        <i class="ri-thumb-up-line me-1"></i> {{ comment.upvotes }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_comments_show', {'id': comment.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="View Details">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <a href="{{ path('app_comments_edit', {'id': comment.id}) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="Edit Comment">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <form method="post" action="{{ path('app_comments_delete', {'id': comment.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ comment.id) }}">
                                            <button class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete Comment">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-chat-1-line empty-state-icon"></i>
                                        <h5>No comments found</h5>
                                        <p class="text-muted">There are no comments yet</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize DataTable with enhanced styling
            $('#commentsTable').DataTable({
                "order": [[ 3, "desc" ]],  // Sort by upvotes by default
                "pageLength": 10,
                "language": {
                    "lengthMenu": "Show _MENU_ comments per page",
                    "zeroRecords": "No comments found",
                    "info": "Showing page _PAGE_ of _PAGES_",
                    "infoEmpty": "No comments available",
                    "infoFiltered": "(filtered from _MAX_ total comments)"
                },
                "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                       '<"row"<"col-sm-12"tr>>' +
                       '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                "responsive": true,
                "autoWidth": false,
                "drawCallback": function() {
                    // Reinitialize tooltips after table redraw
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }
            });

            // Custom search functionality
            const searchInput = document.getElementById('commentSearch');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    $('#commentsTable').DataTable().search(this.value).draw();
                });
            }

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('#commentsTable tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });

                // Make the entire row clickable to view comment details
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons
                    if (e.target.closest('.btn') || e.target.closest('form')) {
                        return;
                    }

                    const viewLink = this.querySelector('a[title="View Details"]');
                    if (viewLink) {
                        viewLink.click();
                    }
                });
            });

            // Add animation to stats cards
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
{% endblock %}
