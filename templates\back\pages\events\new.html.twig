{% extends 'back/base.html.twig' %}

{% block title %}Create Event{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_admin_event_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Events">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Create New Event</h1>
                    <p class="text-muted mb-0">Add a new event to your calendar</p>
                </div>
            </div>
        </div>

        {% for message in app.flashes('success') %}
            <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                <i class="ri-checkbox-circle-line me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}

        {% for message in app.flashes('error') %}
            <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                <i class="ri-error-warning-line me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}

        <div class="row">
            <!-- Event Preview Card -->
            <div class="col-lg-4 mb-4">
                <div class="card event-preview-card shadow-sm border-0 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <h5 class="card-title fw-bold mb-4">Event Preview</h5>

                        <div class="event-preview-image mb-4">
                            <div class="event-image-placeholder-large">
                                <i class="ri-image-line"></i>
                                <p class="mt-2 small">No image available</p>
                            </div>
                        </div>

                        <div class="event-preview-details">
                            <div class="preview-item mb-3">
                                <div class="preview-label text-muted small">Event Title</div>
                                <div class="preview-value fw-bold" id="preview-title">Event Title</div>
                            </div>

                            <div class="preview-item mb-3">
                                <div class="preview-label text-muted small">Date & Time</div>
                                <div class="preview-value" id="preview-date">
                                    <i class="ri-calendar-line text-primary me-1"></i>
                                    Not set
                                </div>
                            </div>

                            <div class="preview-item mb-3">
                                <div class="preview-label text-muted small">Location</div>
                                <div class="preview-value" id="preview-location">
                                    <i class="ri-map-pin-line text-danger me-1"></i>
                                    Location not set
                                </div>
                            </div>

                            <div class="preview-item mb-3">
                                <div class="preview-label text-muted small">Capacity</div>
                                <div class="preview-value" id="preview-capacity">
                                    <i class="ri-user-line text-success me-1"></i>
                                    Not set
                                </div>
                            </div>

                            <div class="preview-item">
                                <div class="preview-label text-muted small">Description</div>
                                <div class="preview-value small text-muted" id="preview-description">
                                    No description provided
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Create Form -->
            <div class="col-lg-8">
                <div class="card shadow-sm border-0 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold">Event Information</h5>
                    </div>
                    <div class="card-body">
                        {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'event-form'}}) }}
                            <div class="row g-3">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.title, {
                                            'attr': {
                                                'class': 'form-control',
                                                'placeholder': 'Enter event title',
                                                'data-preview-target': 'title'
                                            }
                                        }) }}
                                        {{ form_label(form.title) }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.title) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.date, {
                                            'attr': {
                                                'class': 'form-control',
                                                'placeholder': 'Select date and time',
                                                'data-preview-target': 'date'
                                            }
                                        }) }}
                                        {{ form_label(form.date) }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.date) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.location, {
                                            'attr': {
                                                'class': 'form-control',
                                                'placeholder': 'Enter event location',
                                                'data-preview-target': 'location'
                                            }
                                        }) }}
                                        {{ form_label(form.location) }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.location) }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-floating">
                                        {{ form_widget(form.maxParticipants, {
                                            'attr': {
                                                'class': 'form-control',
                                                'placeholder': 'Enter maximum participants',
                                                'data-preview-target': 'capacity'
                                            }
                                        }) }}
                                        {{ form_label(form.maxParticipants) }}
                                        <div class="invalid-feedback">
                                            {{ form_errors(form.maxParticipants) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-12 mb-3">
                                    <label class="form-label fw-bold">{{ form_label(form.description) }}</label>
                                    {{ form_widget(form.description, {
                                        'attr': {
                                            'class': 'form-control',
                                            'placeholder': 'Enter event description',
                                            'rows': 5,
                                            'data-preview-target': 'description'
                                        }
                                    }) }}
                                    <div class="invalid-feedback">
                                        {{ form_errors(form.description) }}
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-4">
                                <a href="{{ path('app_admin_event_index') }}" class="btn btn-outline-secondary">
                                    <i class="ri-close-line me-1"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ri-save-line me-1"></i> Create Event
                                </button>
                            </div>
                        {{ form_end(form) }}
                    </div>
                </div>
            </div>
        </div>
    </div>

<style>
/* Page Header */
.page-header {
    margin-bottom: 1.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Card Styles */
.card {
    border: none;
    margin-bottom: 24px;
    box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
}

/* Event Preview Card */
.event-preview-card {
    height: 100%;
}

.event-preview-image {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    height: 200px;
    background-color: #f8f9fa;
}

.event-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.event-image-placeholder-large {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #adb5bd;
    font-size: 3rem;
}

.preview-item {
    margin-bottom: 1rem;
}

.preview-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.preview-value {
    font-size: 0.95rem;
}

/* Form Styling */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(118, 184, 82, 0.25);
}

.form-control {
    padding: 0.75rem 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
}

textarea.form-control {
    min-height: 120px;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Animation Classes */
.animate__animated {
    animation-duration: 0.5s;
}

.animate__fadeIn {
    animation-name: fadeIn;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation
        const form = document.querySelector('#event-form');
        if (form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        }

        // Live preview functionality
        const previewFields = document.querySelectorAll('[data-preview-target]');
        previewFields.forEach(field => {
            field.addEventListener('input', function() {
                const target = this.getAttribute('data-preview-target');
                const previewElement = document.getElementById('preview-' + target);

                if (previewElement) {
                    if (target === 'title') {
                        previewElement.textContent = this.value || 'Event Title';
                    } else if (target === 'location') {
                        const locationContent = this.value || 'Location not set';
                        previewElement.innerHTML = '<i class="ri-map-pin-line text-danger me-1"></i>' + locationContent;
                    } else if (target === 'capacity') {
                        const capacityContent = this.value ? this.value + ' participants' : 'Not set';
                        previewElement.innerHTML = '<i class="ri-user-line text-success me-1"></i>' + capacityContent;
                    } else if (target === 'description') {
                        let descriptionContent = this.value || 'No description provided';
                        if (descriptionContent.length > 100) {
                            descriptionContent = descriptionContent.substring(0, 100) + '...';
                        }
                        previewElement.textContent = descriptionContent;
                    }
                }
            });
        });
    });
</script>
{% endblock %}
