{{ form_start(form) }}
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        {{ form_label(form.amount) }}
                        {{ form_widget(form.amount) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.amount) }}
                        </div>
                        <small class="form-text text-muted">
                            Entrez un montant entre 1 et 1,000,000 TND
                        </small>
                    </div>

                    <div class="form-group mb-3">
                        {{ form_label(form.donation_date) }}
                        {{ form_widget(form.donation_date) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.donation_date) }}
                        </div>
                        <small class="form-text text-muted">
                            La date ne peut pas être dans le futur
                        </small>
                    </div>

                    <div class="form-group mb-3">
                        {{ form_label(form.type) }}
                        {{ form_widget(form.type) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.type) }}
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    {% if form.payment_method is defined %}
                    <div class="form-group mb-3" id="payment-method-group">
                        {{ form_label(form.payment_method) }}
                        {{ form_widget(form.payment_method) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.payment_method) }}
                        </div>
                    </div>
                    {% endif %}

                    <div class="form-group mb-3">
                        {{ form_label(form.partner_id) }}
                        {{ form_widget(form.partner_id) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.partner_id) }}
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        {{ form_label(form.status) }}
                        {{ form_widget(form.status) }}
                        <div class="invalid-feedback d-block">
                            {{ form_errors(form.status) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 text-end">
        <a href="{{ path('app_back_donation_index') }}" class="btn btn-secondary">
            <i class="fas fa-times me-2"></i>Annuler
        </a>
        <button type="submit" class="btn btn-primary ms-2">
            <i class="fas fa-save me-2"></i>{{ button_label|default('Enregistrer') }}
        </button>
    </div>
{{ form_end(form) }}

{% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Flatpickr for date inputs
    flatpickr(".datepicker", {
        dateFormat: "Y-m-d",
        locale: "fr",
        allowInput: true,
        maxDate: "today",
        altInput: true,
        altFormat: "d/m/Y",
        defaultDate: new Date()
    });

    // Handle amount input validation
    const amountInput = document.querySelector('input[name="donation[amount]"]');
    if (amountInput) {
        amountInput.addEventListener('input', function(e) {
            // Remove any non-digit characters
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // Validate min/max
            const value = parseInt(this.value);
            if (value < 1) this.value = '1';
            if (value > 1000000) this.value = '1000000';
        });
    }

    // Handle donation type change
    const typeSelect = document.querySelector('select[name="donation[type]"]');
    const paymentMethodGroup = document.getElementById('payment-method-group');
    
    if (typeSelect && paymentMethodGroup) {
        typeSelect.addEventListener('change', function() {
            if (this.value === 'Monetary') {
                paymentMethodGroup.style.display = 'block';
                paymentMethodGroup.querySelector('select').required = true;
            } else {
                paymentMethodGroup.style.display = 'none';
                paymentMethodGroup.querySelector('select').required = false;
                paymentMethodGroup.querySelector('select').value = '';
            }
        });

        // Initial state
        if (typeSelect.value !== 'Monetary') {
            paymentMethodGroup.style.display = 'none';
            paymentMethodGroup.querySelector('select').required = false;
        }
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}
