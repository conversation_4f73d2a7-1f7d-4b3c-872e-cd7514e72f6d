<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class SentimentAnalyzer
{
    private const SENTIMENT_MAP = [
        'positive' => '😊',
        'neutral' => '😐',
        'negative' => '😔'
    ];

    private const SENTIMENT_WORDS = [
        'positive' => [
            'great', 'love', 'excellent', 'amazing', 'wonderful', 'fantastic',
            'helpful', 'good', 'impressive', 'happy', 'perfect', 'best',
            'beautiful', 'excited', 'thank', 'thanks', 'awesome', 'brilliant',
            'outstanding', 'superb', 'nice', 'well done', 'bravo'
        ],
        'negative' => [
            'terrible', 'hate', 'disappointing', 'poor', 'bad', 'awful',
            'horrible', 'wrong', 'worst', 'broken', 'useless', 'waste',
            'annoying', 'frustrated', 'disappointing', 'failed', 'failure',
            'problem', 'issue', 'bug', 'error', 'crash', 'not working'
        ]
    ];

    public function __construct(ParameterBagInterface $params)
    {
        // No initialization needed for this simpler implementation
    }

    public function analyzeSentiment(string $text): string
    {
        $text = strtolower($text);
        $words = preg_split('/\s+/', $text);
        
        $scores = [
            'positive' => 0,
            'negative' => 0
        ];

        foreach ($words as $word) {
            if (in_array($word, self::SENTIMENT_WORDS['positive'])) {
                $scores['positive']++;
            }
            if (in_array($word, self::SENTIMENT_WORDS['negative'])) {
                $scores['negative']++;
            }
        }

        // Check for negation words that could reverse sentiment
        $negations = ['not', 'no', "don't", 'doesnt', 'cant', 'cannot', 'never'];
        foreach ($negations as $negation) {
            if (stripos($text, $negation) !== false) {
                // Swap positive and negative scores when negation is found
                $temp = $scores['positive'];
                $scores['positive'] = $scores['negative'];
                $scores['negative'] = $temp;
                break;
            }
        }

        // Determine overall sentiment
        if ($scores['positive'] > $scores['negative']) {
            return self::SENTIMENT_MAP['positive'];
        } elseif ($scores['negative'] > $scores['positive']) {
            return self::SENTIMENT_MAP['negative'];
        } else {
            return self::SENTIMENT_MAP['neutral'];
        }
    }

    public function getEmoji(string $sentiment): string
    {
        return self::SENTIMENT_MAP[$sentiment] ?? self::SENTIMENT_MAP['neutral'];
    }
}
