{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'comment-form'}}) }}
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12 mb-3">
                    {{ form_label(form.content, 'Comment', {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.content, {
                        'attr': {
                            'class': 'form-control form-field',
                            'rows': 4,
                            'placeholder': 'Write your comment here...',
                            'minlength': '2',
                            'required': 'required'
                        }
                    }) }}
                    <div class="invalid-feedback">
                        Please enter a valid comment (minimum 2 characters)
                    </div>
                    <small class="form-text text-muted">Share your thoughts or feedback</small>
                </div>
            </div>

     {#       {% if form.upvotes is defined %}
            <div class="row">
                <div class="col-md-4 mb-3">
                    {{ form_label(form.upvotes, 'Upvotes', {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.upvotes, {
                        'attr': {
                            'class': 'form-control form-field',
                            'min': 0,
                            'type': 'number',
                            'required': 'required'
                        }
                    }) }}
                    <div class="invalid-feedback">
                        Please enter a valid number of upvotes (minimum 0)
                    </div>
                    <small class="form-text text-muted">Number of upvotes for this comment</small>
                </div>
            </div>
            {% endif %}
#}
            {# Hidden Forum ID field 
            {{ form_widget(form.postid, {
                'attr': {
                    'class': 'form-field',
                    'required': 'required',
                    'hidden': 'hidden'
                }
            }) }}
            #}

        </div>
       
    </div>
{{ form_end(form) }}

{% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formFields = document.querySelectorAll('.form-field');
    
    formFields.forEach(field => {
        // Validate on input (while typing)
        field.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Form submission validation
    document.getElementById('comment-form').addEventListener('submit', function(event) {
        let isValid = true;
        formFields.forEach(field => {
            if (!field.checkValidity()) {
                isValid = false;
                field.classList.add('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});
</script>
{% endblock %}
