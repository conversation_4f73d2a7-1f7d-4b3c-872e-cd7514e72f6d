{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col-auto">
      <a href="{{ path('app_command_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Orders">
        <i class="ri-arrow-left-line"></i>
      </a>
    </div>
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Edit Order</h1>
      <p class="text-muted mb-0">Update order information</p>
    </div>
    <div class="col-auto">
      <div class="d-flex gap-2">
        <a href="{{ path('app_command_show', {'id': command.id}) }}" class="btn btn-outline-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="View Order Details">
          <i class="ri-eye-line me-1"></i> View Details
        </a>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      {{ include('command/_form.html.twig', {'button_label': 'Update Order'}) }}
    </div>

    <div class="col-lg-4">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold text-danger">Danger Zone</h5>
        </div>
        <div class="card-body">
          <p class="text-muted mb-3">Deleting this order will permanently remove it from the system. This action cannot be undone.</p>
          {{ include('command/_delete_form.html.twig') }}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
{% endblock %}
