<?php

namespace App\Form;

use App\Entity\Challenge;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;

class ChallengeType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Challenge Name',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter challenge name',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a name']),
                    new Length([
                        'min' => 3,
                        'max' => 255,
                        'minMessage' => 'The name should be at least {{ limit }} characters',
                        'maxMessage' => 'The name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'empty_data' => false,
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 5,
                    'placeholder' => 'Enter challenge description',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a description']),
                ],
                'empty_data' => false,
            ])
            ->add('start', DateTimeType::class, [
                'label' => 'Start Date & Time',
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'form-control',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select a start date']),
                ],
                'empty_data' => false,
            ])
            ->add('end', DateTimeType::class, [
                'label' => 'End Date & Time',
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'form-control',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select an end date']),
                ],
                'empty_data' => false,
            ])
            ->add('image', FileType::class, [
                'label' => 'Challenge Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*',
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '2M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp',
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image (JPEG, PNG, WEBP)',
                    ])
                ],
                'empty_data' => $options['is_edit'] ? '' : false,
            ])
            ->add('duration', IntegerType::class, [
                'label' => 'Duration (in minutes)',
                'attr' => [
                    'class' => 'form-control',
                    'min' => 1,
                    'placeholder' => 'Enter duration in minutes',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter a duration']),
                ],
                'empty_data' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Challenge::class,
            'attr' => [
                'class' => 'needs-validation',
                'novalidate' => true,
            ],
            'is_edit' => false,
        ]);
    }
}
