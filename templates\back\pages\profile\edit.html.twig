{% extends 'front/base.html.twig' %}

{% block title %}Profile Settings{% endblock %}

{% block stylesheets %}
    <link rel="shortcut icon" href="{{asset('front/img/eco-net.png')}}" type="image/x-icon"/>

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:wght@400;500;700;900&display=swap" rel="stylesheet"/>

    <!-- Icon Font Stylesheet -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet"/>

    <!-- Libraries Stylesheet -->
    <link rel="stylesheet" href="{{asset('front/lib/animate/animate.min.css')}}"/>
    <link href="{{asset('front/lib/lightbox/css/lightbox.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('front/lib/owlcarousel/assets/owl.carousel.min.css')}}" rel="stylesheet"/>

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{asset('front/css/bootstrap.min.css')}}" rel="stylesheet"/>

    <!-- Template Stylesheet -->
    <link href="{{asset('front/css/style.css')}}" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Modern Profile Stylesheet -->
    <link rel="stylesheet" href="{{ asset('assets/css/profile-modern.css') }}">

    <!-- Custom styles for file input -->
    <style>
        .profile-file-input {
            /* Make the input cover the entire label area */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 10; /* Higher than other elements */
        }

        .profile-file-input-container {
            position: relative; /* Needed for absolute positioning of the input */
        }

        .profile-file-input-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-file-input-label:hover {
            background-color: #e9ecef;
            border-color: #6BB748;
        }

        .profile-file-input-icon {
            font-size: 2rem;
            color: #6BB748;
            margin-bottom: 0.5rem;
        }

        .profile-file-input-text {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .profile-file-input-subtext {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .profile-file-preview {
            display: flex;
            align-items: center;
            margin-top: 1rem;
            padding: 0.75rem;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .profile-file-preview-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 1rem;
        }

        .profile-file-preview-info {
            flex: 1;
        }

        .profile-file-preview-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
            word-break: break-all;
        }

        .profile-file-preview-size {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .profile-file-preview-remove {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 1rem;
            padding: 0.25rem;
        }

        .profile-file-preview-remove:hover {
            color: #bd2130;
        }

        /* Progress chart container styles */
        .progress-chart-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 0 auto 1.5rem;
        }

        .progress-chart-percentage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Global error handler to catch and log JavaScript errors
        window.addEventListener('error', function(event) {
            console.error('JavaScript Error:', event.message, 'at', event.filename, 'line', event.lineno);
        });

        // Track if Chart.js is fully loaded
        window.chartJsLoaded = false;
        if (typeof Chart !== 'undefined') {
            window.chartJsLoaded = true;
        } else {
            // Set up a listener to detect when Chart.js loads
            Object.defineProperty(window, 'Chart', {
                configurable: true,
                set: function(value) {
                    delete window.Chart;
                    window.Chart = value;
                    window.chartJsLoaded = true;
                    console.log('Chart.js loaded dynamically');
                }
            });
        }
    </script>
{% endblock %}

{% block content %}
    <!-- Navbar & Hero Start -->
    <div class="container-fluid position-relative p-0">
        {% include 'front/includes/navbar.html.twig' %}

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb-profile">
            <div class="container text-center py-5" style="max-width: 900px">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                    Profile
                </h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('app_home')}}">Home</a>
                    </li>
                    <li class="breadcrumb-item active text-primary">Profile</li>
                </ol>
            </div>
        </div>
        <!-- Header End -->
    </div>
    <!-- Navbar & Hero End -->

    <!-- Modern Profile Container -->
    <div class="profile-modern-container">

        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% if (label == 'success') %}
                {% for message in messages %}
                    <div class="profile-alert profile-alert-{{ label }}">
                        <i class="fas fa-{{ label == 'success' ? 'check-circle' : 'exclamation-circle' }}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endfor %}

        <!-- Profile Grid Layout -->
        <div class="profile-grid">
            <!-- Left Column - Profile Info -->
            <div class="profile-sidebar">
                <!-- Profile Image Card -->
                <div class="profile-card">
                    <div class="profile-image-container">
                        {% if user is defined and user is not null and user.image is defined and user.image is not null and user.image is not empty %}
                            <img src="http://localhost/img/{{ user.image }}" alt="Profile Image" class="profile-image">
                        {% else %}
                            <img src="{{ asset('front/img/default user.png') }}" alt="Profile Image" class="profile-image">
                        {% endif %}
                        <h3 class="profile-name">{{ user is defined and user is not null ? user.getFullName() : 'User' }}</h3>
                        <p class="profile-email">{{ user is defined and user is not null and user.email is defined ? user.email : '' }}</p>
                    </div>
                </div>

                <!-- Progress Statistics Card -->
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h2>Progress Statistics</h2>
                        <p>Your challenge completion progress</p>
                    </div>
                    <div class="profile-card-body">
                        <div class="progress-chart-container">
                            <canvas id="progressChart"></canvas>
                            <div class="progress-chart-percentage">{{ progressStats.percentage }}%</div>
                            <!-- Fallback display in case chart fails to load -->
                            <div id="progress-fallback" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: #f8f9fa; border-radius: 50%; overflow: hidden;">
                                <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: {{ progressStats.percentage }}%; background-color: #6BB748;"></div>
                            </div>
                        </div>
                        <div class="progress-stats">
                            <div class="progress-stat-item">
                                <span class="progress-stat-label">Total Challenges</span>
                                <span class="progress-stat-value">{{ progressStats.total }}</span>
                            </div>
                            <div class="progress-stat-item">
                                <span class="progress-stat-label">Completed</span>
                                <span class="progress-stat-value">{{ progressStats.completed }}</span>
                            </div>
                            <div class="progress-stat-item">
                                <span class="progress-stat-label">Remaining</span>
                                <span class="progress-stat-value">{{ progressStats.total - progressStats.completed }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Options Card -->
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h2>Security Options</h2>
                        <p>Keep your account secure</p>
                    </div>
                    <div class="profile-card-body">
                        <a href="{{ path('back_profile_app_change_password') }}" class="profile-btn profile-btn-secondary" style="width: 100%; margin-bottom: 1rem;">
                            <i class="fas fa-lock"></i> Change Password
                        </a>

                        {% if user is defined and user is not null and user.faceId is defined and user.faceId is not null and user.faceId is not empty %}
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <form action="{{ path('disable_face_id') }}" method="post" style="flex: 1;" onsubmit="return confirm('Are you sure you want to disable Face ID?')">
                                    <button type="submit" class="profile-btn profile-btn-danger" style="width: 100%;">
                                        <i class="fas fa-times-circle"></i> Disable Face ID
                                    </button>
                                </form>
                                <a href="{{ path('app_camera') }}" class="profile-btn profile-btn-secondary" style="flex: 1;">
                                    <i class="fas fa-camera"></i> Update Face ID
                                </a>
                            </div>
                        {% else %}
                            <a href="{{ path('app_camera') }}" class="profile-btn profile-btn-primary" style="width: 100%;">
                                <i class="fas fa-camera"></i> Enable Face ID
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Right Column - Profile Settings -->
            <div class="profile-main">
                <!-- Profile Settings Card -->
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h2>Profile Settings</h2>
                        <p>Manage your eco.net account information</p>
                    </div>
                    <div class="profile-card-body">
                        {{ form_start(form, {'attr': {'class': 'profile-form', 'id': 'profile-form'}}) }}
                            <div class="profile-form-group">
                                {{ form_label(form.full_name, 'Full Name', {'label_attr': {'class': 'profile-form-label'}}) }}
                                {{ form_widget(form.full_name, {'attr': {'class': 'profile-form-control'}}) }}
                                <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                                    {{ form_errors(form.full_name) }}
                                </span>
                            </div>

                            <div class="profile-form-group">
                                {{ form_label(form.email, 'Email Address', {'label_attr': {'class': 'profile-form-label'}}) }}
                                {{ form_widget(form.email, {'attr': {'class': 'profile-form-control'}}) }}
                                <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                                    {{ form_errors(form.email) }}
                                </span>
                            </div>

                            <div class="profile-form-group">
                                {{ form_label(form.gender, 'Gender', {'label_attr': {'class': 'profile-form-label'}}) }}
                                {{ form_widget(form.gender, {'attr': {'class': 'profile-form-control profile-form-select'}}) }}
                                <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                                    {{ form_errors(form.gender) }}
                                </span>
                            </div>

                            <div class="profile-form-group">
                                {{ form_label(form.image, 'Profile Image', {'label_attr': {'class': 'profile-form-label'}}) }}

                                <div class="profile-file-input-container" id="profile-file-container">
                                    {{ form_widget(form.image, {'attr': {'class': 'profile-file-input', 'id': 'profile-image-upload'}}) }}
                                    <label for="profile-image-upload" class="profile-file-input-label" id="profile-image-label">
                                        <i class="fas fa-cloud-upload-alt profile-file-input-icon" id="upload-icon"></i>
                                        <span class="profile-file-input-text" id="upload-text">Click to upload a profile image</span>
                                        <span class="profile-file-input-subtext" id="upload-subtext">Supports JPG, PNG or GIF up to 5MB</span>
                                    </label>
                                    <div id="file-preview" class="profile-file-preview" style="display: none;">
                                        <img id="preview-image" src="#" alt="Preview" class="profile-file-preview-image">
                                        <div class="profile-file-preview-info">
                                            <div id="file-name" class="profile-file-preview-name"></div>
                                            <div id="file-size" class="profile-file-preview-size"></div>
                                        </div>
                                        <button type="button" id="remove-file" class="profile-file-preview-remove">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <span style="color: #f44336; font-size: 0.875rem; margin-top: 0.5rem; display: block;">
                                    {{ form_errors(form.image) }}
                                </span>
                            </div>



                            <button type="submit" class="profile-btn profile-btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        {{ form_end(form) }}
                    </div>
                </div>

                <!-- Face ID Card -->
                {% if user is defined and user is not null and user.faceId is defined and user.faceId is not null and user.faceId is not empty %}
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h2>Face ID</h2>
                        <p>Your facial recognition security</p>
                    </div>
                    <div class="profile-card-body">
                        <p style="margin-bottom: 1.5rem;">Your Face ID is currently enabled and active for secure authentication.</p>
                        <div style="text-align: center;">
                            <img
                                src="http://localhost/img/{{ user.faceId }}"
                                alt="Face ID Image"
                                style="
                                    width: 200px;
                                    height: 200px;
                                    border-radius: 12px;
                                    object-fit: cover;
                                    display: block;
                                    margin: 0 auto;
                                    border: 3px solid #fff;
                                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                                "
                            >
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
    // Define chart initialization function in the global scope for reuse
    window.initializeProgressChart = function() {
        console.log('Progress chart initialization started');
        try {
            // Store chart data and options in variables for reuse
            const chartData = {
                labels: ['Completed', 'Remaining'],
                datasets: [{
                    data: [{{ progressStats.completed }}, {{ progressStats.total - progressStats.completed }}],
                    backgroundColor: ['#6BB748', '#E0E0E0'],
                    borderWidth: 0
                }]
            };

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: true,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            };

            // Function to show fallback display
            function showFallbackDisplay() {
                const fallbackElement = document.getElementById('progress-fallback');
                if (fallbackElement) {
                    fallbackElement.style.display = 'block';
                    console.log('Showing fallback progress display');
                }
            }

            // Function to initialize or reinitialize the chart
            function initProgressChart() {
                console.log('Initializing progress chart...');

                // Wait for Chart.js to be loaded if it's not already
                if (typeof Chart === 'undefined') {
                    console.warn('Chart.js not loaded yet, waiting...');
                    if (window.chartJsLoaded === true) {
                        console.error('Chart.js loading state inconsistent');
                    }

                    showFallbackDisplay();

                    // Try again in a moment
                    setTimeout(initProgressChart, 200);
                    return;
                }

                const progressChart = document.getElementById('progressChart');
                if (!progressChart) {
                    console.warn('Progress chart element not found');
                    showFallbackDisplay();
                    return;
                }

                // Check if canvas context is available
                let ctx;
                try {
                    ctx = progressChart.getContext('2d');
                    if (!ctx) {
                        console.warn('Could not get canvas context');
                        showFallbackDisplay();
                        return;
                    }
                } catch (e) {
                    console.error('Error getting canvas context:', e);
                    showFallbackDisplay();
                    return;
                }

                // Destroy existing chart if it exists
                if (window.progressChartInstance) {
                    try {
                        window.progressChartInstance.destroy();
                        console.log('Destroyed existing chart instance');
                    } catch (e) {
                        console.warn('Error destroying existing chart:', e);
                    }
                }

                // Create the chart
                try {
                    // Create new chart
                    window.progressChartInstance = new Chart(ctx, {
                        type: 'doughnut',
                        data: chartData,
                        options: chartOptions
                    });

                    console.log('Progress chart initialized successfully');

                    // Hide fallback if it was shown
                    const fallbackElement = document.getElementById('progress-fallback');
                    if (fallbackElement && fallbackElement.style.display === 'block') {
                        fallbackElement.style.display = 'none';
                    }
                } catch (e) {
                    console.error('Error creating chart:', e);
                    showFallbackDisplay();
                }
            }

            // Initialize the chart
            initProgressChart();

            // Reinitialize chart when page becomes visible (useful after navigation)
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'visible') {
                    console.log('Page became visible, reinitializing chart');
                    setTimeout(initProgressChart, 100); // Small delay to ensure DOM is ready
                }
            });

            // Also reinitialize on window resize for better responsiveness
            window.addEventListener('resize', function() {
                if (window.progressChartInstance) {
                    window.progressChartInstance.resize();
                }
            });

            // Handle form submission to ensure chart is reinitialized after page reload
            const profileForm = document.getElementById('profile-form');
            if (profileForm) {
                profileForm.addEventListener('submit', function() {
                    // Store a flag in sessionStorage to indicate we need to reinitialize the chart
                    sessionStorage.setItem('reinitializeChart', 'true');
                });
            }

            // Check if we need to reinitialize the chart (after form submission)
            if (sessionStorage.getItem('reinitializeChart') === 'true') {
                // Clear the flag
                sessionStorage.removeItem('reinitializeChart');
                console.log('Reinitializing chart after form submission');
                setTimeout(initProgressChart, 500);
            }

            // Return the initialization function for external use
            return initProgressChart;
        } catch (e) {
            console.error('Error in progress chart initialization:', e);
            return function() { console.error('Chart initialization failed'); };
        }
    };

    // Function to handle all navigation scenarios
    function setupChartWithNavigationSupport() {
        // Initialize on DOMContentLoaded (first page load)
        const initFunction = window.initializeProgressChart();

        // Handle Turbo navigation events
        document.addEventListener('turbo:load', function() {
            console.log('Turbo navigation detected, reinitializing chart');
            setTimeout(initFunction, 100);
        });

        document.addEventListener('turbo:render', function() {
            console.log('Turbo render detected, reinitializing chart');
            setTimeout(initFunction, 100);
        });

        // Set up a MutationObserver as a fallback to detect when the chart element is added to the DOM
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if our chart canvas was added
                    const chartCanvas = document.getElementById('progressChart');
                    if (chartCanvas && !window.progressChartInstance) {
                        console.log('Chart canvas detected by MutationObserver, initializing chart');
                        setTimeout(initFunction, 100);
                    }
                }
            });
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, { childList: true, subtree: true });

        // Also set up a periodic check as a last resort
        let checkCount = 0;
        const maxChecks = 5;
        const checkInterval = setInterval(function() {
            const chartCanvas = document.getElementById('progressChart');
            if (chartCanvas && (!window.progressChartInstance || window.progressChartInstance._active === undefined)) {
                console.log('Periodic check detected chart needs initialization');
                initFunction();
            }

            checkCount++;
            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
            }
        }, 1000);
    }

    // Set up the chart with navigation support when the DOM is ready
    document.addEventListener('DOMContentLoaded', setupChartWithNavigationSupport);

    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Get the actual form field ID that Symfony generates - try multiple selectors
            const formImageField = document.querySelector('input[type="file"].profile-file-input') ||
                                  document.querySelector('input[type="file"]') ||
                                  document.getElementById('profile_image');

            // Profile image upload functionality - use the actual ID from the rendered form
            const fileInput = formImageField;
            let filePreview = document.getElementById('file-preview');
            let previewImage = document.getElementById('preview-image');
            let fileName = document.getElementById('file-name');
            let fileSize = document.getElementById('file-size');
            let removeButton = document.getElementById('remove-file');

            // Debug info
            console.log('File input found:', fileInput ? fileInput.id || 'No ID' : 'Not found');
            console.log('File preview found:', filePreview ? 'Yes' : 'No');
            console.log('Preview image found:', previewImage ? 'Yes' : 'No');
            console.log('File name found:', fileName ? 'Yes' : 'No');
            console.log('File size found:', fileSize ? 'Yes' : 'No');
            console.log('Remove button found:', removeButton ? 'Yes' : 'No');

            // Create missing elements if needed
            if (!filePreview) {
                filePreview = document.createElement('div');
                filePreview.id = 'file-preview';
                filePreview.className = 'profile-file-preview';
                filePreview.style.display = 'none';

                // Add it after the file input
                if (fileInput && fileInput.parentNode) {
                    fileInput.parentNode.appendChild(filePreview);
                }
            }

            if (!previewImage) {
                previewImage = document.createElement('img');
                previewImage.id = 'preview-image';
                previewImage.className = 'profile-file-preview-image';
                previewImage.src = '#';
                previewImage.alt = 'Preview';

                if (filePreview) {
                    filePreview.appendChild(previewImage);
                }
            }

            if (!fileName) {
                fileName = document.createElement('div');
                fileName.id = 'file-name';
                fileName.className = 'profile-file-preview-name';

                if (filePreview) {
                    filePreview.appendChild(fileName);
                }
            }

            if (!fileSize) {
                fileSize = document.createElement('div');
                fileSize.id = 'file-size';
                fileSize.className = 'profile-file-preview-size';

                if (filePreview) {
                    filePreview.appendChild(fileSize);
                }
            }

            if (!removeButton) {
                removeButton = document.createElement('button');
                removeButton.id = 'remove-file';
                removeButton.className = 'profile-file-preview-remove';
                removeButton.type = 'button';
                removeButton.innerHTML = '<i class="fas fa-times"></i>';

                if (filePreview) {
                    filePreview.appendChild(removeButton);
                }
            }

            // Check if all required elements exist or were created
            if (fileInput) {
                // Format file size
                function formatFileSize(bytes) {
                    if (bytes < 1024) return bytes + ' bytes';
                    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
                    else return (bytes / 1048576).toFixed(1) + ' MB';
                }

                // Log that we found the file input
                console.log('File input found with ID:', fileInput.id || 'No ID');

                // Handle file selection
                fileInput.addEventListener('change', function(e) {
                    console.log('File input change event triggered');
                    if (this.files && this.files[0]) {
                        const file = this.files[0];
                        console.log('File selected:', file.name);

                        // Update preview
                        fileName.textContent = file.name;
                        fileSize.textContent = formatFileSize(file.size);

                        // Show image preview
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImage.src = e.target.result;
                            filePreview.style.display = 'flex';
                        };
                        reader.readAsDataURL(file);
                    }
                });

                // Handle remove button
                removeButton.addEventListener('click', function() {
                    fileInput.value = '';
                    filePreview.style.display = 'none';
                    previewImage.src = '#';
                });

                console.log('Profile image upload functionality initialized successfully');
            } else {
                console.warn('File input element is missing - cannot initialize profile image upload');
            }
        } catch (error) {
            console.error('Error initializing profile image upload:', error);
        }
    });
    </script>
{% endblock %}
