<?php

namespace App\Security;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use KnpU\OAuth2ClientBundle\Security\Authenticator\OAuth2Authenticator;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;
use Symfony\Component\Security\Http\EntryPoint\AuthenticationEntryPointInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class GithubAuthenticator extends OAuth2Authenticator implements AuthenticationEntryPointInterface
{
    public function __construct(
        private ClientRegistry $clientRegistry,
        private RouterInterface $router,
        private UserRepository $userRepository,
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher
    ) {
    }

    public function supports(Request $request): ?bool
    {
        return $request->attributes->get('_route') === 'back_auth_oauth_github_check';
    }

    public function authenticate(Request $request): Passport
    {
        $client = $this->clientRegistry->getClient('github');
        $accessToken = $this->fetchAccessToken($client);

        return new SelfValidatingPassport(
            new UserBadge($accessToken->getToken(), function() use ($accessToken, $client) {
                $githubUser = $client->fetchUserFromToken($accessToken);

                // Find or create user in your database
                $user = $this->userRepository->findOneBy(['email' => $githubUser->getEmail()/*, 'auth_method' => 'github'*/]);
                // $user_but_no_github = $this->userRepository->findOneBy(['email' => $githubUser->getEmail()]);
                
                if (!$user /*&& !$user_but_no_github*/) {
                    $user = new User();
                    $user->setEmail($githubUser->getEmail());

                    $hashedPassword = $this->passwordHasher->hashPassword(
                        $user,
                        'changeMe123'
                    );

                    $user->setPassword($hashedPassword);

                    // Set default values
                    $user->setRole('user');
                    $user->setAuthMethod('github');
                    $user->setVerified(true);
                    $user->setBanned(false);
                    $user->setCreatedAt(new \DateTimeImmutable());
                    $user->setImage('');
                    $user->setGender('male'); // Set a default gender that user can update later
                    $user->setFullName($githubUser->getName() ?? ''); // Get name from GitHub if available
                    $user->setAccountSetup(false); // New user needs to complete profile setup

                    $this->entityManager->persist($user);
                    $this->entityManager->flush();
                }

                return $user;
            })
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?RedirectResponse
    {
        $user = $token->getUser();
        
        // Always redirect new users to profile setup
        if (!$user->isAccountSetup()) {
            return new RedirectResponse($this->router->generate('back_auth_profile'));
        }
        
        // Redirect to home if account is already set up
        return new RedirectResponse($this->router->generate('app_home'));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?RedirectResponse
    {
        return new RedirectResponse($this->router->generate('back_auth_login'));
    }

    public function start(Request $request, AuthenticationException $authException = null): RedirectResponse
    {
        return new RedirectResponse($this->router->generate('back_auth_login'));
    }
}