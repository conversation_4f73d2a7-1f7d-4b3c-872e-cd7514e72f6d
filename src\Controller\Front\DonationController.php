<?php

namespace App\Controller\Front;

use App\Entity\Donation;
use App\Form\DonationType;
use App\Repository\DonationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/front/donation')]
class DonationController extends AbstractController
{
    #[Route('/', name: 'app_front_donation_index', methods: ['GET'])]
    public function index(DonationRepository $donationRepository, EntityManagerInterface $entityManager): Response
    {
        // Calculate total donations
        $totalDonations = $donationRepository->count([]);

        // Calculate total amount using a separate query builder
        $amountQb = $entityManager->createQueryBuilder();
        $totalAmount = $amountQb
            ->select('SUM(d.amount)')
            ->from(Donation::class, 'd')
            ->where('d.status != :status')
            ->setParameter('status', 'cancelled')
            ->getQuery()
            ->getSingleScalarResult() ?? 0;

        // Count unique donors using a separate query builder
        $donorsQb = $entityManager->createQueryBuilder();
        $donorsCount = $donorsQb
            ->select('COUNT(DISTINCT d.partner_id)')
            ->from(Donation::class, 'd')
            ->where('d.status != :status')
            ->setParameter('status', 'cancelled')
            ->getQuery()
            ->getSingleScalarResult();

        return $this->render('front/pages/donation/index.html.twig', [
            'total_donations' => $totalDonations,
            'total_amount' => $totalAmount,
            'donors_count' => $donorsCount,
        ]);
    }

    #[Route('/new', name: 'app_front_donation_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $donation = new Donation();
        
        // Set type if provided in query parameters
        if ($request->query->has('type')) {
            $donation->setType($request->query->get('type'));
        }
        
        $form = $this->createForm(DonationType::class, $donation);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Set initial status
            $donation->setStatus('pending');
            
            $entityManager->persist($donation);
            $entityManager->flush();

            $this->addFlash('success', 'Thank you for your donation! We will process it shortly.');
            return $this->redirectToRoute('app_front_donation_show', ['id' => $donation->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('front/pages/donation/new.html.twig', [
            'donation' => $donation,
            'form' => $form,
            'type' => $request->query->get('type'),
        ]);
    }

    #[Route('/{id}', name: 'app_front_donation_show', methods: ['GET'])]
    public function show(Donation $donation): Response
    {
        return $this->render('front/pages/donation/show.html.twig', [
            'donation' => $donation,
        ]);
    }

    #[Route('/{id}', name: 'app_front_donation_delete', methods: ['POST'])]
    public function delete(Request $request, Donation $donation, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$donation->getId(), $request->request->get('_token'))) {
            $entityManager->remove($donation);
            $entityManager->flush();
            $this->addFlash('success', 'The donation has been cancelled.');
        }

        return $this->redirectToRoute('app_front_donation_index', [], Response::HTTP_SEE_OTHER);
    }
}
