<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250215225313 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add created_at column to comments table';
    }

    public function up(Schema $schema): void
    {
        // Add the column as nullable first
        $this->addSql('ALTER TABLE comments ADD created_at DATETIME NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        
        // Update existing records with current timestamp
        $this->addSql('UPDATE comments SET created_at = NOW() WHERE created_at IS NULL');
        
        // Make the column non-nullable
        $this->addSql('ALTER TABLE comments MODIFY created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE comments DROP created_at');
    }
}
