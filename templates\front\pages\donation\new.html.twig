{% extends 'front/base.html.twig' %}

{% block title %}New Donation{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .donation-form-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .donation-form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .donation-form-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .donation-form-header h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .donation-form-header p {
            color: #6c757d;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .form-control {
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #ced4da;
        }
        
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        
        .btn-submit {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            border: none;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-back {
            color: #6c757d;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-top: 20px;
        }
        
        .btn-back:hover {
            color: #4CAF50;
        }
        
        .invalid-feedback {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 5px;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .donation-form-card {
                padding: 20px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class="donation-form-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="donation-form-card">
                        <div class="donation-form-header">
                            <h2>Make a {{ type|default('New') }} Donation</h2>
                            <p>Fill out the form below to complete your donation. All fields marked with * are required.</p>
                        </div>

                        {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.amount, 'Amount *', {'label_attr': {'class': 'form-label'}}) }}
                                        {{ form_widget(form.amount, {'attr': {'class': 'form-control' ~ (form.amount.vars.valid ? '' : ' is-invalid')}}) }}
                                        {{ form_errors(form.amount, {'attr': {'class': 'invalid-feedback'}}) }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.donation_date, 'Donation Date *', {'label_attr': {'class': 'form-label'}}) }}
                                        {{ form_widget(form.donation_date, {'attr': {'class': 'form-control' ~ (form.donation_date.vars.valid ? '' : ' is-invalid')}}) }}
                                        {{ form_errors(form.donation_date, {'attr': {'class': 'invalid-feedback'}}) }}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.type, 'Donation Type *', {'label_attr': {'class': 'form-label'}}) }}
                                        {{ form_widget(form.type, {'attr': {'class': 'form-select' ~ (form.type.vars.valid ? '' : ' is-invalid')}}) }}
                                        {{ form_errors(form.type, {'attr': {'class': 'invalid-feedback'}}) }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.payment_method, 'Payment Method *', {'label_attr': {'class': 'form-label'}}) }}
                                        {{ form_widget(form.payment_method, {'attr': {'class': 'form-select' ~ (form.payment_method.vars.valid ? '' : ' is-invalid')}}) }}
                                        {{ form_errors(form.payment_method, {'attr': {'class': 'invalid-feedback'}}) }}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.partner_id, 'Partner *', {'label_attr': {'class': 'form-label'}}) }}
                                        {{ form_widget(form.partner_id, {'attr': {'class': 'form-select' ~ (form.partner_id.vars.valid ? '' : ' is-invalid')}}) }}
                                        {{ form_errors(form.partner_id, {'attr': {'class': 'invalid-feedback'}}) }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form_label(form.status, 'Status *', {'label_attr': {'class': 'form-label'}}) }}
                                        {{ form_widget(form.status, {'attr': {'class': 'form-select' ~ (form.status.vars.valid ? '' : ' is-invalid')}}) }}
                                        {{ form_errors(form.status, {'attr': {'class': 'invalid-feedback'}}) }}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mt-4">
                                <button type="submit" class="btn-submit">
                                    <i class="fas fa-heart me-2"></i> Complete Donation
                                </button>
                            </div>
                        {{ form_end(form) }}

                        <a href="{{ path('app_front_donation_index') }}" class="btn-back">
                            <i class="fas fa-arrow-left"></i> Back to Donations
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
{% endblock %}
