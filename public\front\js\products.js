/**
 * EcoNet Products Page JavaScript
 * Advanced UI functionality for the products page
 */

// Base URL for assets
const asset_base_url = window.location.origin;

// Store original products HTML for reset functionality
let originalProductsHTML = null;

// DOM Elements
const productsContainer = document.getElementById('productsContainer');
const searchField = document.getElementById('searchField');
const sortField = document.getElementById('sortField');
const categoryFilter = document.getElementById('categoryFilter');
const btnSort = document.getElementById('btnSort');
const btnRecommend = document.getElementById('btnRecommend');
const btnClear = document.getElementById('btnClear');
const btnToggleView = document.getElementById('btnToggleView');

/**
 * Initialize the page
 */
function initProductsPage() {
    // Store original HTML for reset
    if (productsContainer) {
        originalProductsHTML = productsContainer.innerHTML;
    }

    // Add event listeners
    if (searchField) {
        searchField.addEventListener('input', filterProducts);
    }

    if (btnSort) {
        btnSort.addEventListener('click', sortProducts);
    }

    // Recommendation button functionality removed
    if (btnRecommend) {
        btnRecommend.style.display = 'none'; // Hide the recommendations button
    }

    if (btnClear) {
        btnClear.addEventListener('click', resetProducts);
    }

    if (btnToggleView) {
        btnToggleView.addEventListener('click', toggleProductView);
    }

    // Add animation classes to product cards
    animateProductCards();
}

/**
 * Filter products based on search input
 */
function filterProducts() {
    const searchValue = searchField.value.toLowerCase();
    const categoryValue = categoryFilter ? categoryFilter.value : 'all';
    const cards = document.querySelectorAll('.product-card');

    cards.forEach(card => {
        const name = card.getAttribute('data-name').toLowerCase();
        const category = card.getAttribute('data-category').toLowerCase();

        // Filter by name and category if selected
        const nameMatch = name.includes(searchValue);
        const categoryMatch = categoryValue === 'all' || category === categoryValue;

        // Show/hide based on filters
        card.style.display = (nameMatch && categoryMatch) ? '' : 'none';

        // Add animation when cards are shown
        if (nameMatch && categoryMatch) {
            card.classList.add('fade-in');
            setTimeout(() => {
                card.classList.remove('fade-in');
            }, 500);
        }
    });
}

/**
 * Sort products by the selected field
 */
function sortProducts() {
    const sortValue = sortField.value;
    const cards = Array.from(document.querySelectorAll('.product-card'));

    cards.sort((a, b) => {
        if (sortValue === 'name') {
            const aValue = a.getAttribute('data-name').toLowerCase();
            const bValue = b.getAttribute('data-name').toLowerCase();
            return aValue.localeCompare(bValue);
        } else if (sortValue === 'price-low') {
            const aValue = parseFloat(a.getAttribute('data-price'));
            const bValue = parseFloat(b.getAttribute('data-price'));
            return aValue - bValue;
        } else if (sortValue === 'price-high') {
            const aValue = parseFloat(a.getAttribute('data-price'));
            const bValue = parseFloat(b.getAttribute('data-price'));
            return bValue - aValue;
        }
    });

    // Clear container and append sorted cards
    productsContainer.innerHTML = '';
    cards.forEach((card, index) => {
        // Add delay for staggered animation
        setTimeout(() => {
            productsContainer.appendChild(card);
            card.classList.add('fade-in');
            setTimeout(() => {
                card.classList.remove('fade-in');
            }, 500);
        }, index * 50);
    });
}

/**
 * This function has been removed as per requirements to remove the product recommendations section
 */
function fetchRecommendations() {
    // Function is kept as a stub to prevent errors if called elsewhere
    console.log("Product recommendations feature has been removed");

    // Reset to original products if needed
    if (originalProductsHTML) {
        resetProducts();
    }

    return;
}

/**
 * This function has been removed as per requirements to remove the product recommendations section
 */
function renderRecommendedProducts(products, headerHTML = '') {
    // Function is kept as a stub to prevent errors if called elsewhere
    console.log("Product recommendations feature has been removed");
    return;
}

/**
 * Reset products to original state
 */
function resetProducts() {
    if (originalProductsHTML) {
        productsContainer.innerHTML = originalProductsHTML;

        // Re-add event listeners to Add to Cart buttons
        document.querySelectorAll('.btn-add-cart').forEach(button => {
            button.addEventListener('click', handleAddToCart);
        });

        // Reset filters
        if (searchField) searchField.value = '';
        if (sortField) sortField.value = 'name';
        if (categoryFilter) categoryFilter.value = 'all';

        // Animate cards
        animateProductCards();
    }
}

/**
 * Toggle between grid and list view
 */
function toggleProductView() {
    const productsGrid = document.querySelector('.products-grid');
    if (productsGrid) {
        productsGrid.classList.toggle('products-list-view');

        // Update button icon
        if (btnToggleView) {
            if (productsGrid.classList.contains('products-list-view')) {
                btnToggleView.innerHTML = '<i class="ri-grid-line"></i> Grid View';
            } else {
                btnToggleView.innerHTML = '<i class="ri-list-check"></i> List View';
            }
        }
    }
}

/**
 * Handle Add to Cart button click
 */
function handleAddToCart(event) {
    const button = event.currentTarget;
    const product = {
        id: button.dataset.id,
        name: button.dataset.name,
        price: parseFloat(button.dataset.price),
        image: button.dataset.image,
        category: button.dataset.category,
        quantity: 1
    };

    // Add to cart in localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    cart.push(product);
    localStorage.setItem('cart', JSON.stringify(cart));

    // Show success feedback
    showAddToCartFeedback(button, product.name);
}

/**
 * Show visual feedback when product is added to cart
 */
function showAddToCartFeedback(button, productName) {
    // Store original button text
    const originalText = button.innerHTML;

    // Change button to success state
    button.innerHTML = '<i class="ri-check-line"></i> Added!';
    button.style.backgroundColor = '#28a745';

    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.innerHTML = `
        <div class="toast-icon"><i class="ri-check-line"></i></div>
        <div class="toast-content">
            <p><strong>${productName}</strong> added to cart</p>
        </div>
    `;
    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Reset button after delay
    setTimeout(() => {
        button.innerHTML = originalText;
        button.style.backgroundColor = '';
    }, 2000);

    // Remove toast after delay
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * Animate product cards on page load
 */
function animateProductCards() {
    const cards = document.querySelectorAll('.product-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
            setTimeout(() => {
                card.classList.remove('fade-in');
            }, 500);
        }, index * 100);
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initProductsPage);

// Add event listeners to Add to Cart buttons
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.btn-add-cart').forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });
});
