<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250216113800 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Replace geoLocation with latitude and longitude coordinates and handle duplicate names';
    }

    public function up(Schema $schema): void
    {
        // First, let's handle duplicate names by appending a number to them
        $this->addSql('
            UPDATE partners p1
            JOIN (
                SELECT name, COUNT(*) as cnt
                FROM partners
                GROUP BY name
                HAVING cnt > 1
            ) p2 ON p1.name = p2.name
            SET p1.name = CONCAT(p1.name, "-", UUID())
            WHERE p1.id NOT IN (
                SELECT MIN(id)
                FROM partners
                GROUP BY name
            )
        ');

        // Add new columns for coordinates
        $this->addSql('ALTER TABLE partners ADD latitude DECIMAL(10, 8) NOT NULL DEFAULT 0, ADD longitude DECIMAL(11, 8) NOT NULL DEFAULT 0');
        
        // Drop the old column
        $this->addSql('ALTER TABLE partners DROP geoLocation');
        
        // Remove the default values
        $this->addSql('ALTER TABLE partners MODIFY latitude DECIMAL(10, 8) NOT NULL, MODIFY longitude DECIMAL(11, 8) NOT NULL');
        
        // Now we can safely add the unique constraint
        $this->addSql('ALTER TABLE partners ADD UNIQUE INDEX UNIQ_EFEB51645E237E06 (name)');
    }

    public function down(Schema $schema): void
    {
        // Remove unique constraint
        $this->addSql('ALTER TABLE partners DROP INDEX UNIQ_EFEB51645E237E06');
        
        // Remove the coordinate columns
        $this->addSql('ALTER TABLE partners DROP latitude, DROP longitude');
        
        // Add back the old column
        $this->addSql('ALTER TABLE partners ADD geoLocation VARCHAR(255) DEFAULT NULL');
    }
}
