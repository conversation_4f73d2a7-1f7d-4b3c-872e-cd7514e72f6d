{{ form_start(form) }}
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0 fw-bold">Order Information</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    {{ form_row(form.idUser, {
                        'attr': {'class': 'form-control'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                <div class="col-md-6">
                    {{ form_row(form.createAt, {
                        'attr': {'class': 'form-control'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                <div class="col-md-6">
                    {{ form_row(form.status, {
                        'attr': {'class': 'form-select'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                <div class="col-md-6">
                    {{ form_row(form.totalAmount, {
                        'attr': {'class': 'form-control'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                <div class="col-12">
                    {{ form_row(form.deliveryAddress, {
                        'attr': {'class': 'form-control'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                <div class="col-12">
                    {{ form_row(form.notes, {
                        'attr': {'class': 'form-control'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                {% if form.products is defined %}
                <div class="col-12">
                    {{ form_row(form.products, {
                        'attr': {'class': 'form-select'},
                        'label_attr': {'class': 'form-label'}
                    }) }}
                </div>
                {% endif %}
            </div>
        </div>
        <div class="card-footer bg-white py-3">
            <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i> {{ button_label|default('Save Order') }}
            </button>
            <a href="{{ path('app_command_index') }}" class="btn btn-outline-secondary ms-2">
                <i class="ri-arrow-left-line me-1"></i> Back to List
            </a>
        </div>
    </div>
{{ form_end(form) }}
