<?php

namespace App\EventListener;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Routing\RouterInterface;

class ProfileSetupListener
{
    private array $publicRoutes = [
        'back_auth_login',
        'back_auth_oauth_google',
        'back_auth_oauth_google_check',
        'back_auth_oauth_github',
        'back_auth_oauth_github_check',
        'back_auth_logout',
        'back_auth_forgot_password'
    ];

    public function __construct(
        private Security $security,
        private RouterInterface $router
    ) {
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $currentRoute = $request->attributes->get('_route');

        // Allow access to public routes
        if (in_array($currentRoute, $this->publicRoutes)) {
            return;
        }

        $user = $this->security->getUser();
        if (!$user) {
            return;
        }

        // If account is not set up and not accessing profile setup pages
        if (!$user->isAccountSetup() && 
            !in_array($currentRoute, ['back_auth_profile', 'back_auth_profile_update'])) {
            $request->getSession()->getFlashBag()->add(
                'warning',
                'Please complete your profile setup to access the application.'
            );
            $event->setResponse(new RedirectResponse(
                $this->router->generate('back_auth_login')
            ));
        }
    }
}