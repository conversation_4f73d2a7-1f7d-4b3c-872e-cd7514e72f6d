<?php

namespace App\Entity;

use App\Repository\CommentsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: CommentsRepository::class)]
class Comments
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 1000)]
    #[Assert\NotBlank(message: "Comment content cannot be empty")]
    #[Assert\Length(
        min: 1,
        max: 1000,
        minMessage: "Comment must be at least {{ limit }} characters long",
        maxMessage: "Comment cannot be longer than {{ limit }} characters"
    )]
    #[Assert\Type(
        type: 'string',
        message: 'The content must be a string'
    )]
    private ?string $content = null;

    #[ORM\Column]
    #[Assert\NotNull(message: "Upvotes count cannot be null")]
    #[Assert\Type(
        type: 'integer',
        message: 'The upvotes must be an integer'
    )]
    #[Assert\GreaterThanOrEqual(
        value: 0,
        message: 'Upvotes cannot be negative'
    )]
    private ?int $upvotes = 0;

    #[ORM\ManyToOne(inversedBy: 'comments')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Forums $forum = null;

    #[ORM\Column(name: "created_at")]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\ManyToOne(inversedBy: 'comments')]
    private ?User $user = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->upvotes = 0;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): static
    {
        $this->content = trim($content);
        return $this;
    }

    public function getUpvotes(): ?int
    {
        return $this->upvotes;
    }

    public function setUpvotes(int $upvotes): static
    {
        $this->upvotes = max(0, $upvotes);
        return $this;
    }

    public function getForum(): ?Forums
    {
        return $this->forum;
    }

    public function setForum(?Forums $forum): static
    {
        $this->forum = $forum;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    // Alias methods for backward compatibility
    public function getPostid(): ?Forums
    {
        return $this->getForum();
    }

    public function setPostid(?Forums $forum): static
    {
        return $this->setForum($forum);
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }
}
