/* Products Page Styling */
:root {
  --primary-color: #00D084;
  --primary-dark: #00b873;
  --primary-light: #33d99c;
  --primary-very-light: #e6f9f3;
  --secondary-color: #17303B;
  --secondary-light: #2a4652;
  --text-dark: #333333;
  --text-medium: #555555;
  --text-light: #777777;
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --border-color: #e0e0e0;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
}

/* Products Container */
.products-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 1.5rem;
  position: relative;
}

/* Products Header */
.products-header {
  margin-bottom: 2.5rem;
  position: relative;
  padding-bottom: 1.5rem;
}

.products-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: 2px;
}

.products-header h2 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.products-header h2::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40%;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.products-header p {
  font-size: 1.15rem;
  color: var(--text-medium);
  max-width: 800px;
  line-height: 1.6;
}

/* Filters Section */
.products-filters {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  padding: 1.75rem;
  margin-bottom: 2.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.products-filters:hover {
  box-shadow: var(--shadow-md);
}

.products-filters::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  align-items: center;
}

.search-input {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input input {
  padding-left: 3rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  height: 48px;
  width: 100%;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.search-input input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 208, 132, 0.15);
  outline: none;
}

.search-input i {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.search-input input:focus + i {
  color: var(--primary-dark);
}

.filter-select {
  min-width: 180px;
  position: relative;
}

.filter-select select {
  appearance: none;
  padding: 0.5rem 2.5rem 0.5rem 1.25rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  height: 48px;
  background-color: var(--bg-white);
  transition: all 0.3s ease;
  font-size: 1rem;
  width: 100%;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
  color: var(--text-medium);
}

.filter-select select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 208, 132, 0.15);
  outline: none;
  color: var(--text-dark);
}

.filter-select::after {
  content: '\25BC';
  position: absolute;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  pointer-events: none;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.filter-select:hover::after {
  transform: translateY(-50%) scale(1.1);
}

.filter-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-filter {
  padding: 0 1.5rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  transition: all 0.3s ease;
  height: 48px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
  z-index: -1;
}

.btn-filter:hover::before {
  left: 100%;
}

.btn-filter i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.btn-filter:hover i {
  transform: scale(1.1);
}

.btn-primary {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-light);
  border-color: var(--secondary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  color: var(--text-medium);
}

.btn-outline:hover {
  background-color: var(--bg-light);
  color: var(--primary-color);
  border-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); /* Slightly wider cards */
  gap: 2rem; /* Increased spacing between cards */
  margin-bottom: 2rem; /* Add bottom margin */
}

/* Recommendation section has been removed */

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Product Card */
.product-card {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden; /* Smoother transitions */
  will-change: transform, box-shadow; /* Optimize for animations */
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: rgba(0, 0, 0, 0.1);
  border-bottom: 3px solid var(--primary-color); /* Add a colored bottom border on hover */
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 208, 132, 0.1) 0%, rgba(0, 0, 0, 0) 50%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 1;
}

.product-card:hover::before {
  opacity: 1;
}

.product-image {
  position: relative;
  height: 280px; /* Increased height for more prominent images */
  overflow: hidden;
}

.product-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-image::after {
  opacity: 1;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease, filter 0.5s ease;
  filter: brightness(0.95); /* Slightly dimmed by default */
}

.product-card:hover .product-image img {
  transform: scale(1.05);
  filter: brightness(1.05); /* Brighten on hover for emphasis */
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
}

.no-image i {
  font-size: 3.5rem;
  color: #ccc;
  opacity: 0.7;
}

.product-badges {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 2;
}

.badge {
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.badge-eco {
  background-color: var(--primary-color);
  color: white;
}

.badge-stock {
  background-color: #3498db;
  color: white;
}

.badge-low {
  background-color: #f39c12;
  color: white;
}

.badge-out {
  background-color: #e74c3c;
  color: white;
}

.product-content {
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
  z-index: 2;
  background-color: var(--bg-white);
  border-top: 1px solid rgba(0, 0, 0, 0.03);
}

.product-category {
  font-size: 0.85rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
}

.product-category::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.product-title {
  font-size: 1.5rem; /* Larger font size */
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
  line-height: 1.3;
  transition: color 0.3s ease;
  position: relative;
  padding-bottom: 0.5rem;
}

.product-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.product-card:hover .product-title {
  color: var(--primary-color);
}

.product-card:hover .product-title::after {
  width: 60px;
}

.product-description {
  color: var(--text-medium);
  margin-bottom: 1.25rem;
  flex-grow: 1;
  line-height: 1.6; /* Improved line height for readability */
  font-size: 1.05rem; /* Slightly larger font size */
}

.product-price {
  font-size: 1.8rem; /* Larger font size */
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0.5rem 0;
}

.product-price::before {
  content: 'TND';
  font-size: 1rem;
  margin-right: 0.2rem;
  opacity: 0.7;
}

/* Add a subtle highlight effect */
.product-price::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
  opacity: 0.5;
  border-radius: 3px;
}

.product-actions {
  margin-top: auto;
}

.btn-add-cart {
  width: 100%;
  padding: 1rem; /* Slightly larger padding */
  border-radius: var(--radius-sm);
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  font-size: 1.05rem; /* Slightly larger font */
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px; /* Improved letter spacing */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Add shadow for depth */
}

.btn-add-cart::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.btn-add-cart:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-add-cart:hover::before {
  left: 100%;
}

.btn-add-cart:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-add-cart:disabled::before {
  display: none;
}

.btn-add-cart i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.btn-add-cart:hover i {
  transform: translateX(-3px);
}

/* QR Code */
.product-qr {
  margin: 1rem 0 1.5rem;
  position: relative;
  text-align: right; /* Align to the right side */
  display: flex;
  justify-content: flex-end; /* Position on the right */
}

.product-qr::before {
  content: 'Scan for details';
  position: absolute;
  top: -18px;
  right: 0; /* Align to the right */
  font-size: 0.75rem;
  color: var(--text-light);
  background-color: var(--bg-white);
  padding: 0 0.5rem;
}

.product-qr::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
  z-index: -1;
}

.product-qr img {
  max-width: 100px; /* Slightly larger QR code */
  border-radius: 8px;
  padding: 5px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-qr img:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .products-container {
    padding: 2.5rem 1.25rem;
  }

  .products-header h2 {
    font-size: 2rem;
  }

  .product-card {
    transform: none;
  }

  .product-card:hover {
    transform: translateY(-3px);
  }
}

@media (max-width: 992px) {
  .products-container {
    padding: 2rem 1rem;
  }

  .products-header {
    margin-bottom: 2rem;
  }

  .products-header h2 {
    font-size: 1.75rem;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input, .filter-select {
    width: 100%;
  }

  .filter-buttons {
    justify-content: space-between;
    width: 100%;
    margin-top: 0.5rem;
  }

  .btn-filter {
    flex: 1;
    padding: 0 1rem;
    font-size: 0.9rem;
  }

  .btn-filter i {
    font-size: 1.1rem;
  }

  .products-grid,
  .recommendation-products {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1.25rem;
  }

  .product-image {
    height: 200px;
  }

  .product-content {
    padding: 1.5rem;
  }

  .product-title {
    font-size: 1.25rem;
  }

  .product-price {
    font-size: 1.4rem;
  }
}

@media (max-width: 768px) {
  .products-container {
    padding: 1.5rem 0.75rem;
  }

  .products-header h2 {
    font-size: 1.5rem;
  }

  .products-header p {
    font-size: 1rem;
  }

  .products-grid,
  .recommendation-products {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
  }

  .product-image {
    height: 180px;
  }

  .product-content {
    padding: 1.25rem;
  }

  .product-title {
    font-size: 1.15rem;
    margin-bottom: 0.5rem;
  }

  .product-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .product-price {
    font-size: 1.3rem;
    margin-bottom: 1.25rem;
  }

  .btn-add-cart {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .product-qr img {
    max-width: 80px;
  }

  /* List view adjustments */
  .products-list-view .product-card {
    flex-direction: row;
    margin-bottom: 1rem;
  }

  .products-list-view .product-image {
    width: 35%;
    height: auto;
    min-height: 150px;
  }

  .products-list-view .product-content {
    width: 65%;
  }
}

@media (max-width: 576px) {
  .products-container {
    padding: 1.25rem 0.5rem;
  }

  .products-header {
    text-align: center;
    padding-bottom: 1.25rem;
  }

  .products-header::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .products-header h2 {
    font-size: 1.4rem;
    display: block;
  }

  .products-header h2::before {
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
  }

  .products-header p {
    font-size: 0.95rem;
  }

  .products-grid,
  .recommendation-products {
    grid-template-columns: 1fr;
  }

  .product-image {
    height: 200px;
  }

  .filter-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .btn-filter {
    font-size: 0.85rem;
    padding: 0 0.75rem;
    height: 42px;
  }

  /* List view adjustments for mobile */
  .products-list-view .product-card {
    flex-direction: column;
  }

  .products-list-view .product-image {
    width: 100%;
    height: 180px;
  }

  .products-list-view .product-content {
    width: 100%;
  }

  .recommendation-header {
    padding: 1.25rem;
    text-align: center;
  }

  .recommendation-title {
    justify-content: center;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
