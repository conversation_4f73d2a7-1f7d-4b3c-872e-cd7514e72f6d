{% extends 'back/base.html.twig' %}

{% block title %}Partner Details{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .partner-details {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-top: 2rem;
        }
        .detail-row {
            margin-bottom: 1.5rem;
        }
        .detail-label {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .detail-value {
            color: #34495e;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .page-title {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #4CAF50;
        }
        .back-link {
            color: #4CAF50;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .back-link:hover {
            color: #45a049;
        }
        .action-buttons {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        .partner-logo {
            width: 150px;
            height: 150px;
            object-fit: contain;
            border-radius: 10px;
            margin-bottom: 1rem;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container mt-4">
        <a href="{{ path('app_partners_index') }}" class="back-link">
            <i class="fas fa-arrow-left me-2"></i> Back to Partners List
        </a>

        <div class="partner-details">
            <h1 class="page-title">Partner Details</h1>

            {% if partner.logo %}
                {% set filename = partner.logo|split('\\')|last %}
                <div class="logo-container">
                    <img src="http://localhost/images/{{ filename }}"
                        alt="Logo of {{ partner.name }}"
                        class="partner-logo">
                </div>
            {% endif %}

            <div class="detail-row">
                <div class="detail-label">Name</div>
                <div class="detail-value">{{ partner.name }}</div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Type</div>
                <div class="detail-value">{{ partner.type }}</div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Description</div>
                <div class="detail-value">{{ partner.description }}</div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Geographic Location</div>
                <div class="detail-value">
                    Latitude: {{ partner.latitude }}<br>
                    Longitude: {{ partner.longitude }}
                </div>
            </div>

            <div class="action-buttons">
                <a href="{{ path('app_partners_edit', {'id': partner.id}) }}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i> Edit
                </a>
                <a href="{{ path('app_partners_index') }}" class="btn btn-secondary">
                    <i class="fas fa-list me-2"></i> Back to List
                </a>
                {{ include('back/pages/partners/_delete_form.html.twig', {'partner': partner}) }}
            </div>
        </div>
    </div>
{% endblock %}
