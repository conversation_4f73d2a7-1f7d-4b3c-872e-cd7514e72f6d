<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            background-color: #f9f9f9;
            margin: 0;
            padding: 20px;
        }

        h2 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header img {
            width: 100px; /* Adjust width as needed */
            height: auto;
            margin-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border: 1px solid #ddd;
            background-color: #ffffff;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 12px 15px;
            text-align: left;
        }

        th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #ddd;
        }

        td {
            color: #333;
        }

        .status {
            font-weight: bold;
            color: #f44336; /* Red color for 'False' */
        }

        .status.true {
            color: #4CAF50; /* Green color for 'True' */
        }

        .created-at {
            font-style: italic;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>User List</h2>
    </div>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Full Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Created At</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.fullName }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.role }}</td>
                    <td class="created-at">{{ user.createdAt|date('Y-m-d H:i:s') }}</td>
                    <td class="status {{ user.banned ? 'true' : 'false' }}">{{ user.banned ? 'True' : 'False' }}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
