{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Challenge Content Styles */
        .challenge-content-card {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            border-left: 4px solid #0d6efd;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .challenge-content-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .challenge-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .challenge-title i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        .challenge-description {
            color: #495057;
            line-height: 1.6;
            white-space: pre-line;
        }

        /* Info Card Styles */
        .info-card {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,.05);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .info-card-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .info-card-title i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Challenge Image Styles */
        .challenge-image-container {
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,.1);
            transition: all 0.3s ease;
        }

        .challenge-image-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,.15);
        }

        .challenge-image {
            width: 100%;
            height: auto;
            object-fit: cover;
            border-radius: 0.75rem;
        }

        .challenge-image-placeholder {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            padding: 3rem;
            text-align: center;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Status Badge */
        .status-badge {
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 500;
            border-radius: 50rem;
            display: inline-flex;
            align-items: center;
        }

        .status-badge i {
            margin-right: 0.25rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_challenge_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Challenges">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Challenge Details</h1>
                    <p class="text-muted mb-0">View and manage challenge information</p>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2">
                        <a href="{{ path('app_challenge_edit', {'id': challenge.id}) }}" class="btn btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Challenge">
                            <i class="ri-edit-line me-1"></i> Edit
                        </a>
                        <a href="{{ path('app_challenge_questions', {'id': challenge.id}) }}" class="btn btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Manage Questions">
                            <i class="ri-questionnaire-line me-1"></i> Questions
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Challenge Details -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-trophy-line text-primary me-2"></i> Challenge Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Challenge Content -->
                        <div class="challenge-content-card animate__animated animate__fadeIn">
                            <div class="challenge-title">
                                <i class="ri-award-line"></i> {{ challenge.name }}
                            </div>
                            <div class="challenge-description">{{ challenge.description }}</div>
                        </div>

                        <!-- Challenge Info Cards -->
                        <div class="row mb-4">
                            <div class="col-md-4 mb-4 mb-md-0">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                                    <div class="info-card-title">
                                        <i class="ri-calendar-line text-primary"></i> Start Date
                                    </div>
                                    <div class="info-card-value">
                                        {% if challenge.start %}
                                            <div class="d-flex align-items-center">
                                                <i class="ri-time-line text-muted me-2"></i>
                                                <div>
                                                    <div>{{ challenge.start|date('M d, Y') }}</div>
                                                    <small class="text-muted">{{ challenge.start|date('H:i') }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">Not set</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4 mb-md-0">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                                    <div class="info-card-title">
                                        <i class="ri-calendar-check-line text-success"></i> End Date
                                    </div>
                                    <div class="info-card-value">
                                        {% if challenge.end %}
                                            <div class="d-flex align-items-center">
                                                <i class="ri-time-line text-muted me-2"></i>
                                                <div>
                                                    <div>{{ challenge.end|date('M d, Y') }}</div>
                                                    <small class="text-muted">{{ challenge.end|date('H:i') }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">Not set</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                                    <div class="info-card-title">
                                        <i class="ri-time-line text-warning"></i> Duration
                                    </div>
                                    <div class="info-card-value">
                                        <span class="badge bg-primary-subtle text-primary rounded-pill px-3 py-2">
                                            <i class="ri-timer-line me-1"></i> {{ challenge.duration }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Challenge Status -->
                        {% set now = "now"|date('U') %}
                        {% set startTime = challenge.start ? challenge.start|date('U') : 0 %}
                        {% set endTime = challenge.end ? challenge.end|date('U') : 0 %}

                        {% if startTime > now %}
                            {% set status = 'upcoming' %}
                            {% set statusClass = 'bg-info-subtle text-info' %}
                            {% set statusIcon = 'ri-time-line' %}
                            {% set statusText = 'Upcoming' %}
                        {% elseif endTime < now and endTime > 0 %}
                            {% set status = 'completed' %}
                            {% set statusClass = 'bg-success-subtle text-success' %}
                            {% set statusIcon = 'ri-check-line' %}
                            {% set statusText = 'Completed' %}
                        {% elseif startTime <= now and (endTime >= now or endTime == 0) %}
                            {% set status = 'active' %}
                            {% set statusClass = 'bg-warning-subtle text-warning' %}
                            {% set statusIcon = 'ri-play-line' %}
                            {% set statusText = 'Active' %}
                        {% else %}
                            {% set status = 'draft' %}
                            {% set statusClass = 'bg-secondary-subtle text-secondary' %}
                            {% set statusIcon = 'ri-draft-line' %}
                            {% set statusText = 'Draft' %}
                        {% endif %}

                        <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.4s">
                            <div class="info-card-title">
                                <i class="ri-flag-line text-info"></i> Status
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="status-badge {{ statusClass }} rounded-pill px-3 py-2 me-3">
                                    <i class="{{ statusIcon }}"></i> {{ statusText }}
                                </span>

                                {% if status == 'upcoming' %}
                                    <span class="text-muted">Challenge will start in
                                        <strong>{{ ((startTime - now) / 86400)|round(0, 'floor') }} days</strong>
                                    </span>
                                {% elseif status == 'active' %}
                                    {% if endTime > 0 %}
                                        <span class="text-muted">Challenge will end in
                                            <strong>{{ ((endTime - now) / 86400)|round(0, 'floor') }} days</strong>
                                        </span>
                                    {% else %}
                                        <span class="text-muted">No end date specified</span>
                                    {% endif %}
                                {% elseif status == 'completed' %}
                                    <span class="text-muted">Challenge ended
                                        <strong>{{ ((now - endTime) / 86400)|round(0, 'floor') }} days ago</strong>
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted d-flex align-items-center">
                                <i class="ri-information-line me-1"></i> Challenge ID: {{ challenge.id }}
                            </div>
                            <div>
                                <form method="post" action="{{ path('app_challenge_delete', {'id': challenge.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this challenge? This action cannot be undone.');">
                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ challenge.id) }}">
                                    <button class="btn btn-outline-danger"
                                            data-bs-toggle="tooltip"
                                            data-bs-placement="top"
                                            title="Delete Challenge">
                                        <i class="ri-delete-bin-line me-1"></i> Delete Challenge
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Challenge Image -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-image-line text-primary me-2"></i> Challenge Image
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="challenge-image-container">
                            {% if challenge.image %}
                                <img src="http://localhost/{{ challenge.image }}"
                                     alt="{{ challenge.name }}"
                                     class="challenge-image">
                            {% else %}
                                <div class="challenge-image-placeholder">
                                    <i class="ri-image-line" style="font-size: 64px; color: #adb5bd;"></i>
                                    <p class="text-muted mt-3 mb-0">No image available</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer bg-white py-3">
                        <a href="{{ path('app_challenge_edit', {'id': challenge.id}) }}" class="btn btn-outline-primary w-100">
                            <i class="ri-image-edit-line me-1"></i> Change Image
                        </a>
                    </div>
                </div>

                <!-- Questions Quick Access -->
                <div class="card shadow-sm border-0 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-questionnaire-line text-primary me-2"></i> Questions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="ri-question-answer-line" style="font-size: 48px; color: #0dcaf0;"></i>
                            <h5 class="mt-3">Manage Challenge Questions</h5>
                            <p class="text-muted">Add, edit, or remove questions for this challenge</p>
                        </div>
                    </div>
                    <div class="card-footer bg-white py-3">
                        <a href="{{ path('app_challenge_questions', {'id': challenge.id}) }}" class="btn btn-info w-100">
                            <i class="ri-questionnaire-line me-1"></i> Manage Questions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
