{% extends 'front/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('front/css/products.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid position-relative p-0">
    {% include 'front/includes/navbar.html.twig' %}
    <div class="container-fluid bg-breadcrumb-products">
        <div class="container text-center py-5" style="max-width: 900px">
            <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">Eco-Friendly Products</h4>
            <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                <li class="breadcrumb-item"><a class="text-white" href="{{path('app_home')}}">Home</a></li>
                <li class="breadcrumb-item active text-white-50">Shop</li>
                <li class="breadcrumb-item active text-primary">Products</li>
            </ol>
        </div>
    </div>
</div>

<div class="products-container">
    <!-- Products Header -->
    <div class="products-header">
        <h2>Discover Our Eco-Friendly Products</h2>
        <p>Browse our selection of sustainable and environmentally friendly products designed to help you reduce your ecological footprint while enjoying high-quality items.</p>
    </div>

    <!-- Filters Section -->
    <div class="products-filters">
        <div class="filter-group">
            <!-- Search Input -->
            <div class="search-input">
                <i class="ri-search-line"></i>
                <input type="text" id="searchField" placeholder="Search products...">
            </div>

            <!-- Category Filter -->
            <div class="filter-select">
                <select id="categoryFilter">
                    <option value="all">All Categories</option>
                    <option value="clothes">Clothes</option>
                    <option value="electronics">Electronics</option>
                    <option value="food">Food</option>
                    <option value="books">Books</option>
                    <option value="others">Others</option>
                </select>
            </div>

            <!-- Sort Filter -->
            <div class="filter-select">
                <select id="sortField">
                    <option value="name">Name (A-Z)</option>
                    <option value="price-low">Price (Low to High)</option>
                    <option value="price-high">Price (High to Low)</option>
                </select>
            </div>

            <!-- Filter Buttons -->
            <div class="filter-buttons">
                <button id="btnSort" class="btn-filter btn-primary">
                    <i class="ri-sort-asc"></i> Sort
                </button>
                <!-- Recommendations button removed -->
                <button id="btnToggleView" class="btn-filter btn-outline">
                    <i class="ri-list-check"></i> List View
                </button>
                <button id="btnClear" class="btn-filter btn-outline">
                    <i class="ri-refresh-line"></i> Reset
                </button>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="products-grid" id="productsContainer">
        {% for product in products %}
            <div class="product-card" data-name="{{ product.nomP }}" data-price="{{ product.price }}" data-category="{{ product.categorie }}">
                <div class="product-image">
                    {% if product.image is not empty %}
                        <img src="http://localhost/img/{{ product.image }}" alt="{{ product.nomP }}">
                    {% else %}
                        <div class="no-image">
                            <i class="ri-image-line"></i>
                        </div>
                    {% endif %}
                    <div class="product-badges">
                        {% if product.isEcological %}
                            <span class="badge badge-eco"><i class="ri-leaf-line"></i> Eco</span>
                        {% endif %}

                        {% if product.stock > 10 %}
                            <span class="badge badge-stock"><i class="ri-stack-line"></i> In Stock</span>
                        {% elseif product.stock > 0 %}
                            <span class="badge badge-low"><i class="ri-error-warning-line"></i> Low Stock</span>
                        {% else %}
                            <span class="badge badge-out"><i class="ri-close-circle-line"></i> Out of Stock</span>
                        {% endif %}
                    </div>
                </div>
                <div class="product-content">
                    <div class="product-category">{{ product.categorie }}</div>
                    <h3 class="product-title">{{ product.nomP }}</h3>
                    <p class="product-description">{{ product.description }}</p>
                    <div class="product-price">{{ product.price }}</div>
                    <div class="product-qr">
                        <img src="{{ asset('uploads/qrcode/produit-' ~ product.id ~ '.png') }}" alt="QR code for {{ product.nomP }}">
                    </div>
                    <div class="product-actions">
                        <button class="btn-add-cart"
                                data-id="{{ product.id }}"
                                data-name="{{ product.nomP }}"
                                data-price="{{ product.price }}"
                                data-image="{{ product.image }}"
                                data-category="{{ product.categorie }}"
                                {% if product.stock <= 0 %} disabled {% endif %}
                        >
                            <i class="ri-shopping-cart-line"></i>
                            {% if product.stock <= 0 %}
                                Out of stock
                            {% else %}
                                Add to cart
                            {% endif %}
                        </button>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<!-- Toast Notification CSS -->
<style>
    .toast-notification {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        padding: 12px 16px;
        transform: translateY(100px);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 9999;
    }

    .toast-notification.show {
        transform: translateY(0);
        opacity: 1;
    }

    .toast-icon {
        background-color: var(--primary-color);
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
    }

    .toast-content {
        flex: 1;
    }

    .toast-content p {
        margin: 0;
    }

    /* List View Styles */
    .products-list-view {
        display: block;
    }

    .products-list-view .product-card {
        display: flex;
        flex-direction: row;
        margin-bottom: 1rem;
    }

    .products-list-view .product-image {
        width: 200px;
        height: auto;
    }

    .products-list-view .product-content {
        flex: 1;
    }

    @media (max-width: 768px) {
        .products-list-view .product-card {
            flex-direction: column;
        }

        .products-list-view .product-image {
            width: 100%;
        }
    }
</style>

<!-- Import the external JS file -->
<script src="{{ asset('front/js/products.js') }}"></script>
{% endblock %}
