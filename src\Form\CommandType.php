<?php

namespace App\Form;

use App\Entity\Command;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CommandType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('idUser', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'fullName',
                'label' => 'Customer',
                'placeholder' => 'Select a customer',
                'attr' => ['class' => 'form-select'],
            ])
            ->add('createAt', DateTimeType::class, [
                'widget' => 'single_text',
                'label' => 'Created At',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('status', ChoiceType::class, [
                'choices' => [
                    'Pending' => 'pending',
                    'Processing' => 'processing',
                    'Completed' => 'completed',
                    'Cancelled' => 'cancelled',
                ],
                'label' => 'Status',
                'attr' => ['class' => 'form-select'],
            ])
            ->add('totalAmount', NumberType::class, [
                'label' => 'Total Amount',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('deliveryAddress', TextType::class, [
                'label' => 'Delivery Address',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('notes', TextareaType::class, [
                'label' => 'Notes',
                'attr' => ['class' => 'form-control', 'rows' => 3],
                'required' => false,
            ])
            ->add('products', EntityType::class, [
                'class' => 'App\Entity\Product',
                'choice_label' => 'nomP',
                'label' => 'Products',
                'multiple' => true,
                'expanded' => false,
                'attr' => ['class' => 'form-select'],
                'required' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Command::class,
        ]);
    }
}
