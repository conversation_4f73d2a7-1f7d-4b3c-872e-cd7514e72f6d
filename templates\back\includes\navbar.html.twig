{% block navbar %}
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button type="button" id="sidebarCollapse" class="btn sidebar-toggle">
                    <i class="ri-menu-line"></i>
                </button>

                <div class="page-title d-none d-md-block ms-3">
                    <h4 class="mb-0">{{ app.request.get('_route')|replace({'_': ' ', 'back': '', 'admin': '', 'app': ''})|title }}</h4>
                </div>
            </div>

            <div class="d-flex align-items-center">
                <div class="nav-item me-3 d-none d-md-block">
                    <a href="{{ path('app_home') }}" class="btn btn-outline-success rounded-pill" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Return to Website">
                        <i class="ri-home-line"></i>
                    </a>
                </div>

                {% if is_granted('IS_AUTHENTICATED_FULLY') %}
                    <div class="nav-item dropdown">
                        <button class="btn user-dropdown" type="button" id="userDropdown" data-bs-toggle="dropdown">
                            <div class="user-avatar">
                                <span>{{ app.user.fullName|slice(0, 1)|upper }}</span>
                            </div>
                            <span class="d-none d-md-inline user-name ms-2"> {{ app.user.fullName }}</span>
                            <i class="ri-arrow-down-s-line ms-1"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end mt-2">
                            <li class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar-lg me-3">
                                        <span>{{ app.user.fullName|slice(0, 1)|upper }}</span>
                                    </div>
                                    <div>
                                        <strong>{{ app.user.fullName }}</strong>
                                        <div class="small text-muted">{{ app.user.email }}</div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ path('back_profile_edit') }}">
                                    <i class="ri-user-settings-line"></i> Profile Settings
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ path('back_profile_app_change_password') }}">
                                    <i class="ri-lock-password-line"></i> Change Password
                                </a>
                            </li>

                            <li>
                                <a class="dropdown-item" href="{{ path('app_home') }}">
                                    <i class="ri-home-line"></i> Return to Website
                                </a>
                            </li>

                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="{{ path('back_auth_logout') }}">
                                    <i class="ri-logout-box-line"></i> Logout
                                </a>
                            </li>
                        </ul>
                    </div>
                {% endif %}
            </div>
        </div>
    </nav>
{% endblock %}