{% extends 'back/pages/home/<USER>' %}

{% block dash %} {% endblock %}
{% block forum %}{% endblock %}
{% block cmt %}active{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Comment Content Styles */
        .comment-content-card {
            background-color: #f8f9fa;
            border-radius: 0.75rem;
            border-left: 4px solid #0dcaf0;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .comment-content-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .comment-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .comment-title i {
            margin-right: 0.5rem;
            color: #0dcaf0;
        }

        .comment-content {
            color: #495057;
            line-height: 1.6;
            white-space: pre-line;
        }

        /* Info Card Styles */
        .info-card {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,.05);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,.05);
        }

        .info-card-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .info-card-title i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .info-card-value {
            font-size: 1rem;
            font-weight: 500;
            color: #212529;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 36px;
            height: 36px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Button Styles */
        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Upvotes Counter */
        .upvotes-counter {
            font-size: 3rem;
            font-weight: 700;
            color: #198754;
            line-height: 1;
        }

        .upvotes-label {
            font-size: 0.875rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col-auto">
                    <a href="{{ path('app_comments_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Comments">
                        <i class="ri-arrow-left-line"></i>
                    </a>
                </div>
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Comment Details</h1>
                    <p class="text-muted mb-0">View and manage comment information</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_comments_edit', {'id': comment.id}) }}" class="btn btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit Comment">
                        <i class="ri-edit-line me-1"></i> Edit
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold d-flex align-items-center">
                            <i class="ri-chat-1-line text-info me-2"></i> Comment Information
                        </h5>
                        <div>
                            <span class="badge bg-success-subtle text-success rounded-pill px-3 py-2">
                                <i class="ri-thumb-up-line me-1"></i> {{ comment.upvotes }} Upvotes
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Comment Content -->
                        <div class="comment-content-card animate__animated animate__fadeIn">
                            <div class="comment-title">
                                <i class="ri-chat-quote-line"></i> Comment Content
                            </div>
                            <div class="comment-content">{{ comment.content|nl2br }}</div>
                        </div>

                        <!-- Comment Info Cards -->
                        <div class="row mb-4">
                            <div class="col-md-6 mb-4 mb-md-0">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                                    <div class="info-card-title">
                                        <i class="ri-discuss-line text-primary"></i> Related Forum
                                    </div>
                                    <div class="info-card-value mb-2">
                                        {{ comment.postid.title }}
                                    </div>
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="ri-calendar-line text-muted me-2"></i>
                                        <div class="text-muted">
                                            {{ comment.postid.createdat ? comment.postid.createdat|date('F d, Y h:i A') : 'Not specified' }}
                                        </div>
                                    </div>
                                    <a href="{{ path('app_forums_show', {'id': comment.postid.id}) }}" class="btn btn-sm btn-outline-info">
                                        <i class="ri-external-link-line me-1"></i> View Forum
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                                    <div class="info-card-title">
                                        <i class="ri-thumb-up-line text-success"></i> Engagement
                                    </div>
                                    <div class="text-center py-3">
                                        <div class="upvotes-counter">{{ comment.upvotes }}</div>
                                        <div class="upvotes-label">Upvotes received</div>
                                    </div>
                                    <div class="progress mt-2" style="height: 6px;">
                                        {% set percentage = comment.upvotes > 0 ? 100 : 0 %}
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ percentage }}%;" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Information -->
                        {% if comment.user %}
                        <div class="info-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                            <div class="info-card-title">
                                <i class="ri-user-line text-warning"></i> User Information
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="avatar me-3 bg-warning-subtle">
                                    <span class="avatar-text text-warning">{{ comment.user.getFullName()|slice(0,1)|upper }}</span>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ comment.user.getFullName() }}</div>
                                    <div class="text-muted small">{{ comment.user.email }}</div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted d-flex align-items-center">
                                <i class="ri-information-line me-1"></i> Comment ID: {{ comment.id }}
                            </div>
                            <div>
                                {{ include('back/pages/comments/_delete_form.html.twig', {
                                    'button_class': 'btn-outline-danger',
                                    'button_icon': 'ri-delete-bin-line me-1',
                                    'button_text': 'Delete Comment'
                                }) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
