{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'forum-form'}}) }}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-9">
            <div class="forum-create-card">
                <div class="forum-create-header">
                    <div class="forum-create-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h3 class="forum-create-title">Share Your Thoughts</h3>
                    <p class="forum-create-subtitle">Create a new post to share with the community</p>
                </div>

                <div class="forum-create-body">
                    <!-- Title Field -->
                    <div class="forum-field-container">
                        <div class="forum-field-header">
                            {{ form_label(form.title, 'Title', {'label_attr': {'class': 'forum-field-label'}}) }}
                            <div class="forum-field-counter" id="title-counter">0/255</div>
                        </div>

                        <div class="forum-field-wrapper">
                            <div class="forum-field-icon">
                                <i class="fas fa-heading"></i>
                            </div>
                            {{ form_widget(form.title, {
                                'attr': {
                                    'class': 'forum-field-input',
                                    'placeholder': 'Enter a descriptive title for your post',
                                    'data-grammar-check': 'true',
                                    'maxlength': '255'
                                }
                            }) }}
                        </div>

                        {% if form_errors(form.title) %}
                            <div class="forum-field-error">
                                {{ form_errors(form.title) }}
                            </div>
                        {% endif %}

                        <div id="title-grammar-suggestions" class="grammar-suggestions"></div>

                        <div class="forum-field-help">
                            <i class="fas fa-info-circle"></i>
                            <span>Choose a clear, descriptive title (3-255 characters, alphanumeric)</span>
                        </div>
                    </div>

                    <!-- Content Field -->
                    <div class="forum-field-container">
                        <div class="forum-field-header">
                            {{ form_label(form.content, 'Content', {'label_attr': {'class': 'forum-field-label'}}) }}
                            <div class="forum-field-counter" id="content-counter">0 characters</div>
                        </div>

                        <div class="forum-field-wrapper textarea-wrapper">
                            {{ form_widget(form.content, {
                                'attr': {
                                    'class': 'forum-field-textarea',
                                    'placeholder': 'Share your thoughts, ideas, or questions with the community...',
                                    'rows': '10',
                                    'data-grammar-check': 'true'
                                }
                            }) }}
                        </div>

                        {% if form_errors(form.content) %}
                            <div class="forum-field-error">
                                {{ form_errors(form.content) }}
                            </div>
                        {% endif %}

                        <div id="content-grammar-suggestions" class="grammar-suggestions"></div>

                        <div class="forum-field-help">
                            <i class="fas fa-info-circle"></i>
                            <span>Describe your topic in detail (minimum 10 characters). Use #hashtags to categorize your post.</span>
                        </div>

                        <div class="forum-hashtag-preview" id="hashtag-preview">
                            <div class="forum-hashtag-title">Detected Tags:</div>
                            <div class="forum-hashtag-container" id="hashtag-container"></div>
                        </div>
                    </div>

                    {{ form_row(form.createdat, { 'attr': {'style': 'display: none;'} }) }}

                    <div class="forum-actions">
                        <a href="{{ path('front_forums_index') }}" class="forum-btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back to Forums</span>
                        </a>
                        <button type="submit" class="forum-btn-primary" id="submit-button">
                            <i class="fas fa-paper-plane"></i>
                            <span>{{ button_label|default('Publish Post') }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --forum-primary: #00D084;
    --forum-primary-dark: #00b873;
    --forum-primary-light: #7eebc5;
    --forum-primary-very-light: #e6f9f3;
    --forum-secondary: #17303B;
    --forum-text-dark: #333333;
    --forum-text-medium: #555555;
    --forum-text-light: #777777;
    --forum-bg-light: #f8f9fa;
    --forum-bg-white: #ffffff;
    --forum-border-color: #e0e0e0;
    --forum-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
    --forum-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
    --forum-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    --forum-radius-sm: 8px;
    --forum-radius-md: 12px;
    --forum-radius-lg: 16px;
    --forum-radius-xl: 24px;
}

/* Main Card Styling */
.forum-create-card {
    background-color: var(--forum-bg-white);
    border-radius: var(--forum-radius-lg);
    box-shadow: var(--forum-shadow-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 2rem;
}

/* Header Section */
.forum-create-header {
    background: linear-gradient(135deg, var(--forum-primary) 0%, var(--forum-primary-dark) 100%);
    padding: 2.5rem 2rem;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.forum-create-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.1)' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
}

.forum-create-icon {
    width: 70px;
    height: 70px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.forum-create-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.forum-create-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 500px;
    margin: 0 auto;
}

/* Body Section */
.forum-create-body {
    padding: 2.5rem;
}

/* Field Styling */
.forum-field-container {
    margin-bottom: 2rem;
    position: relative;
}

.forum-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.forum-field-label {
    font-weight: 600;
    color: var(--forum-text-dark);
    font-size: 1.1rem;
    margin: 0;
}

.forum-field-counter {
    font-size: 0.85rem;
    color: var(--forum-text-light);
    transition: all 0.2s ease;
}

.forum-field-counter.warning {
    color: #ffc107;
}

.forum-field-counter.danger {
    color: #dc3545;
}

.forum-field-wrapper {
    position: relative;
    transition: all 0.3s ease;
}

.forum-field-wrapper.focused {
    transform: translateY(-2px);
}

.forum-field-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--forum-primary);
    font-size: 1.1rem;
    z-index: 2;
}

.forum-field-input, .forum-field-textarea {
    width: 100%;
    padding: 1rem 1rem 1rem 2.75rem;
    border: 2px solid var(--forum-border-color);
    border-radius: var(--forum-radius-md);
    background-color: var(--forum-bg-white);
    transition: all 0.3s ease;
    font-size: 1rem;
    color: var(--forum-text-dark);
}

.forum-field-textarea {
    padding: 1rem;
    resize: vertical;
    min-height: 200px;
}

.textarea-wrapper {
    border-radius: var(--forum-radius-md);
    overflow: hidden;
    box-shadow: var(--forum-shadow-sm);
}

.forum-field-input:focus, .forum-field-textarea:focus {
    outline: none;
    border-color: var(--forum-primary);
    box-shadow: 0 0 0 4px rgba(0, 208, 132, 0.15);
}

.forum-field-input::placeholder, .forum-field-textarea::placeholder {
    color: var(--forum-text-light);
    opacity: 0.7;
}

.forum-field-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.forum-field-help {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.75rem;
    color: var(--forum-text-light);
    font-size: 0.875rem;
}

.forum-field-help i {
    color: var(--forum-primary);
}

/* Grammar Suggestions */
.grammar-suggestions {
    font-size: 0.875rem;
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: var(--forum-radius-sm);
    padding: 1rem;
    margin-top: 0.75rem;
    display: none;
    box-shadow: var(--forum-shadow-sm);
    animation: fadeIn 0.3s ease;
}

.grammar-suggestions-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(133, 100, 4, 0.2);
    font-weight: 600;
}

.grammar-suggestions-header i {
    color: #ffc107;
}

.grammar-suggestions-body {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.grammar-suggestion-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: var(--forum-radius-sm);
}

.grammar-error-text, .grammar-suggestion-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.grammar-error-text i {
    color: #ffc107;
}

.grammar-suggestion-text i {
    color: #28a745;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.grammar-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.15) !important;
}

/* Hashtag Preview */
.forum-hashtag-preview {
    margin-top: 1.5rem;
    background-color: var(--forum-primary-very-light);
    border-radius: var(--forum-radius-md);
    padding: 1rem;
    display: none;
}

.forum-hashtag-preview.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.forum-hashtag-title {
    font-weight: 600;
    color: var(--forum-primary-dark);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.forum-hashtag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.forum-hashtag {
    background-color: white;
    color: var(--forum-primary);
    border: 1px solid var(--forum-primary-light);
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
}

.forum-hashtag:hover {
    background-color: var(--forum-primary);
    color: white;
}

/* Action Buttons */
.forum-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2.5rem;
}

.forum-btn-primary, .forum-btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.forum-btn-primary {
    background: linear-gradient(135deg, var(--forum-primary) 0%, var(--forum-primary-dark) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 208, 132, 0.3);
}

.forum-btn-primary {
    position: relative;
    overflow: hidden;
}

.forum-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 208, 132, 0.4);
}

.forum-btn-primary:active {
    transform: translateY(-1px);
}

.forum-btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.forum-btn-primary:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

.forum-btn-secondary {
    background-color: #f8f9fa;
    color: var(--forum-text-medium);
    border: 1px solid var(--forum-border-color);
}

.forum-btn-secondary:hover {
    background-color: #e9ecef;
    color: var(--forum-text-dark);
}
</style>

<script>
const TEXTGEARS_API_KEY = 'AEFOCqMSNWL85pVP';
const debounceTimeout = 1000;

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

async function checkGrammar(text, suggestionsDivId, inputElement) {
    if (!text || text.trim().length < 3) return;

    try {
        const response = await fetch(`https://api.textgears.com/check.php?text=${encodeURIComponent(text)}&key=${TEXTGEARS_API_KEY}`);
        const data = await response.json();

        const suggestionsDiv = document.getElementById(suggestionsDivId);
        suggestionsDiv.innerHTML = '';

        if (data.errors && data.errors.length > 0) {
            const suggestions = data.errors.map(error => {
                return `<div class="grammar-suggestion-item">
                    <div class="grammar-error-text">
                        <i class="fas fa-exclamation-circle text-warning"></i>
                        <span>${error.bad}</span>
                    </div>
                    <div class="grammar-suggestion-text">
                        <i class="fas fa-lightbulb text-success"></i>
                        <span>${error.better.join(', ')}</span>
                    </div>
                </div>`;
            }).join('');

            suggestionsDiv.innerHTML = `
                <div class="grammar-suggestions-header">
                    <i class="fas fa-spell-check"></i>
                    <span>Writing Suggestions</span>
                </div>
                <div class="grammar-suggestions-body">
                    ${suggestions}
                </div>
            `;

            suggestionsDiv.style.display = 'block';
            inputElement.classList.add('grammar-error');
        } else {
            suggestionsDiv.style.display = 'none';
            inputElement.classList.remove('grammar-error');
        }
    } catch (error) {
        console.error('Grammar check failed:', error);
    }
}

// Function to extract hashtags from text
function extractHashtags(text) {
    const hashtagRegex = /#(\w+)/g;
    const matches = text.match(hashtagRegex);

    if (!matches) return [];

    // Remove duplicates and the # symbol
    return [...new Set(matches)].map(tag => tag.substring(1));
}

// Function to update character counter
function updateCounter(input, counterId, maxLength = null) {
    const counter = document.getElementById(counterId);
    const length = input.value.length;

    if (maxLength) {
        counter.textContent = `${length}/${maxLength}`;

        // Add warning classes based on length
        if (length > maxLength * 0.8 && length <= maxLength) {
            counter.classList.add('warning');
            counter.classList.remove('danger');
        } else if (length > maxLength) {
            counter.classList.add('danger');
            counter.classList.remove('warning');
        } else {
            counter.classList.remove('warning', 'danger');
        }
    } else {
        counter.textContent = `${length} characters`;
    }
}

// Function to update hashtag preview
function updateHashtagPreview(text) {
    const hashtags = extractHashtags(text);
    const container = document.getElementById('hashtag-container');
    const preview = document.getElementById('hashtag-preview');

    if (hashtags.length > 0) {
        container.innerHTML = hashtags.map(tag =>
            `<span class="forum-hashtag">#${tag}</span>`
        ).join('');

        preview.classList.add('active');
    } else {
        preview.classList.remove('active');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.querySelector('input[data-grammar-check]');
    const contentInput = document.querySelector('textarea[data-grammar-check]');
    const submitButton = document.getElementById('submit-button');
    const form = document.getElementById('forum-form');

    // Initialize counters
    if (titleInput) {
        updateCounter(titleInput, 'title-counter', 255);
    }

    if (contentInput) {
        updateCounter(contentInput, 'content-counter');
        updateHashtagPreview(contentInput.value);
    }

    // Set up debounced grammar checks
    const debouncedTitleCheck = debounce((value) => {
        checkGrammar(value, 'title-grammar-suggestions', titleInput);
    }, debounceTimeout);

    const debouncedContentCheck = debounce((value) => {
        checkGrammar(value, 'content-grammar-suggestions', contentInput);
    }, debounceTimeout);

    // Add event listeners for title input
    if (titleInput) {
        titleInput.addEventListener('input', (e) => {
            updateCounter(titleInput, 'title-counter', 255);
            debouncedTitleCheck(e.target.value);
        });

        titleInput.addEventListener('focus', () => {
            titleInput.parentElement.classList.add('focused');
        });

        titleInput.addEventListener('blur', () => {
            titleInput.parentElement.classList.remove('focused');
        });
    }

    // Add event listeners for content input
    if (contentInput) {
        contentInput.addEventListener('input', (e) => {
            updateCounter(contentInput, 'content-counter');
            updateHashtagPreview(e.target.value);
            debouncedContentCheck(e.target.value);
        });
    }

    // Add form submission animation
    if (form && submitButton) {
        form.addEventListener('submit', function(e) {
            if (form.checkValidity()) {
                submitButton.innerHTML = '<div class="spinner-border spinner-border-sm text-white" role="status"></div><span class="ms-2">Publishing...</span>';
                submitButton.disabled = true;
            }
        });
    }
});
</script>

{{ form_end(form) }}