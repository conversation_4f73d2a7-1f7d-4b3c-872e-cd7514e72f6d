{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col-auto">
      <a href="{{ path('app_product_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Products">
        <i class="ri-arrow-left-line"></i>
      </a>
    </div>
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Add New Product</h1>
      <p class="text-muted mb-0">Create a new product in the system</p>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Product Information</h5>
        </div>
        <div class="card-body">
          {{ form_start(form,{
            'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}
          }) }}
            <div class="row g-3">
              <div class="col-md-6 mb-3">
                <div class="form-floating">
                  {{ form_widget(form.nom_p, {
                    'attr': {
                      'class': 'form-control' ~ (form.nom_p.vars.errors|length > 0 ? ' is-invalid' : ''),
                      'placeholder': 'Product Name'
                    }
                  }) }}
                  {{ form_label(form.nom_p, 'Product Name') }}
                  {% for error in form.nom_p.vars.errors %}
                    <div class="invalid-feedback">
                      {{ error.message }}
                    </div>
                  {% endfor %}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <div class="form-floating">
                  {{ form_widget(form.price, {
                    'attr': {
                      'class': 'form-control' ~ (form.price.vars.errors|length > 0 ? ' is-invalid' : ''),
                      'placeholder': 'Price'
                    }
                  }) }}
                  {{ form_label(form.price, 'Price') }}
                  {% for error in form.price.vars.errors %}
                    <div class="invalid-feedback">
                      {{ error.message }}
                    </div>
                  {% endfor %}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <div class="form-floating">
                  {{ form_widget(form.stock, {
                    'attr': {
                      'class': 'form-control' ~ (form.stock.vars.errors|length > 0 ? ' is-invalid' : ''),
                      'placeholder': 'Stock'
                    }
                  }) }}
                  {{ form_label(form.stock, 'Stock') }}
                  {% for error in form.stock.vars.errors %}
                    <div class="invalid-feedback">
                      {{ error.message }}
                    </div>
                  {% endfor %}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <div class="form-floating">
                  {{ form_widget(form.categorie, {
                    'attr': {
                      'class': 'form-control' ~ (form.categorie.vars.errors|length > 0 ? ' is-invalid' : ''),
                      'placeholder': 'Category'
                    }
                  }) }}
                  {{ form_label(form.categorie, 'Category') }}
                  {% for error in form.categorie.vars.errors %}
                    <div class="invalid-feedback">
                      {{ error.message }}
                    </div>
                  {% endfor %}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <div class="form-floating">
                  {{ form_widget(form.origin, {
                    'attr': {
                      'class': 'form-control' ~ (form.origin.vars.errors|length > 0 ? ' is-invalid' : ''),
                      'placeholder': 'Origin'
                    }
                  }) }}
                  {{ form_label(form.origin, 'Origin') }}
                  {% for error in form.origin.vars.errors %}
                    <div class="invalid-feedback">
                      {{ error.message }}
                    </div>
                  {% endfor %}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <div class="form-check form-switch mt-2">
                  {{ form_widget(form.is_ecological, {
                    'attr': {
                      'class': 'form-check-input' ~ (form.is_ecological.vars.errors|length > 0 ? ' is-invalid' : ''),
                      'role': 'switch'
                    }
                  }) }}
                  {{ form_label(form.is_ecological, 'Ecological Product', {'label_attr': {'class': 'form-check-label'}}) }}
                  {% for error in form.is_ecological.vars.errors %}
                    <div class="invalid-feedback d-block">
                      {{ error.message }}
                    </div>
                  {% endfor %}
                </div>
              </div>

              <div class="col-12 mb-3">
                <label for="{{ form.image.vars.id }}" class="form-label">Product Image</label>
                {{ form_widget(form.image, {
                  'attr': {
                    'class': 'form-control' ~ (form.image.vars.errors|length > 0 ? ' is-invalid' : '')
                  }
                }) }}
                <div class="form-text">Upload a high-quality image of the product</div>
                {% for error in form.image.vars.errors %}
                  <div class="invalid-feedback">
                    {{ error.message }}
                  </div>
                {% endfor %}
              </div>

              <div class="col-12 mb-3">
                <label for="{{ form.description.vars.id }}" class="form-label">Description</label>
                {{ form_widget(form.description, {
                  'attr': {
                    'class': 'form-control' ~ (form.description.vars.errors|length > 0 ? ' is-invalid' : ''),
                    'rows': 4
                  }
                }) }}
                {% for error in form.description.vars.errors %}
                  <div class="invalid-feedback">
                    {{ error.message }}
                  </div>
                {% endfor %}
              </div>
            </div>

            <div class="d-flex justify-content-between mt-4">
              <a href="{{ path('app_product_index') }}" class="btn btn-outline-secondary">
                <i class="ri-close-line me-1"></i> Cancel
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i> Save Product
              </button>
            </div>
          {{ form_end(form) }}
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Guidelines</h5>
        </div>
        <div class="card-body">
          <div class="mb-4">
            <h6 class="fw-bold"><i class="ri-information-line text-primary me-2"></i> Product Information</h6>
            <p class="text-muted small mb-0">Provide accurate and detailed information about the product to help customers make informed decisions.</p>
          </div>

          <div class="mb-4">
            <h6 class="fw-bold"><i class="ri-image-line text-primary me-2"></i> Product Image</h6>
            <p class="text-muted small mb-0">Upload a clear, high-quality image that accurately represents the product.</p>
          </div>

          <div class="mb-4">
            <h6 class="fw-bold"><i class="ri-leaf-line text-primary me-2"></i> Ecological Products</h6>
            <p class="text-muted small mb-0">Mark products as ecological only if they meet environmental sustainability standards.</p>
          </div>

          <div>
            <h6 class="fw-bold"><i class="ri-price-tag-3-line text-primary me-2"></i> Pricing</h6>
            <p class="text-muted small mb-0">Set competitive prices that reflect the product's value and market positioning.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
  }

  .form-floating > .form-control {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
  }

  .form-floating > label {
    padding: 1rem 0.75rem;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
{% endblock %}