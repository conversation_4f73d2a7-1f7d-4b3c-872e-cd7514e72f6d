<?php

namespace App\Form;

use App\Entity\Partners;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;

class PartnersType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('logo', FileType::class, [
                'label' => 'Logo du partenaire *',
                'mapped' => false,
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Veuillez sélectionner un logo',
                    ]),
                    new File([
                        'maxSize' => '1024k',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                        ],
                        'mimeTypesMessage' => 'Veuillez télécharger une image valide (JPG, PNG, GIF)',
                        'maxSizeMessage' => 'Le logo ne doit pas dépasser 1MB',
                    ])
                ],
                'attr' => [
                    'accept' => 'image/*',
                    'class' => 'form-control'
                ],
                'help' => 'Formats acceptés : JPG, PNG, GIF. Taille maximale : 1MB'
            ])
            ->add('name', TextType::class, [
                'label' => 'Nom du partenaire *',
                'attr' => [
                    'placeholder' => 'Entrez le nom du partenaire',
                    'class' => 'form-control'
                ]
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type de partenaire *',
                'choices' => [
                    'Sponsor' => 'Sponsor',
                    'Fournisseur' => 'Fournisseur',
                    'Client' => 'Client',
                    'Autre' => 'Autre'
                ],
                'attr' => [
                    'class' => 'form-control'
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description *',
                'attr' => [
                    'placeholder' => 'Entrez une description détaillée du partenaire',
                    'class' => 'form-control',
                    'rows' => 4
                ]
            ])
            ->add('latitude', NumberType::class, [
                'label' => 'Latitude *',
                'attr' => [
                    'placeholder' => 'Ex: 36.8065',
                    'class' => 'form-control',
                    'step' => 'any',
                    'min' => -90,
                    'max' => 90
                ],
                'help' => 'Valeur entre -90 et 90 degrés'
            ])
            ->add('longitude', NumberType::class, [
                'label' => 'Longitude *',
                'attr' => [
                    'placeholder' => 'Ex: 10.1815',
                    'class' => 'form-control',
                    'step' => 'any',
                    'min' => -180,
                    'max' => 180
                ],
                'help' => 'Valeur entre -180 et 180 degrés'
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Partners::class,
        ]);
    }
}
