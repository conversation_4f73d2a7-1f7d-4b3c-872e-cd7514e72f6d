{% extends 'back/pages/home/<USER>' %}

{% block dash %} {% endblock %}
{% block forum %}active {% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Stats Card Styles */
        .stats-card {
            border-radius: 0.75rem;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 36px;
            height: 36px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Forum Item Styles */
        .forum-item {
            transition: all 0.3s ease;
        }

        .forum-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .forum-title {
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.25rem;
        }

        .forum-content {
            color: #6c757d;
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Forums Management</h1>
                    <p class="text-muted mb-0">Manage and monitor all forum discussions</p>
                </div>
            </div>
        </div>

        <!-- Statistics Summary Cards -->
        <div class="row mb-4">
            <!-- Total Forums Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                                <i class="ri-discuss-line text-primary fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 text-muted">Total Forums</h6>
                                <h3 class="mb-0">{{ forums|length }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Users Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-danger-subtle rounded-3 p-3 me-3">
                                <i class="ri-user-voice-line text-danger fs-4"></i>
                            </div>
                            <div>
                                {% set uniqueUsers = [] %}
                                {% for forum in forums %}
                                    {% if forum.user and forum.user.id not in uniqueUsers %}
                                        {% set uniqueUsers = uniqueUsers|merge([forum.user.id]) %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Active Users</h6>
                                <h3 class="mb-0">{{ uniqueUsers|length }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Forums Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                                <i class="ri-time-line text-success fs-4"></i>
                            </div>
                            <div>
                                {% set recentCount = 0 %}
                                {% set now = "now"|date('U') %}
                                {% for forum in forums %}
                                    {% if forum.createdat and (now - forum.createdat|date('U')) < 604800 %}
                                        {% set recentCount = recentCount + 1 %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Last 7 Days</h6>
                                <h3 class="mb-0">{{ recentCount }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comments Card -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                                <i class="ri-chat-1-line text-success fs-4"></i>
                            </div>
                            <div>
                                {% set totalComments = 0 %}
                                {% for forum in forums %}
                                    {% if forum.comments is defined %}
                                        {% set totalComments = totalComments + forum.comments|length %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Total Comments</h6>
                                <h3 class="mb-0">{{ totalComments }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forums List Card -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Forum List</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" id="forumSearch" class="form-control" placeholder="Search forums...">
                            <span class="input-group-text bg-primary text-white">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle border-0" id="forumsTable">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th>Title</th>
                                <th>Content</th>
                                <th>Created At</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for forum in forums %}
                            <tr class="align-middle forum-item">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2 bg-primary-subtle rounded-circle">
                                            <span class="avatar-text text-primary">
                                                {% if forum.user %}
                                                    {{ forum.user.getFullname()|slice(0,1)|upper }}
                                                {% else %}
                                                    ?
                                                {% endif %}
                                            </span>
                                        </div>
                                        <div>
                                            {% if forum.user %}
                                                {{ forum.user.getFullname() }}
                                            {% else %}
                                                Unknown User
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="forum-title">{{ forum.title }}</div>
                                </td>
                                <td>
                                    <div class="forum-content">{{ forum.content|length > 100 ? forum.content|slice(0, 100) ~ '...' : forum.content }}</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-calendar-line text-muted me-2"></i>
                                        <div>
                                            <div>{{ forum.createdat ? forum.createdat|date('M d, Y') : '' }}</div>
                                            <small class="text-muted">{{ forum.createdat ? forum.createdat|date('H:i') : '' }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_forums_show', {'id': forum.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="View Details">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <form method="post" action="{{ path('app_forums_delete', {'id': forum.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this forum?');">
                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ forum.id) }}">
                                            <button class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete Forum">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-discuss-line empty-state-icon"></i>
                                        <h5>No forums found</h5>
                                        <p class="text-muted">There are no forum discussions yet</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize DataTable with enhanced styling
            $('#forumsTable').DataTable({
                "order": [[ 3, "desc" ]],  // Sort by created date by default
                "pageLength": 10,
                "language": {
                    "lengthMenu": "Show _MENU_ forums per page",
                    "zeroRecords": "No forums found",
                    "info": "Showing page _PAGE_ of _PAGES_",
                    "infoEmpty": "No forums available",
                    "infoFiltered": "(filtered from _MAX_ total forums)"
                },
                "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                       '<"row"<"col-sm-12"tr>>' +
                       '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                "responsive": true,
                "autoWidth": false,
                "drawCallback": function() {
                    // Reinitialize tooltips after table redraw
                    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function(tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                }
            });

            // Custom search functionality
            const searchInput = document.getElementById('forumSearch');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    $('#forumsTable').DataTable().search(this.value).draw();
                });
            }

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('#forumsTable tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });

                // Make the entire row clickable to view forum details
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons
                    if (e.target.closest('.btn') || e.target.closest('form')) {
                        return;
                    }

                    const viewLink = this.querySelector('a[title="View Details"]');
                    if (viewLink) {
                        viewLink.click();
                    }
                });
            });

            // Add animation to stats cards
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
{% endblock %}
