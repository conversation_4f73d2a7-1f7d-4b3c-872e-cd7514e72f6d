{% extends 'back/base.html.twig' %}

{% block title %}Donations List{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Header Styles */
        .page-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            padding: 2rem 0;
            margin-bottom: 2rem;
            color: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Card Styles */
        .content-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        /* Table Styles */
        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #4CAF50;
            color: #2c3e50;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            padding: 1rem;
        }

        .table tbody td {
            padding: 1rem;
            vertical-align: middle;
            color: #34495e;
            border-bottom: 1px solid #eee;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* Button Styles */
        .btn-add {
            background-color: #4CAF50;
            border-color: #4CAF50;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background-color: #45a049;
            border-color: #45a049;
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
            transform: translateY(-1px);
        }

        .btn-action {
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            margin: 0 2px;
        }

        .btn-view {
            background-color: #3498db;
            border-color: #3498db;
            color: white;
        }

        .btn-view:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            color: white;
        }

        .btn-edit {
            background-color: #f1c40f;
            border-color: #f1c40f;
            color: white;
        }

        .btn-edit:hover {
            background-color: #f39c12;
            border-color: #f39c12;
            color: white;
        }

        .btn-delete {
            background-color: #e74c3c;
            border-color: #e74c3c;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c0392b;
            border-color: #c0392b;
            color: white;
        }

        /* Status Badge */
        .badge {
            padding: 0.4rem 0.8rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-pending {
            background-color: #f1c40f;
            color: white;
        }

        .badge-approved {
            background-color: #2ecc71;
            color: white;
        }

        .badge-completed {
            background-color: #3498db;
            color: white;
        }

        .badge-cancelled {
            background-color: #e74c3c;
            color: white;
        }

        /* Empty State */
        .empty-state {
            padding: 3rem;
            text-align: center;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #bdc3c7;
        }

        /* Alert styling */
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
{% endblock %}

{% block body %}
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">Donations Management</h1>
        </div>
    </div>

    <div class="container">
        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endfor %}

        <div class="content-card">
            <div class="card-header d-flex justify-content-between align-items-center p-3">
                <h5 class="m-0">Donations List</h5>
                <div class="d-flex gap-2">
                    <a href="{{ path('app_partners_new') }}" class="btn btn-add">
                        <i class="fas fa-user-plus me-2"></i> New Partner
                    </a>
                    <a href="{{ path('app_back_donation_new') }}" class="btn btn-add">
                        <i class="fas fa-hand-holding-heart me-2"></i> New Donation
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Payment Method</th>
                                <th>Partner</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for donation in donations %}
                            <tr>
                                <td>{{ donation.amount }} TND</td>
                                <td>{{ donation.donationDate ? donation.donationDate|date('Y-m-d') : '' }}</td>
                                <td>{{ donation.type|capitalize }}</td>
                                <td>
                                    <span class="badge badge-{{ donation.status }}">
                                        {{ donation.status|capitalize }}
                                    </span>
                                </td>
                                <td>{{ donation.paymentMethod|replace({'_': ' '})|capitalize }}</td>
                                <td>{{ donation.partnerId.name }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ path('app_back_donation_show', {'id': donation.id}) }}" class="btn btn-action btn-view">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ path('app_back_donation_edit', {'id': donation.id}) }}" class="btn btn-action btn-edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {{ include('back/pages/donation/_delete_form.html.twig') }}
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="8" class="empty-state">
                                    <i class="fas fa-donate"></i>
                                    <p>No donations found</p>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });
    </script>
{% endblock %}
