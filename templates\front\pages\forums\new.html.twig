
{% extends 'front/base.html.twig' %}
{% block content %}

<div class="container-fluid position-relative p-0">
    {% include 'front/includes/navbar.html.twig' %}

    <!-- Header Start -->
    <div class="container-fluid bg-breadcrumb-forums">
        <div class="container text-center py-5" style="max-width: 900px">
            <div class="forum-create-page-icon wow fadeInDown" data-wow-delay="0.1s">
                <i class="fas fa-edit"></i>
            </div>
            <h1 class="text-white display-3 mb-3 wow fadeInDown" data-wow-delay="0.2s">Create New Post</h1>
            <p class="text-white-50 mb-4 wow fadeInUp" data-wow-delay="0.3s">
                Share your thoughts, ideas, and questions with our eco-friendly community
            </p>
            <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.4s">
                <li class="breadcrumb-item">
                    <a class="text-white" href="{{path('app_home')}}">Home</a>
                </li>
                <li class="breadcrumb-item">
                    <a class="text-white" href="{{path('front_forums_index')}}">Forums</a>
                </li>
                <li class="breadcrumb-item active text-primary">Create Post</li>
            </ol>
        </div>
    </div>
    <!-- Header End -->
</div>

{{ include('front/pages/forums/_form.html.twig') }}

<style>
    .forum-create-header-bg {
        background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(../img/eco_home/eco_forum.jpg);
        background-position: center;
        background-size: cover;
        position: relative;
    }

    .forum-create-header-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(0, 208, 132, 0.3) 0%, rgba(0, 184, 115, 0.3) 100%);
        z-index: 1;
    }

    .forum-create-header-bg > .container {
        position: relative;
        z-index: 2;
    }

    .forum-create-page-icon {
        width: 90px;
        height: 90px;
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2.5rem;
        color: var(--forum-primary);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }
</style>
{% endblock %}
