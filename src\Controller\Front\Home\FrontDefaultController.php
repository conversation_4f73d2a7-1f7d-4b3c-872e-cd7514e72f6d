<?php

namespace App\Controller\Front\Home;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/', name: 'app_')]
class FrontDefaultController extends AbstractController
{
    #[Route('', name: 'home')]
    public function index(): Response
    {
        return $this->render('front/pages/home.html.twig');
    }

    #[Route('/about', name: 'about')]
    public function about(): Response
    {
        return $this->render('front/pages/about.html.twig');
    }

    #[Route('/feature', name: 'feature')]
    public function feature(): Response
    {
        return $this->render('front/pages/feature.html.twig');
    }

    #[Route('/service', name: 'service')]
    public function service(): Response
    {
        return $this->render('front/pages/service.html.twig');
    }

    #[Route('/contact', name: 'contact')]
    public function contact(): Response
    {
        return $this->render('front/pages/contact.html.twig');
    }

    #[Route('/faq', name: 'faq')]
    public function faq(): Response
    {
        return $this->render('front/pages/faq.html.twig');
    }

    #[Route('/partnerships', name: 'partnerships')]
    public function partnerships(): Response
    {
        return $this->render('front/pages/managements/partners.html.twig');
    }

    #[Route('/events', name: 'events')]
    public function events(): Response
    {
        return $this->render('front/pages/managements/events.html.twig');
    }

    #[Route('/products', name: 'products')]
    public function products(): Response
    {
        return $this->render('front/pages/managements/products.html.twig');
    }

    #[Route('/forums', name: 'forums')]
    public function forums(): Response
    {
        return $this->render('front/pages/managements/forums.html.twig');
    }

}
