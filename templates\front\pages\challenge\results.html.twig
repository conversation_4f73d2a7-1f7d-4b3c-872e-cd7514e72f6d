{% extends 'front/base.html.twig' %}

{% block title %}Quiz Results - {{ challenge.name }}{% endblock %}

{% block content %}
    <!-- Navbar & Hero Start -->
    <div class="container-fluid position-relative p-0">
        {% include 'front/includes/navbar.html.twig' %}

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb-challenges">
            <div class="container text-center py-5" style="max-width: 900px">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                    Challenge Results - {{ challenge.name }}
                </h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('app_home')}}">Home</a>
                    </li>
                    <li class="breadcrumb-item active text-white-50">Challenges</li>
                    <li class="breadcrumb-item active text-primary">Results</li>
                </ol>
            </div>
        </div>
        <!-- Header End -->
    </div>
    <!-- Navbar & Hero End -->

    <div class="container-xxl py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Results Card -->
                    <div class="wow fadeInUp" data-wow-delay="0.1s">
                        <div class="card border-0 shadow-lg">
                            <div class="card-body p-5">
                                {% set maxScore = progress.progressnb * 10 %}
                                {% set percentage = maxScore > 0 ? (progress.score / maxScore * 100)|round : 0 %}
                                
                                <!-- Score Circle -->
                                <div class="text-center mb-5">
                                    <div class="score-circle mx-auto mb-4 wow fadeInUp" data-wow-delay="0.3s"
                                         style="background: conic-gradient(
                                            {% if percentage >= 70 %}var(--bs-success){% elseif percentage >= 40 %}var(--bs-warning){% else %}var(--bs-danger){% endif %} {{ percentage }}%,
                                            #f0f0f0 0
                                         )">
                                        <div class="inner">
                                            <h2 class="display-4 mb-0">{{ percentage }}%</h2>
                                            <p class="text-muted mb-0">Score</p>
                                        </div>
                                    </div>
                                    
                                    <div class="score-details wow fadeInUp" data-wow-delay="0.5s">
                                        <h3 class="text-primary mb-4">{{ progress.score }} / {{ maxScore }} points</h3>
                                        <p class="lead mb-1">Questions Completed: {{ progress.progressnb }}</p>
                                        <p class="text-muted">Each correct answer is worth 10 points</p>
                                    </div>
                                </div>

                                <!-- Performance Message -->
                                <div class="performance-message text-center p-4 mb-4 wow fadeInUp" data-wow-delay="0.7s"
                                     style="background-color: {% if percentage >= 70 %}rgba(var(--bs-success-rgb), 0.1){% elseif percentage >= 40 %}rgba(var(--bs-warning-rgb), 0.1){% else %}rgba(var(--bs-danger-rgb), 0.1){% endif %}; 
                                            border-radius: 15px;">
                                    {% if percentage >= 70 %}
                                        <div class="mb-3">
                                            <i class="fas fa-trophy fa-3x text-success"></i>
                                        </div>
                                        <h4 class="text-success mb-3">Excellent Achievement! 🎉</h4>
                                        <p class="mb-0">You've demonstrated mastery of this challenge. Keep up the outstanding work!</p>
                                    {% elseif percentage >= 40 %}
                                        <div class="mb-3">
                                            <i class="fas fa-star fa-3x text-warning"></i>
                                        </div>
                                        <h4 class="text-warning mb-3">Good Progress! 👍</h4>
                                        <p class="mb-0">You're on the right track! A bit more practice will help you excel.</p>
                                    {% else %}
                                        <div class="mb-3">
                                            <i class="fas fa-book-reader fa-3x text-danger"></i>
                                        </div>
                                        <h4 class="text-danger mb-3">Keep Learning! 💪</h4>
                                        <p class="mb-0">Every attempt is a step toward mastery. Review and try again!</p>
                                    {% endif %}
                                </div>

                                <!-- Action Buttons -->
                                <div class="text-center wow fadeInUp" data-wow-delay="0.9s">
                                    <a href="{{ path('app_challenges_list') }}" 
                                       class="btn btn-light btn-lg me-3 px-4">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Back to Challenges
                                    </a>
                                    <a href="{{ path('app_challenge_take_quiz', {'id': challenge.id, 'restart': 1}) }}" 
                                       class="btn btn-primary btn-lg px-4" 
                                       onclick="return confirm('Starting over will reset your current progress. Are you sure?')">
                                        Try Again
                                        <i class="fas fa-redo ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .score-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            position: relative;
            transition: all 0.3s ease;
        }

        .score-circle .inner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 160px;
            height: 160px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .performance-message {
            transition: all 0.3s ease;
        }

        .performance-message:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn {
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
{% endblock %}