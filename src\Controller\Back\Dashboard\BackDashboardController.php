<?php

namespace App\Controller\Back\Dashboard;

use App\Entity\Challenge;
use App\Entity\Command;
use App\Entity\Comments;
use App\Entity\Donation;
use App\Entity\Event;
use App\Entity\Forums;
use App\Entity\Partners;
use App\Entity\Product;
use App\Entity\Progress;
use App\Entity\User;
use App\Repository\CommandRepository;
use App\Repository\CommentsRepository;
use App\Repository\DonationRepository;
use App\Repository\EventRepository;
use App\Repository\ForumsRepository;
use App\Repository\PartnersRepository;
use App\Repository\ProductRepository;
use App\Repository\ProgressRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/back', name: 'back_')]
class BackDashboardController extends AbstractController
{
    #[Route('', name: 'admin_dashboard')]
    #[IsGranted('ROLE_ADMIN', message: 'You need admin privileges to access this area.')]
    public function index(
        UserRepository $userRepository,
        EventRepository $eventRepository,
        ForumsRepository $forumsRepository,
        CommentsRepository $commentsRepository,
        EntityManagerInterface $entityManager,
        PartnersRepository $partnersRepository,
        DonationRepository $donationRepository,
        ProductRepository $productRepository,
        CommandRepository $commandRepository,
        ProgressRepository $progressRepository
    ): Response
    {
        // User statistics
        $users = $userRepository->findAll();
        $adminCount = 0;
        $regularCount = 0;
        $partnerCount = 0;

        foreach ($users as $user) {
            if ($user->getRole() === 'admin') {
                $adminCount++;
            } elseif ($user->getRole() === 'partner') {
                $partnerCount++;
            } else {
                $regularCount++;
            }
        }

        // Event statistics
        $events = $eventRepository->findAll();
        $today = new \DateTime();
        $upcomingEvents = array_filter($events, function($event) use ($today) {
            return $event->getDate() > $today;
        });

        // Forum statistics
        $forums = $forumsRepository->findAll();
        $comments = $commentsRepository->findAll();
        $todayForums = array_filter($forums, function($forum) {
            $today = new \DateTime();
            $today->setTime(0, 0, 0);
            return $forum->getCreatedAt() >= $today;
        });

        // Challenge statistics
        $challenges = $entityManager->getRepository(Challenge::class)->findAll();
        $activeCount = 0;
        $inactiveCount = 0;
        $today = new \DateTime();

        foreach ($challenges as $challenge) {
            // Check if challenge is active by comparing start and end dates with current date
            $isActive = false;
            if ($challenge->getStart() && $challenge->getEnd()) {
                $isActive = $challenge->getStart() <= $today && $challenge->getEnd() >= $today;
            }

            if ($isActive) {
                $activeCount++;
            } else {
                $inactiveCount++;
            }
        }

        // Progress statistics
        $progressEntries = $progressRepository->findAll();

        // Partners & Donations
        $partners = $partnersRepository->findAll();

        // Calculate total donation amount
        $totalDonationAmount = 0;
        $donations = $donationRepository->findAll();
        foreach ($donations as $donation) {
            if ($donation->getStatus() !== 'cancelled') {
                $totalDonationAmount += $donation->getAmount();
            }
        }

        // Products & Commands
        $products = $productRepository->findAll();
        $commands = $commandRepository->findBy([], ['createAt' => 'DESC']);
        $recentOrders = array_slice($commands, 0, 5);

        // Calculate total sales amount
        $totalSalesAmount = 0;
        foreach ($commands as $command) {
            if ($command->getStatus() === 'completed') {
                $totalSalesAmount += $command->getTotalAmount();
            }
        }

        return $this->render('back/pages/home/<USER>', [
            // User statistics
            'users' => $users,
            'adminCount' => $adminCount,
            'regularCount' => $regularCount,
            'partnerCount' => $partnerCount,

            // Event statistics
            'events' => $events,
            'upcomingEvents' => $upcomingEvents,

            // Forum statistics
            'forums' => $forums,
            'comments' => $comments,
            'todayForums' => $todayForums,

            // Challenge statistics
            'challenges' => $challenges,
            'activeCount' => $activeCount,
            'inactiveCount' => $inactiveCount,
            'progressEntries' => $progressEntries,

            // Partners & Donations
            'partners' => $partners,
            'donations' => $donations,
            'totalDonationAmount' => $totalDonationAmount,

            // Products & Commands
            'products' => $products,
            'commands' => $commands,
            'recentOrders' => $recentOrders,
            'totalSalesAmount' => $totalSalesAmount,
        ]);
    }

    #[Route('/forums', name: 'forum_dashboard')]
    public function forums(): Response
    {
        return $this->render('back/pages/home/<USER>');
    }
}
