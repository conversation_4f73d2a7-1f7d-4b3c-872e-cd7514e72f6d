<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250215235342 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE comments DROP FOREIGN KEY FK_5F9E962A29CCBAD0');
        $this->addSql('DROP INDEX IDX_5F9E962A29CCBAD0 ON comments');
        $this->addSql('ALTER TABLE comments CHANGE postid_id forum_id INT NOT NULL');
        $this->addSql('ALTER TABLE comments ADD CONSTRAINT FK_5F9E962A29CCBAD0 FOREIGN KEY (forum_id) REFERENCES forums (id)');
        $this->addSql('CREATE INDEX IDX_5F9E962A29CCBAD0 ON comments (forum_id)');
        $this->addSql('ALTER TABLE forums CHANGE created_at createdat DATETIME NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE comments DROP FOREIGN KEY FK_5F9E962A29CCBAD0');
        $this->addSql('DROP INDEX IDX_5F9E962A29CCBAD0 ON comments');
        $this->addSql('ALTER TABLE comments CHANGE forum_id postid_id INT NOT NULL');
        $this->addSql('ALTER TABLE comments ADD CONSTRAINT FK_5F9E962A29CCBAD0 FOREIGN KEY (postid_id) REFERENCES forums (id)');
        $this->addSql('CREATE INDEX IDX_5F9E962A29CCBAD0 ON comments (postid_id)');
        $this->addSql('ALTER TABLE forums CHANGE createdat created_at DATETIME NOT NULL');
    }
}
