/* Dashboard Home Styles */

/* Main Dashboard Container */
.dashboard-container {
    padding: 1.5rem 0;
}

/* Welcome Header */
.welcome-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-header h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    margin-right: 1rem;
    color: #2c3e50;
}

.system-status {
    display: inline-flex;
    align-items: center;
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Section Cards */
.section-card {
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
    border-radius: 0.75rem;
    overflow: hidden;
}

.section-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.section-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.section-card .card-header h5 {
    font-weight: 600;
    color: #2c3e50;
}

.section-card .card-body {
    padding: 1.5rem;
}

/* Stats Overview */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: #fff;
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-card .stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.stat-card .stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #2c3e50;
}

.stat-card .stat-label {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 220px;
    margin: 1rem 0;
}

/* Dashboard Tables */
.dashboard-table {
    font-size: 0.9rem;
}

.dashboard-table th {
    font-weight: 600;
    color: #495057;
}

.dashboard-table td {
    vertical-align: middle;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #e9ecef;
    margin-top: 0.5rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6c757d;
}

.progress-value {
    font-weight: 600;
    color: #495057;
}

/* Date Display */
.date-display {
    display: flex;
    flex-direction: column;
}

.date-display .date {
    font-weight: 500;
    color: #495057;
}

.date-display .time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Animation Delays */
.animate__animated.animate__fadeIn:nth-child(1) { animation-delay: 0.1s; }
.animate__animated.animate__fadeIn:nth-child(2) { animation-delay: 0.2s; }
.animate__animated.animate__fadeIn:nth-child(3) { animation-delay: 0.3s; }
.animate__animated.animate__fadeIn:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Adjustments */
@media (max-width: 992px) {
    .stats-overview {
        grid-template-columns: 1fr;
    }
}

/* Color Schemes */
.bg-primary-light {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.bg-success-light {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.bg-info-light {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

/* Quick Action Buttons */
.quick-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.quick-action-btn {
    flex: 1;
    text-align: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
    color: #495057;
    transition: all 0.2s;
    text-decoration: none;
}

.quick-action-btn:hover {
    background-color: #e9ecef;
    color: #212529;
}

.quick-action-btn i {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Recent Activity Section */
.recent-activity {
    margin-top: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex-grow: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}
