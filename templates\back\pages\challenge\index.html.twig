{% extends 'back/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Card Styles */
        .card {
            border: none;
            margin-bottom: 24px;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1rem 1.5rem;
        }

        /* Stats Card Styles */
        .stats-card {
            border-radius: 0.75rem;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #fff;
            background-color: var(--primary-color);
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-text {
            font-size: 16px;
            font-weight: 600;
        }

        .avatar-sm {
            width: 36px;
            height: 36px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Empty State Styles */
        .empty-state {
            padding: 2rem;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 3rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }

        /* Challenge Item Styles */
        .challenge-item {
            transition: all 0.3s ease;
        }

        .challenge-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .challenge-title {
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.25rem;
        }

        .challenge-description {
            color: #6c757d;
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        /* Challenge Image Thumbnail */
        .challenge-thumbnail {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,.05);
        }

        .challenge-thumbnail-placeholder {
            width: 60px;
            height: 60px;
            background-color: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #adb5bd;
            font-size: 24px;
        }

        /* Status Badge */
        .status-badge {
            padding: 0.35em 0.65em;
            font-size: 0.75em;
            font-weight: 500;
            border-radius: 50rem;
            display: inline-flex;
            align-items: center;
        }

        .status-badge i {
            margin-right: 0.25rem;
        }

        /* Date Display */
        .date-display {
            display: flex;
            flex-direction: column;
        }

        .date-display .date {
            font-weight: 500;
        }

        .date-display .time {
            font-size: 0.75rem;
            color: #6c757d;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="page-header animate__animated animate__fadeIn">
            <div class="row align-items-center mb-4">
                <div class="col">
                    <h1 class="h3 mb-0 text-gray-800">Challenges Management</h1>
                    <p class="text-muted mb-0">Manage and organize all challenges</p>
                </div>
                <div class="col-auto">
                    <a href="{{ path('app_challenge_new') }}" class="btn btn-primary">
                        <i class="ri-add-line me-1"></i> New Challenge
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Summary Cards -->
        <div class="row mb-4">
            <!-- Total Challenges Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                                <i class="ri-trophy-line text-primary fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 text-muted">Total Challenges</h6>
                                <h3 class="mb-0">{{ challenges|length }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Challenges Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-danger-subtle rounded-3 p-3 me-3">
                                <i class="ri-calendar-check-line text-danger fs-4"></i>
                            </div>
                            <div>
                                {% set activeCount = 0 %}
                                {% set now = "now"|date('U') %}
                                {% for challenge in challenges %}
                                    {% if challenge.start and challenge.end %}
                                        {% set startTime = challenge.start|date('U') %}
                                        {% set endTime = challenge.end|date('U') %}
                                        {% if startTime <= now and endTime >= now %}
                                            {% set activeCount = activeCount + 1 %}
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Active Challenges</h6>
                                <h3 class="mb-0">{{ activeCount }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Challenges Card -->
            <div class="col-md-3 mb-4 mb-md-0">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                                <i class="ri-time-line text-success fs-4"></i>
                            </div>
                            <div>
                                {% set upcomingCount = 0 %}
                                {% set now = "now"|date('U') %}
                                {% for challenge in challenges %}
                                    {% if challenge.start %}
                                        {% set startTime = challenge.start|date('U') %}
                                        {% if startTime > now %}
                                            {% set upcomingCount = upcomingCount + 1 %}
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Upcoming</h6>
                                <h3 class="mb-0">{{ upcomingCount }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Challenges Card -->
            <div class="col-md-3">
                <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
                                <i class="ri-flag-line text-warning fs-4"></i>
                            </div>
                            <div>
                                {% set completedCount = 0 %}
                                {% set now = "now"|date('U') %}
                                {% for challenge in challenges %}
                                    {% if challenge.end %}
                                        {% set endTime = challenge.end|date('U') %}
                                        {% if endTime < now %}
                                            {% set completedCount = completedCount + 1 %}
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                                <h6 class="mb-0 text-muted">Completed</h6>
                                <h3 class="mb-0">{{ completedCount }}</h3>                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Challenges List Card -->
        <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Challenges List</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" id="challenge-search" class="form-control" placeholder="Search challenges...">
                            <span class="input-group-text bg-primary text-white">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle border-0" id="challengesTable">
                        <thead class="table-light">
                            <tr>
                                <th>Challenge</th>
                                <th>Duration</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for challenge in challenges %}
                            {% set now = "now"|date('U') %}
                            {% set startTime = challenge.start ? challenge.start|date('U') : 0 %}
                            {% set endTime = challenge.end ? challenge.end|date('U') : 0 %}

                            {% if startTime > now %}
                                {% set status = 'upcoming' %}
                                {% set statusClass = 'bg-info-subtle text-info' %}
                                {% set statusIcon = 'ri-time-line' %}
                                {% set statusText = 'Upcoming' %}
                            {% elseif endTime < now and endTime > 0 %}
                                {% set status = 'completed' %}
                                {% set statusClass = 'bg-success-subtle text-success' %}
                                {% set statusIcon = 'ri-check-line' %}
                                {% set statusText = 'Completed' %}
                            {% elseif startTime <= now and (endTime >= now or endTime == 0) %}
                                {% set status = 'active' %}
                                {% set statusClass = 'bg-warning-subtle text-warning' %}
                                {% set statusIcon = 'ri-play-line' %}
                                {% set statusText = 'Active' %}
                            {% else %}
                                {% set status = 'draft' %}
                                {% set statusClass = 'bg-secondary-subtle text-secondary' %}
                                {% set statusIcon = 'ri-draft-line' %}
                                {% set statusText = 'Draft' %}
                            {% endif %}

                            <tr class="align-middle challenge-item">
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if challenge.image %}
                                            <img src="http://localhost/{{ challenge.image }}" alt="{{ challenge.name }}" class="challenge-thumbnail me-3">
                                        {% else %}
                                            <div class="challenge-thumbnail-placeholder me-3">
                                                <i class="ri-image-line"></i>
                                            </div>
                                        {% endif %}
                                        <div>
                                            <div class="challenge-title">{{ challenge.name }}</div>
                                            <div class="challenge-description">{{ challenge.description }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary-subtle text-primary rounded-pill">
                                        <i class="ri-time-line me-1"></i> {{ challenge.duration }}
                                    </span>
                                </td>
                                <td>
                                    {% if challenge.start %}
                                        <div class="date-display">
                                            <span class="date">{{ challenge.start|date('M d, Y') }}</span>
                                            <span class="time">{{ challenge.start|date('H:i') }}</span>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">Not set</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if challenge.end %}
                                        <div class="date-display">
                                            <span class="date">{{ challenge.end|date('M d, Y') }}</span>
                                            <span class="time">{{ challenge.end|date('H:i') }}</span>
                                        </div>
                                    {% else %}
                                        <span class="text-muted">Not set</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="status-badge {{ statusClass }} rounded-pill">
                                        <i class="{{ statusIcon }}"></i> {{ statusText }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_challenge_show', {'id': challenge.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="View Details">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                        <a href="{{ path('app_challenge_edit', {'id': challenge.id}) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="Edit Challenge">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <a href="{{ path('app_challenge_questions', {'id': challenge.id}) }}"
                                           class="btn btn-sm btn-outline-info"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="Manage Questions">
                                            <i class="ri-questionnaire-line"></i>
                                        </a>
                                        <form method="post" action="{{ path('app_challenge_delete', {'id': challenge.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this challenge? This action cannot be undone.');">
                                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ challenge.id) }}">
                                            <button class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete Challenge">
                                                <i class="ri-delete-bin-line"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-trophy-line empty-state-icon"></i>
                                        <h5>No challenges found</h5>
                                        <p class="text-muted">There are no challenges yet</p>
                                        <a href="{{ path('app_challenge_new') }}" class="btn btn-primary mt-3">
                                            <i class="ri-add-line me-1"></i> Create New Challenge
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Search functionality
            const searchInput = document.getElementById('challenge-search');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    const searchValue = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('#challengesTable tbody tr');

                    tableRows.forEach(function(row) {
                        const titleElement = row.querySelector('.challenge-title');
                        const descriptionElement = row.querySelector('.challenge-description');

                        if (titleElement && descriptionElement) {
                            const titleText = titleElement.textContent.toLowerCase();
                            const descriptionText = descriptionElement.textContent.toLowerCase();
                            const textToSearch = titleText + ' ' + descriptionText;

                            if (textToSearch.includes(searchValue)) {
                                row.style.display = '';
                            } else {
                                row.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('#challengesTable tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });

                // Make the entire row clickable to view challenge details
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons
                    if (e.target.closest('.btn') || e.target.closest('form')) {
                        return;
                    }

                    const viewLink = this.querySelector('a[title="View Details"]');
                    if (viewLink) {
                        viewLink.click();
                    }
                });
            });

            // Add animation to stats cards
            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
{% endblock %}
