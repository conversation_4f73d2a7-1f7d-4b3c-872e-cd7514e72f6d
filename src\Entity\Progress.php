<?php

namespace App\Entity;

use App\Repository\ProgressRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ProgressRepository::class)]
class Progress
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column]
    #[Assert\NotNull(message: 'Score cannot be empty')]
    #[Assert\Range(
        min: 0,
        max: 100,
        notInRangeMessage: 'Score must be between {{ min }} and {{ max }}'
    )]
    private ?int $score = null;

    #[ORM\Column]
    #[Assert\NotNull(message: 'Progress number cannot be empty')]
    #[Assert\Range(
        min: 0,
        notInRangeMessage: 'Progress number must be at least {{ min }}'
    )]
    private ?int $progressnb = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'Please select a user')]
    private ?User $user = null;

    #[ORM\ManyToOne(targetEntity: Challenge::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'Please select a challenge')]
    private ?Challenge $challenge = null;

    #[ORM\Column]
    #[Assert\NotNull(message: 'Last updated date cannot be empty')]
    private ?\DateTimeImmutable $lastUpdated = null;

    public function __construct()
    {
        $this->lastUpdated = new \DateTimeImmutable();
        $this->progressnb = 0;
        $this->score = 0;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getScore(): ?int
    {
        return $this->score;
    }

    public function setScore(int $score): static
    {
        $this->score = $score;
        return $this;
    }

    public function getProgressnb(): ?int
    {
        return $this->progressnb;
    }

    public function setProgressnb(int $progressnb): static
    {
        $this->progressnb = $progressnb;
        $this->lastUpdated = new \DateTimeImmutable();
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function getChallenge(): ?Challenge
    {
        return $this->challenge;
    }

    public function setChallenge(?Challenge $challenge): static
    {
        $this->challenge = $challenge;
        return $this;
    }

    public function getLastUpdated(): ?\DateTimeImmutable
    {
        return $this->lastUpdated;
    }

    public function setLastUpdated(\DateTimeImmutable $lastUpdated): static
    {
        $this->lastUpdated = $lastUpdated;
        return $this;
    }
}
