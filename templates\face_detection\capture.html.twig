{% extends 'front/base.html.twig' %}

{% block title %}Face ID Setup{% endblock %}

{% block stylesheets %}
    <link rel="shortcut icon" href="{{asset('front/img/eco-net.png')}}" type="image/x-icon"/>

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:wght@400;500;700;900&display=swap" rel="stylesheet"/>

    <!-- Icon Font Stylesheet -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet"/>

    <!-- Libraries Stylesheet -->
    <link rel="stylesheet" href="{{asset('front/lib/animate/animate.min.css')}}"/>
    <link href="{{asset('front/lib/lightbox/css/lightbox.min.css')}}" rel="stylesheet"/>
    <link href="{{asset('front/lib/owlcarousel/assets/owl.carousel.min.css')}}" rel="stylesheet"/>

    <!-- Customized Bootstrap Stylesheet -->
    <link href="{{asset('front/css/bootstrap.min.css')}}" rel="stylesheet"/>

    <!-- Template Stylesheet -->
    <link href="{{asset('front/css/style.css')}}" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Modern Profile Stylesheet -->
    <link rel="stylesheet" href="{{ asset('assets/css/profile-modern.css') }}">

    <style>
        .camera-container {
            max-width: 640px;
            margin: 0 auto;
        }
        #video {
            width: 100%;
            border-radius: var(--radius-md);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-md);
            background-color: #000;
        }
        #canvas {
            display: none;
        }
        .preview-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }
        .preview-content {
            background-color: var(--bg-white);
            margin: 5% auto;
            padding: 2rem;
            border-radius: var(--radius-md);
            max-width: 680px;
            position: relative;
            box-shadow: var(--shadow-lg);
        }
        .preview-image {
            width: 100%;
            border-radius: var(--radius-md);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
        }
        .preview-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        .camera-instructions {
            background-color: var(--primary-very-light);
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            border-radius: var(--radius-sm);
            margin-bottom: 1.5rem;
        }
        .camera-instructions h4 {
            color: var(--primary-dark);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        .camera-instructions ul {
            margin-bottom: 0;
            padding-left: 1.5rem;
        }
        .camera-instructions li {
            margin-bottom: 0.5rem;
            color: var(--text-medium);
        }
        .camera-instructions li:last-child {
            margin-bottom: 0;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Navbar & Hero Start -->
    <div class="container-fluid position-relative p-0">
        {% include 'front/includes/navbar.html.twig' %}

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb-profile">
            <div class="container text-center py-5" style="max-width: 900px">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                    Face ID Setup
                </h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('app_home')}}">Home</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('back_profile_edit')}}">Profile</a>
                    </li>
                    <li class="breadcrumb-item active text-primary">Face ID</li>
                </ol>
            </div>
        </div>
        <!-- Header End -->
    </div>
    <!-- Navbar & Hero End -->

    <div class="profile-modern-container">

        <div class="profile-card">
            <div class="profile-card-header">
                <h2>Face ID Setup</h2>
                <p>Configure facial recognition for secure login</p>
            </div>

            <div class="profile-card-body">
                <div class="camera-instructions">
                    <h4><i class="fas fa-info-circle"></i> Instructions for Best Results</h4>
                    <ul>
                        <li>Ensure your face is well-lit and clearly visible</li>
                        <li>Remove glasses, hats, or other accessories that may cover your face</li>
                        <li>Look directly at the camera with a neutral expression</li>
                        <li>Position your face in the center of the frame</li>
                    </ul>
                </div>

                <div class="camera-container">
                    <video id="video" height="480" autoplay></video>
                    <canvas id="canvas" width="640" height="480"></canvas>
                    <button id="capture" class="profile-btn profile-btn-primary" style="width: 100%;">
                        <i class="fas fa-camera"></i> Capture Photo
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <h3 style="color: var(--primary-color); margin-bottom: 1rem; text-align: center;">Review Your Photo</h3>
            <img id="previewImage" class="preview-image" src="" alt="Captured photo">
            <div class="preview-buttons">
                <button id="confirmCapture" class="profile-btn profile-btn-primary">
                    <i class="fas fa-check"></i> Confirm & Save
                </button>
                <button id="tryAgain" class="profile-btn profile-btn-secondary">
                    <i class="fas fa-redo"></i> Try Again
                </button>
            </div>
        </div>
    </div>

    <script>
        // Access the camera
        const video = document.getElementById('video');
        const canvas = document.getElementById('canvas');
        const captureButton = document.getElementById('capture');
        const previewModal = document.getElementById('previewModal');
        const previewImage = document.getElementById('previewImage');
        const confirmButton = document.getElementById('confirmCapture');
        const tryAgainButton = document.getElementById('tryAgain');
        let imageData = null;
        let cameraStream = null;

        // Function to show error message
        function showErrorMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'profile-alert profile-alert-danger';
            alertDiv.style.backgroundColor = '#ffebee';
            alertDiv.style.color = '#c62828';
            alertDiv.style.borderLeft = '4px solid #c62828';
            alertDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
            document.querySelector('.camera-container').prepend(alertDiv);

            // Remove the alert after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Function to show success message
        function showSuccessMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'profile-alert profile-alert-success';
            alertDiv.style.backgroundColor = '#e8f5e9';
            alertDiv.style.color = '#2E7D32';
            alertDiv.style.borderLeft = '4px solid #4CAF50';
            alertDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            document.querySelector('.camera-container').prepend(alertDiv);

            // Remove the alert after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Initialize camera
        async function initCamera() {
            try {
                const constraints = {
                    video: {
                        width: 640,
                        height: 480,
                        facingMode: 'user' // Use front camera on mobile devices
                    }
                };

                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = stream;
                cameraStream = stream;

                // Add a loading indicator while camera initializes
                video.onloadedmetadata = () => {
                    captureButton.disabled = false;
                    captureButton.innerHTML = '<i class="fas fa-camera"></i> Capture Photo';
                };

            } catch (err) {
                console.error("Error accessing the camera: ", err);
                showErrorMessage('Failed to access camera. Please ensure camera permissions are granted and you are using a secure connection (HTTPS).');
                captureButton.disabled = true;
            }
        }

        // Initialize camera on page load
        document.addEventListener('DOMContentLoaded', () => {
            captureButton.disabled = true;
            captureButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading Camera...';
            initCamera();
        });

        // Capture the image
        captureButton.addEventListener('click', () => {
            // Flash effect
            const flashOverlay = document.createElement('div');
            flashOverlay.style.position = 'absolute';
            flashOverlay.style.top = '0';
            flashOverlay.style.left = '0';
            flashOverlay.style.width = '100%';
            flashOverlay.style.height = '100%';
            flashOverlay.style.backgroundColor = 'white';
            flashOverlay.style.opacity = '0.8';
            flashOverlay.style.zIndex = '10';
            flashOverlay.style.pointerEvents = 'none';
            video.parentElement.style.position = 'relative';
            video.parentElement.appendChild(flashOverlay);

            // Remove flash effect after a short time
            setTimeout(() => {
                flashOverlay.remove();
            }, 150);

            // Capture the image
            const context = canvas.getContext('2d');
            context.drawImage(video, 0, 0, 640, 480);

            // Get the image data and show preview
            imageData = canvas.toDataURL('image/png');
            previewImage.src = imageData;
            previewModal.style.display = 'block';
        });

        // Confirm and save the image
        confirmButton.addEventListener('click', () => {
            if (!imageData) return;

            // Show loading state
            confirmButton.disabled = true;
            confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

            // Send the image data to the server
            fetch('/save-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ image: imageData }),
            })
            .then(response => response.json())
            .then(data => {
                console.log('Success:', data);

                // Show success message before redirecting
                showSuccessMessage('Face ID successfully saved! Redirecting to profile...');

                // Stop the camera stream
                if (cameraStream) {
                    cameraStream.getTracks().forEach(track => track.stop());
                }

                // Redirect to profile edit page after a short delay
                setTimeout(() => {
                    window.location.href = "{{ path('back_profile_edit') }}";
                }, 1500);
            })
            .catch((error) => {
                console.error('Error:', error);

                // Reset button state
                confirmButton.disabled = false;
                confirmButton.innerHTML = '<i class="fas fa-check"></i> Confirm & Save';

                // Hide the preview modal
                previewModal.style.display = 'none';

                // Show error message
                showErrorMessage('Failed to save the image. Please try again.');
            });
        });

        // Try again button handler
        tryAgainButton.addEventListener('click', () => {
            previewModal.style.display = 'none';
            imageData = null;
        });

        // Close modal when clicking outside
        previewModal.addEventListener('click', (e) => {
            if (e.target === previewModal) {
                previewModal.style.display = 'none';
                imageData = null;
            }
        });

        // Clean up camera stream when leaving the page
        window.addEventListener('beforeunload', () => {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
{% endblock %}