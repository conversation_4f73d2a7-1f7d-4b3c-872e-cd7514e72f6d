<?php

namespace App\Controller\Back\Profile;

use App\Entity\User;
use App\Form\ProfileType;
use App\Form\UserType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use App\Form\ChangePasswordType;
use Symfony\Component\String\Slugger\SluggerInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use App\Repository\ProgressRepository;

#[Route('/profile', name: 'back_profile_')]
class ProfileController extends AbstractController
{
    #[Route('', name: 'edit')]
    public function edit(
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager,
        SluggerInterface $slugger,
        ProgressRepository $progressRepository
    ): Response {
        // Redirect if already logged in


        // $user = new User();
        $current_user = $this->getUser();
        $form = $this->createForm(ProfileType::class, $current_user);
        $form->handleRequest($request);

        $progressStats = $progressRepository->getUserProgressStatistics($current_user);

        if ($form->isSubmitted() && $form->isValid()) {
            try {

                $imageFile = $form->get('image')->getData();

                if ($imageFile) {
                    // Create a timestamp-based filename with format: image-YYYY-MM-DD-HHmmssSSS-uniqueid.extension
                    $timestamp = new \DateTime();
                    $timestampStr = $timestamp->format('Y-m-d-His');
                    $uniqueId = uniqid();
                    $extension = $imageFile->guessExtension();
                    $newFilename = "image-{$timestampStr}-{$uniqueId}.{$extension}";

                    try {
                        // Move the file to the directory
                        $imageFile->move(
                            $this->getParameter('users_directory'),
                            $newFilename
                        );

                        // Store the path with 'profiles/' prefix in the database
                        $current_user->setImage('profiles/' . $newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'There was an error uploading your image');
                    }
                }

                $entityManager->flush();
                $this->addFlash('success', 'Profile updated successfully!');
                return $this->redirectToRoute('back_profile_edit');
            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while creating your account. Please try again.');
            }
        } elseif ($form->isSubmitted()) {
            foreach ($form->getErrors(true) as $error) {
                $this->addFlash('error', $error->getMessage());
            }
        }

        return $this->render('back/pages/profile/edit.html.twig', [
            'form' => $form->createView(),
            'user' => $current_user,
            'progressStats' => $progressStats
        ]);
    }
    #[Route('/change-password', name: 'app_change_password')]
    public function changePassword(
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager
    ): Response {
        $user = $this->getUser();

        // Add null check for user
        if (!$user) {
            $this->addFlash('error', 'You must be logged in to change your password');
            return $this->redirectToRoute('back_auth_login');
        }

        $form = $this->createForm(ChangePasswordType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Get form data
            $currentPassword = $form->get('currentPassword')->getData();
            $newPassword = $form->get('password')->getData();

            // Verify current password
            if (!$passwordHasher->isPasswordValid($user, $currentPassword)) {
                $this->addFlash('error', 'Current password is incorrect');
                return $this->redirectToRoute('back_profile_app_change_password');
            }

            // Hash new password
            $hashedPassword = $passwordHasher->hashPassword(
                $user,
                $newPassword
            );
            $user->setPassword($hashedPassword);

            $entityManager->persist($user);
            $entityManager->flush();

            $this->addFlash('success', 'Password has been changed successfully');
            return $this->redirectToRoute('back_profile_edit');
        }

        return $this->render('back/pages/profile/change_password.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
