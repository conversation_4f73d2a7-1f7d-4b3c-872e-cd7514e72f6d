<?php

namespace App\Controller;

use App\Entity\Forums;
use App\Form\ForumsType;
use App\Repository\ForumsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/back/pages/forums')]
final class ForumsController extends AbstractController
{
    #[Route(name: 'app_forums_index', methods: ['GET'])]
    public function index(ForumsRepository $forumsRepository): Response
    {
        return $this->render('back/pages/forums/index.html.twig', [
            'forums' => $forumsRepository->findAll(),
        ]);
    }


    #[Route('/{id}', name: 'app_forums_show', methods: ['GET'])]
    public function show(Forums $forum): Response
    {
        return $this->render('back/pages/forums/show.html.twig', [
            'forum' => $forum,
        ]);
    }


    #[Route('/{id}', name: 'app_forums_delete', methods: ['POST'])]
    public function delete(Request $request, Forums $forum, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$forum->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($forum);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_forums_index', [], Response::HTTP_SEE_OTHER);
    }
}
