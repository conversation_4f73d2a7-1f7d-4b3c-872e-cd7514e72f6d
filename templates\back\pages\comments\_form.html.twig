{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'comment-form'}}) }}
    <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="border-radius: 0.75rem;">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0 fw-bold d-flex align-items-center">
                <i class="ri-chat-1-line text-primary me-2"></i> {{ form.vars.data.id ? 'Edit Comment' : 'New Comment' }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="form-group">
                        {{ form_label(form.content, 'Comment Content', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                        {{ form_widget(form.content, {
                            'attr': {
                                'class': 'form-control form-field rounded-3',
                                'rows': 5,
                                'placeholder': 'Write your comment here...',
                                'minlength': '2',
                                'required': 'required',
                                'style': 'border-color: #dee2e6; resize: vertical;'
                            }
                        }) }}
                        <div class="invalid-feedback">
                            Please enter a valid comment (minimum 2 characters)
                        </div>
                        <div class="text-danger">
                            {{ form_errors(form.content) }}
                        </div>
                        <small class="form-text text-muted mt-2">
                            <i class="ri-information-line me-1"></i> Share your thoughts or feedback
                        </small>
                    </div>
                </div>
            </div>

            {% if form.upvotes is defined %}
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="form-group">
                        {{ form_label(form.upvotes, 'Upvotes', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                        <div class="input-group">
                            <span class="input-group-text bg-success-subtle text-success">
                                <i class="ri-thumb-up-line"></i>
                            </span>
                            {{ form_widget(form.upvotes, {
                                'attr': {
                                    'class': 'form-control form-field rounded-end',
                                    'min': 0,
                                    'type': 'number',
                                    'required': 'required',
                                    'style': 'border-color: #dee2e6;'
                                }
                            }) }}
                        </div>
                        <div class="invalid-feedback">
                            Please enter a valid number of upvotes (minimum 0)
                        </div>
                        <div class="text-danger">
                            {{ form_errors(form.upvotes) }}
                        </div>
                        <small class="form-text text-muted mt-2">
                            <i class="ri-information-line me-1"></i> Number of upvotes for this comment
                        </small>
                    </div>
                </div>
            </div>
            {% endif %}

            {# Hidden Forum ID field #}
            {{ form_widget(form.postid, {
                'attr': {
                    'class': 'form-field',
                    'required': 'required',
                    'hidden': 'hidden'
                }
            }) }}
        </div>
        <div class="card-footer bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ path('app_comments_index') }}" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i> Back to List
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="ri-save-line me-1"></i> {{ button_label|default('Save Comment') }}
                </button>
            </div>
        </div>
    </div>
{{ form_end(form) }}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formFields = document.querySelectorAll('.form-field');

        formFields.forEach(field => {
            // Validate on input (while typing)
            field.addEventListener('input', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });

        // Form submission validation
        document.getElementById('comment-form').addEventListener('submit', function(event) {
            let isValid = true;
            formFields.forEach(field => {
                if (!field.checkValidity()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                }
            });

            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();
            }
        });
    });
</script>

{# {% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formFields = document.querySelectorAll('.form-field');

    formFields.forEach(field => {
        // Validate on input (while typing)
        field.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Form submission validation
    document.getElementById('comment-form').addEventListener('submit', function(event) {
        let isValid = true;
        formFields.forEach(field => {
            if (!field.checkValidity()) {
                isValid = false;
                field.classList.add('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});
</script>
{% endblock %} #}
