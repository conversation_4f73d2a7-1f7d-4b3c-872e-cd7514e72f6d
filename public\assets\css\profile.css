/* Profile Container Styles */
.profile-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* Back Link */
.back-link {
    display: inline-flex;
    align-items: center;
    color: #4CAF50;
    text-decoration: none;
    margin-bottom: 1.5rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.back-link:hover {
    color: #388E3C;
}

.back-link i {
    margin-right: 0.5rem;
}

/* Card Styles */
.eco-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
}

.eco-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.eco-header h2 {
    color: #2E7D32;
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.eco-header p {
    color: #666;
    margin: 0;
}

.eco-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    outline: none;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M8 11.5l-6-6h12l-6 6z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 12px;
}

/* Button Styles */
.eco-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
}

.eco-btn i {
    margin-right: 0.5rem;
}

.eco-btn-primary {
    background: #4CAF50;
    color: white;
}

.eco-btn-primary:hover {
    background: #388E3C;
}

.eco-btn-secondary {
    background: #f8f9fa;
    color: #2E7D32;
    border: 1px solid #4CAF50;
}

.eco-btn-secondary:hover {
    background: #e8f5e9;
}

/* Alert Styles */
.eco-alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.eco-alert i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.eco-alert-success {
    background: #e8f5e9;
    color: #2E7D32;
    border: 1px solid #c8e6c9;
}

.eco-alert-danger {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

/* Form Validation */
.form-error {
    color: #c62828;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-container {
        margin: 1rem auto;
    }
    
    .eco-header {
        padding: 1rem;
    }
    
    .eco-body {
        padding: 1rem;
    }
    
    .eco-btn {
        width: 100%;
        justify-content: center;
    }
} 