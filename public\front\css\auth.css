.form-floating.is-invalid .form-control {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-floating.is-invalid .form-label {
    color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.gender-group.is-invalid .form-check-input {
    border-color: #dc3545;
}

.gender-group.is-invalid .form-label {
    color: #dc3545;
}

.form-check.is-invalid .form-check-input {
    border-color: #dc3545;
}

.form-check.is-invalid .form-check-label {
    color: #dc3545;
}

/* Date Select Styling */
.date-select-group {
    margin-bottom: 1rem;
}

.date-select-group .form-label {
    display: block;
    margin-bottom: 0.5rem;
}

.date-select-group.is-invalid .form-label {
    color: #dc3545;
}

.date-select-group.is-invalid .form-select {
    border-color: #dc3545;
}

.date-select-group .form-select {
    display: inline-block;
    width: auto;
}

.date-select-group .form-select:not(:last-child) {
    margin-right: 0.5rem;
}

/* Month field should be wider to accommodate month names */
.date-select-group select[id$="_month"] {
    min-width: 120px;
}

/* Year field should be wide enough for 4 digits */
.date-select-group select[id$="_year"] {
    min-width: 100px;
}

/* Day field can be narrower */
.date-select-group select[id$="_day"] {
    min-width: 80px;
}
