<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Nullable;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
#[UniqueEntity(fields: ['email'], message: 'This email is already registered')]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[Assert\Nullable]
    #[Assert\NotBlank(groups: ['registration', 'profile', 'user_edit', 'user_login'], message: "Full name cannot be empty.")]
    #[Assert\Length(
        min: 2,
        max: 255,
        minMessage: 'Your name must be at least {{ limit }} characters long',
        maxMessage: 'Your name cannot be longer than {{ limit }} characters',
        groups: ['registration', 'profile', 'user_edit', 'user_login']
    )]
    #[Assert\Regex(
        pattern: '/^[a-zA-Z\s]+$/',
        message: 'Your name can only contain letters and spaces',
        groups: ['registration', 'profile', 'user_edit', 'user_login']
    )]
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $full_name = null;



    #[Assert\NotBlank(
        message: 'Email cannot be empty',
        groups: ['registration', 'profile', 'user_edit', 'user_login']
    )]
    #[Assert\Email(
        message: 'The email {{ value }} is not a valid email address',
        mode: Assert\Email::VALIDATION_MODE_STRICT,
        groups: ['registration', 'profile', 'user_edit', 'user_login']
    )]
    #[Assert\Length(
        max: 255,
        maxMessage: 'Your email cannot be longer than {{ limit }} characters',
        groups: ['registration', 'profile', 'user_edit', 'user_login']
    )]
    #[ORM\Column(length: 255)]
    private ?string $email = null;

    #[Assert\NotBlank(groups: ['registration', 'user_login', 'password_change'])]
    #[Assert\Length(
        min: 8,
        max: 50,
        minMessage: 'Your password must be at least {{ limit }} characters long',
        maxMessage: 'Your password cannot be longer than {{ limit }} characters',
        groups: ['registration', 'password_change']
    )]
    #[Assert\Regex(
        pattern: '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/',
        message: 'Your password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        groups: ['registration', 'password_change']
    )]
    #[ORM\Column(length: 255)]
    private ?string $password = null;

    #[ORM\Column(length: 255)]
    private ?string $role = null;

    #[ORM\Column(length: 255)]
    private ?string $auth_method = null;

    #[ORM\Column]
    private ?bool $verified = null;

    #[ORM\Column]
    private ?bool $banned = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    private bool $terms = false;

    #[ORM\Column(length: 10)]
    #[Assert\Choice(choices: ['male', 'female'], message: 'Please select a valid gender')]
    private ?string $gender = null;

    #[ORM\Column(length: 255)]
    private ?string $image = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $face_id = null;

    /**
     * Flag indicating whether the user has completed their initial profile setup
     */
    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $is_account_setup = false;

    #[ORM\Column(type: 'string', length: 64, nullable: true)]
    private ?string $resetToken = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $resetTokenCreatedAt = null;

    /**
     * @var Collection<int, Forums>
     */
    #[ORM\OneToMany(targetEntity: Forums::class, mappedBy: 'user')]
    private Collection $forums;

    /**
     * @var Collection<int, Comments>
     */
    #[ORM\OneToMany(targetEntity: Comments::class, mappedBy: 'user')]
    private Collection $comments;

    public function __construct()
    {
        $this->forums = new ArrayCollection();
        $this->comments = new ArrayCollection();
        $this->is_account_setup = false;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFullName(): ?string
    {
        return $this->full_name;
    }

    public function setFullName(string $full_name): static
    {
        $this->full_name = $full_name;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;

        return $this;
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function setRole(string $role): static
    {
        $this->role = $role;

        return $this;
    }

    public function getAuthMethod(): ?string
    {
        return $this->auth_method;
    }

    public function setAuthMethod(string $auth_method): static
    {
        $this->auth_method = $auth_method;

        return $this;
    }

    public function isVerified(): ?bool
    {
        return $this->verified;
    }

    public function setVerified(bool $verified): static
    {
        $this->verified = $verified;

        return $this;
    }

    public function isBanned(): ?bool
    {
        return $this->banned;
    }

    public function setBanned(?bool $banned): self
    {
        $this->banned = $banned;
        return $this;
    }

    public function isAccountSetup(): bool
    {
        return $this->is_account_setup;
    }

    public function setAccountSetup(bool $is_account_setup): static
    {
        $this->is_account_setup = $is_account_setup;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getTerms(): bool
    {
        return $this->terms;
    }

    public function setTerms(bool $terms): self
    {
        $this->terms = $terms;
        return $this;
    }

    public function getRoles(): array
    {
        $roles = ['ROLE_USER'];
        
        if (strtolower($this->role) === 'admin') {
            $roles[] = 'ROLE_ADMIN';
        }
        
        return array_unique($roles);
    }

    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
    }

    public function getUserIdentifier(): string
    {
        return $this->email;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(string $gender): static
    {
        $this->gender = $gender;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): static
    {
        $this->image = $image;

        return $this;
    }

    // Getters and Setters
    public function getResetToken(): ?string
    {
        return $this->resetToken;
    }

    public function setResetToken(?string $resetToken): self
    {
        $this->resetToken = $resetToken;
        return $this;
    }

    public function getResetTokenCreatedAt(): ?\DateTimeInterface
    {
        return $this->resetTokenCreatedAt;
    }

    public function setResetTokenCreatedAt(?\DateTimeInterface $resetTokenCreatedAt): self
    {
        $this->resetTokenCreatedAt = $resetTokenCreatedAt;
        return $this;
    }

    /**
     * @return Collection<int, Forums>
     */
    public function getForums(): Collection
    {
        return $this->forums;
    }

    public function addForum(Forums $forum): static
    {
        if (!$this->forums->contains($forum)) {
            $this->forums->add($forum);
            $forum->setUser($this);
        }

        return $this;
    }

    public function removeForum(Forums $forum): static
    {
        if ($this->forums->removeElement($forum)) {
            // set the owning side to null (unless already changed)
            if ($forum->getUser() === $this) {
                $forum->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Comments>
     */
    public function getComments(): Collection
    {
        return $this->comments;
    }

    public function addComment(Comments $comment): static
    {
        if (!$this->comments->contains($comment)) {
            $this->comments->add($comment);
            $comment->setUser($this);
        }

        return $this;
    }

    public function removeComment(Comments $comment): static
    {
        if ($this->comments->removeElement($comment)) {
            // set the owning side to null (unless already changed)
            if ($comment->getUser() === $this) {
                $comment->setUser(null);
            }
        }

        return $this;
    }

    public function getFaceId(): ?string
    {
        return $this->face_id;
    }

    public function setFaceId(?string $face_id): static
    {
        $this->face_id = $face_id;
        return $this;
    }
}
