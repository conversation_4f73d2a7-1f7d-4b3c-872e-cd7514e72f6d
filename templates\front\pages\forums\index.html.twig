{% extends 'front/base.html.twig' %}

{% block content %}
<div class="container-fluid position-relative p-0">

    {% include 'front/includes/navbar.html.twig' %}

    <!-- Header Start -->
    <div class="container-fluid bg-breadcrumb-forums">
        <div class="container text-center py-5" style="max-width: 900px">
            <h1 class="text-white display-3 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                <i class="fas fa-comments me-2"></i>Community Forums
            </h1>
            <p class="text-white-50 mb-4 wow fadeInUp" data-wow-delay="0.2s">
                Join the conversation, share your ideas, and connect with our eco-friendly community
            </p>
            <div class="d-flex justify-content-center align-items-center gap-4 mb-4 wow fadeInUp" data-wow-delay="0.3s">
                <a type="button" class="btn btn-primary rounded-pill px-4 py-3 btn-create-post" href="{{path('front_forums_new')}}">
                    <i class="fas fa-plus me-2"></i> Create New Post
                </a>
            </div>
            <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                <li class="breadcrumb-item">
                    <a class="text-white" href="{{path('app_home')}}">Home</a>
                </li>
                <li class="breadcrumb-item active text-white-50">Pages</li>
                <li class="breadcrumb-item active text-primary">Forums</li>
            </ol>
        </div>
    </div>
    <!-- Header End -->
</div>

<div class="container py-5">
    <div class="row">
        <!-- Sidebar Column (Now on the left) -->
        <div class="col-lg-4">
            <!-- Top Contributors Card -->
            <div class="card mb-4 top-contributors-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i> Top Contributors</h5>
                </div>
                <div class="card-body p-0">
                    {% if topContributors|length > 0 %}
                        <div class="contributors-list">
                            {% for contributor in topContributors %}
                                <div class="contributor-item" data-rank="{{ loop.index }}">
                                    <div class="contributor-rank-wrapper">
                                        <div class="contributor-rank">
                                            {% if loop.index == 1 %}
                                                <i class="fas fa-crown"></i>
                                            {% else %}
                                                {{ loop.index }}
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="contributor-avatar">
                                        {% if contributor.image %}
                                            <img src="http://localhost/img/{{ contributor.image }}" alt="{{ contributor.full_name }}" class="rounded-circle">
                                        {% else %}
                                            <i class="fas fa-user-circle"></i>
                                        {% endif %}
                                    </div>
                                    <div class="contributor-info">
                                        <h6 class="contributor-name">{{ contributor.full_name }}</h6>
                                        <div class="contributor-stats">
                                            <span class="post-count">{{ contributor.postCount }}</span>
                                            <span class="post-label">{{ contributor.postCount == 1 ? 'post' : 'posts' }}</span>
                                        </div>
                                        <div class="contribution-bar">
                                            <div class="contribution-progress" style="width: {{ (contributor.postCount / topContributors[0].postCount * 100)|round }}%"></div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-contributors">
                            <div class="empty-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <p>No contributors yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Popular Tags Card -->
            <div class="card mb-4 tags-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-hashtag me-2"></i> Popular Tags</h5>
                </div>
                <div class="card-body">
                    {% if allTags|length > 0 %}
                        <div class="tags-cloud">
                            <a href="{{ path('front_forums_index') }}"
                               class="tag-item all-tags {{ not currentTag ? 'active' : '' }}"
                               data-tag="">
                                <i class="fas fa-tags me-1"></i> All Tags
                            </a>
                            {% for tag in allTags %}
                                {% set tagSize = 1 + (loop.index % 3) * 0.15 %}
                                <a href="{{ path('front_forums_index', {'tag': tag}) }}"
                                   class="tag-item {{ currentTag == tag ? 'active' : '' }}"
                                   data-tag="{{ tag }}"
                                   style="--tag-size: {{ tagSize }}">
                                    #{{ tag }}
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-tags">
                            <div class="empty-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <p>No tags yet</p>
                        </div>
                    {% endif %}

                    <div class="tag-search mt-3">
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" id="tagSearchInput" class="form-control" placeholder="Search tags...">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tag Filter Bar -->
            <div class="tag-filter-bar mb-4">
                <div class="filter-header d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0"><i class="fas fa-filter me-2"></i> Filter by Tag</h6>
                    {% if currentTag %}
                        <a href="{{ path('front_forums_index') }}" class="clear-filter">
                            <i class="fas fa-times me-1"></i> Clear Filter
                        </a>
                    {% endif %}
                </div>
                <div class="filter-body p-3 bg-light rounded">
                    <div class="tag-filter-chips d-flex flex-wrap gap-2" id="tagFilterChips">
                        <a href="{{ path('front_forums_index') }}"
                           class="tag-chip {{ not currentTag ? 'active' : '' }}"
                           data-tag="">
                            <i class="fas fa-tags me-1"></i> All
                        </a>
                        {% for tag in allTags %}
                            <a href="{{ path('front_forums_index', {'tag': tag}) }}"
                               class="tag-chip {{ currentTag == tag ? 'active' : '' }}"
                               data-tag="{{ tag }}">
                                #{{ tag }}
                            </a>
                        {% endfor %}
                    </div>
                </div>
                {% if currentTag %}
                    <div class="active-filter-indicator mt-2">
                        <span class="badge bg-primary-soft rounded-pill px-3 py-2">
                            <i class="fas fa-filter me-1"></i> Filtered by: <strong>#{{ currentTag }}</strong>
                        </span>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Main Content Column (Now on the right) -->
        <div class="col-lg-8">
            <div class="forum-header d-flex justify-content-between align-items-center mb-4">
                <div class="forum-stats">
                    <span class="badge bg-primary-soft rounded-pill px-3 py-2">
                        <i class="fas fa-comments me-1"></i> {{ forums.getTotalItemCount }} Discussions
                    </span>
                </div>
                <div class="forum-actions d-flex gap-2">
                    <!-- View Toggle Buttons -->
                    <div class="btn-group view-toggle me-2 {{ viewMode == 'list' ? 'list-active' : '' }}" id="viewToggle" role="group" aria-label="View Mode">
                        <button type="button" class="btn {{ viewMode == 'grid' ? 'active' : '' }}" id="gridViewBtn" data-view="grid">
                            <i class="fas fa-th-large me-1"></i> Grid
                        </button>
                        <button type="button" class="btn {{ viewMode == 'list' ? 'active' : '' }}" id="listViewBtn" data-view="list">
                            <i class="fas fa-list me-1"></i> List
                        </button>
                    </div>

                    <!-- My Posts Filter -->
                    <a href="{{ path('front_forums_index', {'my_posts': not myPostsOnly}) }}"
                       class="btn btn-sm my-posts-btn {{ myPostsOnly ? 'active' : '' }} rounded-pill">
                        <i class="fas {{ myPostsOnly ? 'fa-check-circle' : 'fa-user' }} me-1"></i>
                        <span>{{ myPostsOnly ? 'All Posts' : 'My Posts Only' }}</span>
                    </a>

                    <!-- New Post Button -->
                    <a href="{{path('front_forums_new')}}" class="btn btn-sm new-post-btn rounded-pill">
                        <i class="fas fa-plus me-1"></i> New Post
                    </a>
                </div>
            </div>

    <!-- Create Forum Modal -->
    <div class="modal fade" id="createForumModal" tabindex="-1" aria-labelledby="createForumModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createForumModalLabel">Create New Post</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="{{ path('front_forums_new') }}" class="create-forum-form">
                        <div class="mb-3">
                            <label for="newForumTitle" class="form-label">Title (only letters and underscores)</label>
                            <input type="text" class="form-control" id="newForumTitle" name="forums[title]"
                                   required pattern="[A-Za-z_]+"
                                   title="Only letters and underscores are allowed">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="mb-3">
                            <label for="newForumContent" class="form-label">Content</label>
                            <textarea class="form-control" id="newForumContent" name="forums[content]" rows="4" required></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Create Post</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

            {% for forum in forums %}
                <div class="forum-item card mb-4 forum-card" data-forum-id="{{ forum.id }}">
                    <div class="card-body">
                        <!-- Post Header with Three Dot Menu -->
                        <div class="d-flex justify-content-between align-items-start mb-3 position-relative">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    {% if forum.user and forum.user.image %}
                                        <img src="http://localhost/img/{{ forum.user.image }}" alt="{{ forum.user.getFullName() }}" class="rounded-circle">
                                    {% else %}
                                        <i class="fas fa-user-circle fa-2x text-primary"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <h5 class="card-title mb-1">
                                        {{ forum.title }}
                                        {% if forum.sentiment %}
                                            <span class="sentiment-emoji" title="Post sentiment">{{ forum.sentiment|raw }}</span>
                                        {% endif %}
                                    </h5>
                                    <div class="d-flex align-items-center text-muted small">
                                        {% if forum.user %}
                                            <span class="me-2 fw-medium">{{ forum.user.getFullName() }}</span>
                                        {% else %}
                                            <span class="me-2 fw-medium">Unknown User</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown forum-dropdown">
                            {% if is_granted('ROLE_USER') and app.user == forum.user %}
                                <button class="btn btn-link text-muted p-0 forum-menu" type="button">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a href="{{ path('front_forums_edit', {'id': forum.id}) }}"
                                            class="dropdown-item edit-forum"
                                        >
                                            <i class="fas fa-edit me-2"></i>Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ path('front_forums_delete', {'id': forum.id}) }}"
                                           class="dropdown-item text-danger delete-forum"
                                           data-forum-id="{{ forum.id }}"
                                           data-token="{{ csrf_token('delete' ~ forum.id) }}">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </a>
                                    </li>
                                </ul>
                            {% endif %}
                            </div>
                        </div>

                        <!-- Post Content -->
                        <div class="forum-content">
                            <p class="card-text">{{ forum.content }}</p>
                            <div class="forum-post-date text-muted mt-2">
                                <i class="far fa-calendar-alt me-1"></i> Posted {{ forum.createdAt|date('F d, Y') }} at {{ forum.createdAt|date('h:i A') }}
                                <span class="comment-count ms-2">
                                    <i class="far fa-comment text-primary me-1"></i>{{ forum.comments|length }}
                                </span>
                            </div>
                        </div>

                        <!-- Post Meta -->
                        <div class="forum-actions mt-3 pt-3">
                            <div class="d-flex align-items-center">
                                <!-- Show Comments button - only visible in Grid View -->
                                <button class="btn btn-sm btn-outline-primary rounded-pill toggle-comments px-3" data-forum-id="{{ forum.id }}">
                                    <i class="far fa-comments me-1"></i> Show Comments
                                </button>
                            </div>
                        </div>

                        <!-- Comments Section -->
                        <div class="comments-section" id="comments-section-{{ forum.id }}" style="display: none;">
                            <!-- Add Comment Form -->
                            <form class="comment-form mb-4 mt-3" data-forum-id="{{ forum.id }}">
                                <input type="hidden" name="forumId" value="{{ forum.id }}">
                                <div class="d-flex">
                                    <div class="user-avatar me-2">
                                        {% if app.user and app.user.image %}
                                            <img src="http://localhost/img/{{ app.user.image }}" alt="{{ app.user.getFullName() }}" class="rounded-circle">
                                        {% else %}
                                            <i class="fas fa-user-circle fa-2x text-primary"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="input-group">
                                            <input type="text" class="form-control comment-input" placeholder="Write a comment..." name="content">
                                            <button type="submit" class="btn btn-primary comment-submit">
                                                <i class="far fa-paper-plane"></i>
                                            </button>
                                        </div>
                                        <div class="text-danger comment-error mt-2" style="display: none;"></div>
                                    </div>
                                </div>
                            </form>

                            <!-- Comments List -->
                            <div class="comments-list" id="comments-list-{{ forum.id }}">
                                {% if forum.comments|length > 0 %}
                                    {% for comment in forum.comments|sort((a, b) => b.createdAt <=> a.createdAt) %}
                                        <div class="comment-item mb-3" id="comment-{{ comment.id }}">
                                            <div class="d-flex">
                                                <!-- User Avatar -->
                                                <div class="user-avatar me-2">
                                                    {% if comment.user and comment.user.image %}
                                                        <img src="http://localhost/img/{{ comment.user.image }}" alt="{{ comment.user.getFullName() }}" class="rounded-circle">
                                                    {% else %}
                                                        <i class="fas fa-user-circle fa-2x text-primary"></i>
                                                    {% endif %}
                                                </div>

                                                <div class="flex-grow-1">
                                                    <div class="comment-bubble">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <!-- User Name -->
                                                                {% if comment.user %}
                                                                    <p class="mb-1 fw-bold small comment-author">{{ comment.user.getFullName() }}</p>
                                                                {% else %}
                                                                    <p class="mb-1 fw-bold small comment-author">Unknown User</p>
                                                                {% endif %}
                                                                <!-- Comment Content -->
                                                                <p class="mb-0 comment-content">{{ comment.content }}</p>
                                                            </div>
                                                            <div class="dropdown">
                                                            {% if is_granted('ROLE_USER') and app.user == comment.user %}
                                                                <button class="btn btn-link text-muted p-0 comment-menu" type="button">
                                                                    <i class="fas fa-ellipsis-v"></i>
                                                                </button>
                                                                <div class="dropdown-menu">
                                                                    <button class="dropdown-item edit-comment" data-comment-id="{{ comment.id }}">
                                                                        <i class="fas fa-edit me-2"></i>Edit
                                                                    </button>
                                                                    <button class="dropdown-item text-danger delete-comment" data-comment-id="{{ comment.id }}">
                                                                        <i class="fas fa-trash me-2"></i>Delete
                                                                    </button>
                                                                </div>
                                                            {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center mt-1">
                                                        <small class="text-muted me-3">{{ comment.createdAt|date('M d, Y H:i') }}</small>
                                                        <button class="btn btn-link p-0 like-button{% if comment.upvotes > 0 %} liked{% endif %}" data-comment-id="{{ comment.id }}">
                                                            <i class="fas fa-heart"></i>
                                                            <span class="like-count ms-1">{{ comment.upvotes }}</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="empty-comments text-center py-4">
                                        <i class="far fa-comment-dots text-muted mb-2" style="font-size: 2rem;"></i>
                                        <p class="text-muted mb-0">No comments yet. Be the first to comment!</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="empty-forums text-center py-5">
                    <div class="empty-forums-icon mb-3">
                        <i class="fas fa-comments fa-4x text-muted"></i>
                    </div>
                    <h4>No Discussions Yet</h4>
                    <p class="text-muted mb-4">Be the first to start a conversation in our community!</p>
                    <a href="{{path('front_forums_new')}}" class="btn btn-primary rounded-pill px-4 py-2">
                        <i class="fas fa-plus me-2"></i> Create New Post
                    </a>
                </div>
            {% endfor %}

            {# Pagination #}
            {% if forums|length > 0 %}
                <div class="forum-pagination mt-4">
                    {{ knp_pagination_render(forums, '@KnpPaginator/Pagination/bootstrap_v5_pagination.html.twig') }}
                </div>
            {% endif %}
        </div>
    </div>
</div>

{# Edit Modals #}
{% for forum in forums %}
    <div class="modal fade" id="editModal{{ forum.id }}" tabindex="-1" aria-labelledby="editModalLabel{{ forum.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel{{ forum.id }}">Edit Forum</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="{{ path('front_forums_edit', {'id': forum.id}) }}" class="edit-forum-form">
                    <div class="modal-body">
                        <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ forum.id) }}">

                        <div class="mb-3">
                            <label for="forums_title_{{ forum.id }}" class="form-label">Title (only letters and underscores)</label>
                            <input type="text" id="forums_title_{{ forum.id }}" name="forums[title]" class="form-control"
                                   value="{{ forum.title }}" required pattern="[A-Za-z_]+"
                                   minlength="3" maxlength="255"
                                   title="Only letters and underscores are allowed">
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="forums_content_{{ forum.id }}" class="form-label">Content</label>
                            <textarea id="forums_content_{{ forum.id }}" name="forums[content]" class="form-control" rows="5" required>{{ forum.content }}</textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --forum-primary: #00D084;
            --forum-primary-dark: #00b873;
            --forum-primary-light: #7eebc5;
            --forum-primary-very-light: #e6f9f3;
            --forum-secondary: #17303B;
            --forum-text-dark: #333333;
            --forum-text-medium: #555555;
            --forum-text-light: #777777;
            --forum-bg-light: #f8f9fa;
            --forum-bg-white: #ffffff;
            --forum-border-color: #e0e0e0;
            --forum-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
            --forum-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
            --forum-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --forum-radius-sm: 8px;
            --forum-radius-md: 12px;
            --forum-radius-lg: 16px;
            --forum-radius-xl: 24px;
            --forum-radius-circle: 50%;
            --forum-transition-fast: 0.2s ease;
            --forum-transition-normal: 0.3s ease;
        }

        /* View Toggle Styles */
        .view-toggle {
            position: relative;
            overflow: hidden;
            border-radius: 50px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .view-toggle .btn {
            transition: all 0.3s ease;
            border: none;
            padding: 0.5rem 1rem;
            z-index: 1;
            position: relative;
        }

        .view-toggle .btn:first-child {
            border-top-left-radius: 50px;
            border-bottom-left-radius: 50px;
        }

        .view-toggle .btn:last-child {
            border-top-right-radius: 50px;
            border-bottom-right-radius: 50px;
        }

        .view-toggle .btn.active {
            background-color: transparent;
            color: white;
        }

        .view-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 50%;
            height: 100%;
            background-color: var(--forum-primary);
            border-radius: 50px;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            z-index: 0;
        }

        .view-toggle.list-active::before {
            left: 50%;
        }

        /* My Posts Button Styling */
        .my-posts-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: var(--forum-text-medium);
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            position: relative;
            overflow: hidden;
        }

        .my-posts-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,208,132,0.1) 0%, rgba(0,0,0,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .my-posts-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            color: var(--forum-text-dark);
        }

        .my-posts-btn:hover::before {
            opacity: 1;
        }

        .my-posts-btn.active {
            background: linear-gradient(135deg, var(--forum-primary) 0%, var(--forum-primary-dark) 100%);
            color: white;
            box-shadow: 0 3px 6px rgba(0,208,132,0.3);
        }

        .my-posts-btn i {
            transition: all 0.3s ease;
        }

        .my-posts-btn:hover i {
            transform: scale(1.2);
        }

        /* New Post Button Styling */
        .new-post-btn {
            background: linear-gradient(135deg, var(--forum-primary) 0%, var(--forum-primary-dark) 100%);
            color: white;
            border: none;
            box-shadow: 0 3px 6px rgba(0,208,132,0.3);
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            position: relative;
            overflow: hidden;
        }

        .new-post-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255,255,255,0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .new-post-btn:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 5px 10px rgba(0,208,132,0.4);
        }

        .new-post-btn:hover::after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }

        /* View Transitions */
        .col-lg-8 {
            transition: all 0.5s ease;
        }

        .forum-card {
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        }

        /* Grid View Styles */
        .forum-grid-view .forum-card {
            display: block;
            transform-origin: center;
            animation: fadeInScale 0.5s forwards;
        }

        @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        /* List View Styles - Simplified UI */
        .forum-list-view .forum-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0;
            animation: slideInRight 0.5s forwards;
            border-radius: var(--forum-radius-md);
            overflow: hidden;
            border: none;
            box-shadow: var(--forum-shadow-sm);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            position: relative;
            background-color: white;
        }

        .forum-list-view .forum-card:hover {
            box-shadow: var(--forum-shadow-md);
            transform: translateY(-2px);
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .forum-list-view .forum-card .card-body {
            gap: 0.5rem 1.25rem;
            padding: 1rem 1.25rem;
            width: 100%;
            position: relative;
            align-items: center;
        }

        .forum-list-view .forum-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: var(--forum-primary);
            z-index: 1;
        }

        .forum-list-view .forum-card .user-avatar {
            grid-area: avatar;
            width: 50px;
            height: 50px;
            min-width: 50px;
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .forum-list-view .forum-card:hover .user-avatar {
            transform: scale(1.05);
        }

        .forum-list-view .d-flex.align-items-center {
            grid-area: header;
            align-self: start;
        }

        .forum-list-view .forum-content {
            grid-area: content;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin: 0;
            padding: 0;
            font-size: 0.95rem;
            line-height: 1.5;
            color: var(--forum-text-medium);
            position: relative;
            transition: all 0.3s ease;
        }

        .forum-list-view .forum-content::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 30%;
            height: 1.5em;
            background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
            pointer-events: none;
        }

        .forum-list-view .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--forum-text-dark);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .forum-list-view .sentiment-emoji {
            margin-left: 0.5rem;
            font-size: 1.1rem;
        }

        .forum-list-view .forum-post-date {
            grid-area: meta;
            border-top: none;
            padding-top: 0;
            margin-top: 0;
            font-size: 0.85rem;
            color: var(--forum-text-light);
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .forum-list-view .forum-post-date i {
            margin-right: 0.5rem;
            color: var(--forum-primary-light);
        }

        .forum-list-view .comment-count {
            background-color: rgba(0,208,132,0.15);
            color: var(--forum-primary-dark);
            border-radius: 50px;
            padding: 0.2rem 0.6rem;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
        }

        .forum-list-view .forum-dropdown {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 10;
        }

        .forum-list-view .dropdown-menu {
            z-index: 1050;
            box-shadow: var(--forum-shadow-md);
            border: none;
            border-radius: var(--forum-radius-md);
            overflow: hidden;
            animation: fadeInDown 0.3s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced Comments in List View */
        .forum-list-view .comments-section {
            margin: 1rem -1.25rem -1.25rem -1.25rem;
            padding: 1.5rem;
            border-top: 1px solid rgba(0,0,0,0.05);
            background-color: rgba(0,208,132,0.03);
            border-radius: 0 0 var(--forum-radius-md) var(--forum-radius-md);
            transition: all 0.3s ease;
        }

        .forum-list-view .comment-form {
            background-color: white;
            border-radius: var(--forum-radius-md);
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .forum-list-view .comment-form:focus-within {
            box-shadow: 0 4px 12px rgba(0,208,132,0.15);
            transform: translateY(-2px);
        }

        .forum-list-view .comments-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .forum-list-view .comment-item {
            padding: 0;
            border-radius: var(--forum-radius-md);
            background-color: transparent;
            transition: all 0.3s ease;
        }

        .forum-list-view .comment-item:hover {
            transform: translateY(-2px);
        }

        .forum-list-view .comment-bubble {
            background-color: white;
            border-radius: 1rem;
            padding: 1rem 1.25rem;
            position: relative;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border-top-left-radius: 0;
        }

        .forum-list-view .comment-item:hover .comment-bubble {
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            background-color: #fcfcfc;
        }

        .forum-list-view .comment-bubble::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 0;
            width: 0;
            height: 0;
            border-top: 0px solid transparent;
            border-bottom: 12px solid transparent;
            border-right: 12px solid white;
            transition: all 0.3s ease;
        }

        .forum-list-view .comment-item:hover .comment-bubble::before {
            border-right-color: #fcfcfc;
        }

        .forum-list-view .comment-author {
            color: var(--forum-primary-dark);
            font-weight: 700;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .forum-list-view .comment-content {
            color: var(--forum-text-dark);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .forum-list-view .like-button {
            color: #dc3545;
            opacity: 0.5;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.85rem;
        }

        .forum-list-view .like-button:hover,
        .forum-list-view .like-button.liked {
            opacity: 1;
            transform: scale(1.1);
        }

        .forum-list-view .like-button i {
            transition: all 0.3s ease;
        }

        .forum-list-view .like-button:hover i,
        .forum-list-view .like-button.liked i {
            transform: scale(1.2);
        }

        /* Tag Chip Styles - Advanced UI */
        .tag-chip {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: var(--forum-text-medium);
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .tag-chip::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,208,132,0.1) 0%, rgba(0,0,0,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .tag-chip:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 5px 12px rgba(0,0,0,0.1);
            color: var(--forum-text-dark);
        }

        .tag-chip:hover::before {
            opacity: 1;
        }

        .tag-chip.active {
            background: linear-gradient(135deg, var(--forum-primary) 0%, var(--forum-primary-dark) 100%);
            color: white;
            border-color: var(--forum-primary-dark);
            box-shadow: 0 4px 10px rgba(0,208,132,0.3);
            font-weight: 600;
        }

        .tag-chip.active::after {
            content: '';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 20px;
            height: 20px;
            background-color: rgba(255,255,255,0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .tag-chip.tag-clicked {
            animation: tagClickPulse 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        @keyframes tagClickPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .tag-filter-bar {
            position: relative;
        }

        .filter-body {
            border-radius: var(--forum-radius-md);
            box-shadow: var(--forum-shadow-sm);
            transition: all 0.3s ease;
        }

        .filter-header h6 {
            font-weight: 600;
            color: var(--forum-text-dark);
        }

        .clear-filter {
            color: var(--forum-text-medium);
            font-size: 0.85rem;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .clear-filter:hover {
            color: var(--forum-primary);
        }

        .active-filter-indicator {
            animation: fadeIn 0.5s ease;
        }

        /* Top Contributors Styles */
        .top-contributors-card, .tags-card {
            border: none;
            border-radius: var(--forum-radius-md);
            box-shadow: var(--forum-shadow-md);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .top-contributors-card:hover, .tags-card:hover {
            box-shadow: var(--forum-shadow-lg);
            transform: translateY(-3px);
        }

        .top-contributors-card .card-header, .tags-card .card-header {
            background-color: var(--forum-primary);
            border-bottom: none;
            padding: 1rem;
        }

        .contributors-list {
            padding: 0.5rem 0;
        }

        .contributor-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contributor-item:last-child {
            border-bottom: none;
        }

        .contributor-item:hover {
            background-color: rgba(0,0,0,0.02);
        }

        .contributor-item:hover .contributor-rank {
            transform: scale(1.1) rotate(5deg);
        }

        .contributor-item:hover .contributor-avatar {
            transform: scale(1.05);
        }

        .contributor-item:hover .contribution-progress {
            animation: pulse 1.5s infinite;
        }

        .contributor-rank-wrapper {
            position: relative;
            margin-right: 1rem;
            width: 36px;
            height: 36px;
        }

        .contributor-rank {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: var(--forum-primary-light);
            color: var(--forum-primary-dark);
            border-radius: 50%;
            font-weight: bold;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1;
        }

        .contributor-item[data-rank="1"] .contributor-rank {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
        }

        .contributor-item[data-rank="2"] .contributor-rank {
            background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
            color: white;
        }

        .contributor-item[data-rank="3"] .contributor-rank {
            background: linear-gradient(135deg, #CD7F32, #8B4513);
            color: white;
        }

        .contributor-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            border: 2px solid white;
            transition: all 0.3s ease;
            background-color: var(--forum-bg-light);
        }

        .contributor-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .contributor-avatar i {
            font-size: 1.5rem;
            color: var(--forum-primary);
        }

        .contributor-info {
            flex: 1;
        }

        .contributor-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--forum-text-dark);
        }

        .contributor-stats {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .post-count {
            font-weight: 700;
            color: var(--forum-primary);
            margin-right: 0.25rem;
        }

        .post-label {
            font-size: 0.85rem;
            color: var(--forum-text-light);
        }

        .contribution-bar {
            height: 4px;
            background-color: rgba(0,0,0,0.05);
            border-radius: 2px;
            overflow: hidden;
        }

        .contribution-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--forum-primary), var(--forum-primary-light));
            border-radius: 2px;
            transition: width 0.5s ease;
        }

        .empty-contributors, .empty-tags {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: var(--forum-text-light);
        }

        .empty-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        /* Tags Cloud Styles */
        .tags-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            padding: 0.75rem;
            position: relative;
            overflow: hidden;
        }

        .tags-cloud::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,208,132,0.05) 0%, rgba(0,0,0,0) 100%);
            z-index: 0;
            border-radius: var(--forum-radius-md);
        }

        .tag-item {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: var(--forum-text-medium);
            border-radius: 50px;
            font-size: calc(0.85rem * var(--tag-size, 1));
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border: 1px solid transparent;
            transform-origin: center;
            position: relative;
            z-index: 1;
        }

        /* Generate different colors for tags */
        .tag-item:nth-child(5n+1) {
            background: linear-gradient(135deg, #e6f9f3 0%, #f5f5f5 100%);
            border-left: 3px solid rgba(0, 208, 132, 0.5);
        }

        .tag-item:nth-child(5n+2) {
            background: linear-gradient(135deg, #e6f0f9 0%, #f5f5f5 100%);
            border-left: 3px solid rgba(79, 129, 255, 0.5);
        }

        .tag-item:nth-child(5n+3) {
            background: linear-gradient(135deg, #f9f6e6 0%, #f5f5f5 100%);
            border-left: 3px solid rgba(255, 193, 7, 0.5);
        }

        .tag-item:nth-child(5n+4) {
            background: linear-gradient(135deg, #f9e6e6 0%, #f5f5f5 100%);
            border-left: 3px solid rgba(220, 53, 69, 0.5);
        }

        .tag-item:nth-child(5n+5) {
            background: linear-gradient(135deg, #e6f9e9 0%, #f5f5f5 100%);
            border-left: 3px solid rgba(40, 167, 69, 0.5);
        }

        .tag-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
            border-radius: inherit;
        }

        .tag-item:hover {
            color: var(--forum-text-dark);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .tag-item:hover::before {
            opacity: 1;
        }

        .tag-item.active {
            background: linear-gradient(135deg, var(--forum-primary) 0%, var(--forum-primary-dark) 100%);
            color: white;
            border-color: var(--forum-primary-dark);
            box-shadow: 0 4px 10px rgba(0,208,132,0.3);
            border-left: 3px solid white;
        }

        .tag-item.active::after {
            content: '';
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            background-color: white;
            border-radius: 50%;
            box-shadow: 0 0 0 2px var(--forum-primary);
        }

        .tag-item.all-tags {
            background: linear-gradient(135deg, #e9ecef 0%, #d1d7dc 100%);
            font-weight: 500;
            color: var(--forum-text-dark);
        }

        .tag-search .input-group {
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-radius: var(--forum-radius-sm);
            overflow: hidden;
        }

        .tag-search .input-group-text {
            border: none;
            background-color: #f8f9fa;
        }

        .tag-search .form-control {
            border: none;
            padding: 0.6rem 0.75rem;
        }

        .tag-search .form-control:focus {
            box-shadow: none;
            border-color: transparent;
        }

        /* Tag click animation */
        .tag-clicked {
            animation: tagPulse 0.5s ease;
        }

        @keyframes tagPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Tag Link Styles */
        .tag-link {
            color: var(--forum-primary);
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .tag-link:hover {
            color: var(--forum-primary-dark);
            text-decoration: underline;
        }

        /* Header styling */
        .bg-breadcrumb-forums {
            background-position: center;
            background-size: cover;
            position: relative;
        }

        .btn-create-post {
            box-shadow: 0 4px 12px rgba(0, 208, 132, 0.3);
            transition: all 0.3s ease;
        }

        .btn-create-post:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 208, 132, 0.4);
        }

        /* Forum cards styling */
        .forum-card {
            border: none;
            border-radius: var(--forum-radius-md);
            box-shadow: var(--forum-shadow-sm);
            transition: all 0.3s ease;
            overflow: hidden;
            background-color: var(--forum-bg-white);
            position: relative;
        }

        .forum-card:hover {
            box-shadow: var(--forum-shadow-md);
            transform: translateY(-3px);
        }

        .forum-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--forum-primary), var(--forum-primary-light));
            border-radius: var(--forum-radius-md) 0 0 var(--forum-radius-md);
            opacity: 0.8;
        }

        .forum-card .card-body {
            padding: 1.5rem;
        }

        .forum-content {
            padding: 0.75rem 0;
            font-size: 1.05rem;
            line-height: 1.6;
            color: var(--forum-text-dark);
        }

        .forum-post-date {
            font-size: 0.85rem;
            color: var(--forum-text-light);
            border-top: 1px dashed rgba(0,0,0,0.05);
            padding-top: 0.5rem;
            margin-top: 1rem;
        }

        .forum-actions {
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding-top: 1rem;
            margin-top: 0.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--forum-text-dark);
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .sentiment-emoji {
            font-size: 1.3rem;
            margin-left: 0.5rem;
            vertical-align: middle;
            display: inline-block;
            transition: all 0.3s ease;
            transform-origin: center;
            animation: sentiment-pulse 2s infinite alternate;
        }

        @keyframes sentiment-pulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.2); }
        }

        .forum-card:hover .sentiment-emoji {
            animation-duration: 1s;
        }

        /* Badge styling */
        .bg-primary-soft {
            background-color: rgba(0, 208, 132, 0.15);
            color: var(--forum-primary-dark);
        }

        /* User avatar styling */
        .user-avatar {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: var(--forum-bg-light);
            color: var(--forum-primary);
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Comments section styling */
        .comments-section {
            display: none;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--forum-border-color);
            background-color: var(--forum-bg-light);
            border-radius: 0 0 var(--forum-radius-md) var(--forum-radius-md);
            padding: 1.5rem;
            margin: 1rem -1.5rem -1.5rem -1.5rem;
        }

        .comment-bubble {
            position: relative;
            background-color: var(--forum-bg-white);
            border-radius: var(--forum-radius-md);
            padding: 0.75rem 1rem;
            box-shadow: var(--forum-shadow-sm);
            margin-bottom: 0.5rem;
        }

        .comment-author {
            color: var(--forum-primary);
            font-size: 0.85rem;
        }

        .comment-content {
            word-break: break-word;
            white-space: pre-wrap;
            font-size: 0.95rem;
            color: var(--forum-text-dark);
        }

        .comment-input {
            border-radius: var(--forum-radius-md) 0 0 var(--forum-radius-md);
            border: 1px solid var(--forum-border-color);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .comment-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(0, 208, 132, 0.25);
            border-color: var(--forum-primary-light);
        }

        .comment-input.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .comment-submit {
            border-radius: 0 var(--forum-radius-md) var(--forum-radius-md) 0;
            padding: 0.75rem 1.25rem;
        }

        /* Comment menu styling */
        .comment-menu, .forum-menu {
            opacity: 0.6;
            transition: opacity 0.2s;
            color: var(--forum-text-medium);
        }

        .comment-menu:hover, .forum-menu:hover {
            opacity: 1;
            color: var(--forum-text-dark);
        }

        /* Dropdown styling */
        .dropdown-menu {
            position: absolute;
            right: 0;
            background-color: white;
            border: none;
            border-radius: var(--forum-radius-sm);
            box-shadow: var(--forum-shadow-md);
            display: none;
            min-width: 10rem;
            padding: 0.5rem 0;
            z-index: 1000;
        }

        .dropdown-menu.show {
            display: block;
            animation: fadeIn 0.2s ease;
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.5rem 1rem;
            clear: both;
            font-weight: 400;
            color: var(--forum-text-dark);
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--forum-primary-very-light);
            color: var(--forum-primary-dark);
        }

        .dropdown-item.text-danger:hover {
            background-color: #fee2e2;
            color: #dc3545;
        }

        .dropdown {
            position: relative;
        }

        /* Like button styling */
        .like-button {
            color: #adb5bd;
            transition: all 0.2s ease;
            position: relative;
            display: inline-flex;
            align-items: center;
        }

        .like-button:hover {
            color: #dc3545;
            transform: scale(1.1);
        }

        .like-button.liked {
            color: #dc3545;
        }

        .like-button.liked i {
            animation: heartBeat 0.3s ease-in-out;
        }

        .like-button .like-count {
            transition: all 0.3s ease;
        }

        .like-button.liked .like-count {
            font-weight: bold;
        }

        .like-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 30px;
            height: 30px;
            background-color: rgba(220, 53, 69, 0.2);
            border-radius: 50%;
            z-index: -1;
            animation: likeRipple 0.6s ease-out;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.4); }
            100% { transform: scale(1); }
        }

        @keyframes likeRipple {
            0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(3); opacity: 0; }
        }

        /* Toggle comments button */
        .toggle-comments {
            transition: all 0.3s ease;
        }

        .toggle-comments:hover {
            background-color: var(--forum-primary);
            color: white;
        }

        /* Empty states styling */
        .empty-forums, .empty-comments {
            color: var(--forum-text-medium);
        }

        .empty-forums-icon {
            color: var(--forum-text-light);
            opacity: 0.7;
        }

        /* Pagination styling */
        .forum-pagination .pagination {
            justify-content: center;
            margin-bottom: 0;
        }

        .forum-pagination .page-link {
            color: var(--forum-primary);
            border-radius: var(--forum-radius-sm);
            margin: 0 2px;
            border: none;
            box-shadow: var(--forum-shadow-sm);
        }

        .forum-pagination .page-item.active .page-link {
            background-color: var(--forum-primary);
            border-color: var(--forum-primary);
        }

        .forum-pagination .page-item.disabled .page-link {
            color: var(--forum-text-light);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            20% { transform: translateX(-5px); }
            40% { transform: translateX(5px); }
            60% { transform: translateX(-3px); }
            80% { transform: translateX(3px); }
            100% { transform: translateX(0); }
        }

        /* Forum header stats */
        .forum-stats .badge {
            font-weight: 500;
            font-size: 0.85rem;
        }

        /* Loading Transition */
        .loading-transition {
            position: relative;
            overflow: hidden;
        }

        .loading-transition::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(3px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.3s ease forwards;
        }

        .loading-transition::before {
            content: '';
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 3px solid rgba(0, 208, 132, 0.3);
            border-radius: 50%;
            border-top-color: var(--forum-primary);
            z-index: 10000;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Tag Link Styling */
        .tag-link {
            display: inline-block;
            color: var(--forum-primary);
            font-weight: 600;
            text-decoration: none;
            position: relative;
            transition: all 0.3s ease;
            padding: 0 0.2rem;
        }

        .tag-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--forum-primary);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }

        .tag-link:hover {
            color: var(--forum-primary-dark);
        }

        .tag-link:hover::after {
            transform: scaleX(1);
            transform-origin: left;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .forum-card .card-body {
                padding: 1.25rem;
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "content"
                    "meta"
                    "actions";
            }

            .forum-list-view .forum-card .user-avatar {
                display: none;
            }

            .forum-list-view .forum-card .forum-actions {
                flex-direction: row;
                justify-content: space-between;
                width: 100%;
                margin-top: 1rem;
            }

            .comments-section {
                margin: 1rem -1.25rem -1.25rem -1.25rem;
                padding: 1.25rem;
            }

            .card-title {
                font-size: 1.1rem;
            }

            .forum-list-view .toggle-comments {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }
        }
    </style>
{% endblock %}

{% block javascripts %}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Define asset base path for use in JavaScript
        const asset_base_path = '{{ asset('') }}';

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize view mode
            const forumsContainer = document.querySelector('.col-lg-8');
            const viewMode = '{{ viewMode }}';
            if (viewMode === 'list') {
                forumsContainer.classList.add('forum-list-view');
                // Hide comment buttons in list view
                document.querySelectorAll('.toggle-comments').forEach(btn => {
                    btn.style.display = 'none';
                });
            }

            // Handle view toggle
            const viewToggleButtons = document.querySelectorAll('.view-toggle button');
            viewToggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const view = this.getAttribute('data-view');

                    // Update active button
                    viewToggleButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Update view
                    if (view === 'list') {
                        forumsContainer.classList.add('forum-list-view');
                    } else {
                        forumsContainer.classList.remove('forum-list-view');
                    }

                    // Save preference in URL
                    const url = new URL(window.location);
                    url.searchParams.set('view', view);
                    window.history.pushState({}, '', url);
                });
            });

            // Add animation classes to forum cards
            const forumCards = document.querySelectorAll('.forum-card');
            forumCards.forEach((card, index) => {
                card.classList.add('wow', 'fadeInUp');
                card.setAttribute('data-wow-delay', `${0.1 + (index * 0.1)}s`);
            });

            // Highlight hashtags in forum content with enhanced styling
            document.querySelectorAll('.forum-content p').forEach(content => {
                const text = content.innerHTML;
                const highlightedText = text.replace(/#(\w+)/g, '<a href="{{ path('front_forums_index') }}?tag=$1" class="tag-link" data-tag="$1">#$1</a>');
                content.innerHTML = highlightedText;
            });

            // Add click event to tag links for smooth transition
            document.querySelectorAll('.tag-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tag = this.getAttribute('data-tag');

                    // Highlight the corresponding tag chip
                    const tagChips = document.querySelectorAll('.tag-chip');
                    tagChips.forEach(chip => {
                        if (chip.textContent.trim() === '#' + tag) {
                            chip.classList.add('tag-clicked');
                        }
                    });

                    // Show loading animation
                    document.body.classList.add('loading-transition');

                    // Redirect after a short delay for animation
                    setTimeout(() => {
                        window.location.href = "{{ path('front_forums_index') }}?tag=" + tag;
                    }, 300);
                });
            });

            // Enhanced tag chip filtering
            document.querySelectorAll('.tag-chip, .tag-item').forEach(tagElement => {
                tagElement.addEventListener('click', function(e) {
                    if (this.classList.contains('active')) {
                        return; // Don't do anything if already active
                    }

                    e.preventDefault();
                    const tag = this.getAttribute('data-tag');

                    // Add clicked animation
                    this.classList.add('tag-clicked');

                    // Show loading animation
                    document.body.classList.add('loading-transition');

                    // Build the URL with current parameters
                    const url = new URL(window.location.href);

                    // Preserve view mode and my_posts parameters
                    const viewMode = url.searchParams.get('view');
                    const myPosts = url.searchParams.get('my_posts');

                    let newUrl = "{{ path('front_forums_index') }}";
                    let params = [];

                    if (tag) {
                        params.push('tag=' + tag);
                    }

                    if (viewMode) {
                        params.push('view=' + viewMode);
                    }

                    if (myPosts) {
                        params.push('my_posts=' + myPosts);
                    }

                    if (params.length > 0) {
                        newUrl += '?' + params.join('&');
                    }

                    // Redirect after a short delay for animation
                    setTimeout(() => {
                        window.location.href = newUrl;
                    }, 300);
                });
            });

            // Toggle comments visibility with animation - only in Grid View
            const toggleButtons = document.querySelectorAll('.toggle-comments');
            toggleButtons.forEach(button => {
                // Check if we're in list view and hide the button if so
                const updateButtonVisibility = () => {
                    if (document.querySelector('.forum-list-view')) {
                        button.style.display = 'none';
                    } else {
                        button.style.display = '';
                    }
                };

                // Initial check
                updateButtonVisibility();

                // Update when view changes
                document.querySelectorAll('.view-toggle button').forEach(viewBtn => {
                    viewBtn.addEventListener('click', updateButtonVisibility);
                });

                button.addEventListener('click', function() {
                    // Only work in grid view
                    if (document.querySelector('.forum-list-view')) {
                        return;
                    }

                    const forumId = this.getAttribute('data-forum-id');
                    const commentsSection = document.getElementById(`comments-section-${forumId}`);
                    const isHidden = commentsSection.style.display === 'none' || commentsSection.style.display === '';

                    if (isHidden) {
                        // Show comments
                        commentsSection.style.display = 'block';
                        commentsSection.style.opacity = '0';
                        commentsSection.style.transform = 'translateY(-10px)';

                        // Animate in
                        setTimeout(() => {
                            commentsSection.style.transition = 'all 0.3s ease';
                            commentsSection.style.opacity = '1';
                            commentsSection.style.transform = 'translateY(0)';
                        }, 10);

                        this.innerHTML = '<i class="far fa-comment-dots me-1"></i> Hide Comments';
                        this.classList.add('active');
                    } else {
                        // Animate out
                        commentsSection.style.opacity = '0';
                        commentsSection.style.transform = 'translateY(-10px)';

                        // Hide after animation
                        setTimeout(() => {
                            commentsSection.style.display = 'none';
                        }, 300);

                        this.innerHTML = '<i class="far fa-comments me-1"></i> Show Comments';
                        this.classList.remove('active');
                    }
                });
            });

            // Initialize comment menu dropdowns
            document.querySelectorAll('.comment-menu').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const dropdown = this.nextElementSibling;

                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        if (menu !== dropdown) {
                            menu.classList.remove('show');
                        }
                    });

                    // Toggle current dropdown
                    dropdown.classList.toggle('show');
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });

            // Handle comment submission with improved UX
            document.querySelectorAll('.comment-form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const input = form.querySelector('input[name="content"]');
                    const errorDiv = form.querySelector('.comment-error');
                    const content = input.value.trim();

                    // Clear previous error
                    errorDiv.style.display = 'none';
                    errorDiv.textContent = '';

                    if (!content) {
                        errorDiv.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i> Please enter a comment';
                        errorDiv.style.display = 'block';
                        input.focus();

                        // Shake animation for error
                        input.classList.add('is-invalid');
                        input.style.animation = 'shake 0.5s';
                        setTimeout(() => {
                            input.style.animation = '';
                        }, 500);
                        return;
                    }

                    // Show loading state
                    form.classList.add('loading');
                    const submitButton = form.querySelector('button[type="submit"]');
                    const originalButtonContent = submitButton.innerHTML;
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

                    const forumId = form.getAttribute('data-forum-id');
                    const commentsList = document.getElementById(`comments-list-${forumId}`);

                    fetch('/front/pages/comments/new', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: new URLSearchParams({
                            forumId: forumId,
                            content: content
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Close any open edit forms
                            document.querySelectorAll('.edit-form').forEach(editForm => {
                                const contentElement = editForm.previousElementSibling;
                                if (contentElement && contentElement.classList.contains('comment-content')) {
                                    contentElement.style.display = '';
                                }
                                editForm.remove();
                            });

                            // Clear input
                            input.value = '';

                            // Update all comment count elements
                            const forumItem = form.closest('.forum-item');
                            const countElements = forumItem.querySelectorAll('.comment-count');
                            const currentCount = parseInt(countElements[0].textContent.trim().replace(/\D/g, '') || '0');
                            const newCount = currentCount + 1;

                            // Update all instances of comment count
                            countElements.forEach(el => {
                                // Check if the element has an icon
                                if (el.querySelector('i')) {
                                    // If it has an icon, preserve it and update only the text part
                                    const icon = el.querySelector('i').outerHTML;
                                    el.innerHTML = `${icon}${newCount}`;
                                } else {
                                    // If no icon, just update the text
                                    el.textContent = newCount;
                                }
                            });

                            // Remove "no comments" message if it exists
                            const emptyMessage = commentsList.querySelector('.empty-comments');
                            if (emptyMessage) {
                                emptyMessage.remove();
                            }

                            // Create and append new comment with animation
                            const newComment = createCommentElement(data.comment);
                            newComment.style.opacity = '0';
                            newComment.style.transform = 'translateY(20px)';

                            // Always add to the top of the list (newest first)
                            if (commentsList.firstChild) {
                                commentsList.insertBefore(newComment, commentsList.firstChild);
                            } else {
                                commentsList.appendChild(newComment);
                            }

                            // Animate in
                            setTimeout(() => {
                                newComment.style.transition = 'all 0.5s ease';
                                newComment.style.opacity = '1';
                                newComment.style.transform = 'translateY(0)';
                            }, 10);

                            // Success feedback
                            const successFeedback = document.createElement('div');
                            successFeedback.className = 'text-success mt-2 comment-success';
                            successFeedback.innerHTML = '<i class="fas fa-check-circle me-1"></i> Comment posted successfully';
                            form.appendChild(successFeedback);

                            // Remove success message after 3 seconds
                            setTimeout(() => {
                                successFeedback.remove();
                            }, 3000);
                        } else {
                            errorDiv.innerHTML = `<i class="fas fa-exclamation-circle me-1"></i> ${data.error || 'Error posting comment'}`;
                            errorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        errorDiv.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i> Error posting comment';
                        errorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        form.classList.remove('loading');
                        submitButton.disabled = false;
                        submitButton.innerHTML = originalButtonContent;
                    });
                });
            });

            // Handle comment edit
            document.addEventListener('click', function(e) {
                const editButton = e.target.closest('.edit-comment');
                if (!editButton) return;

                e.preventDefault();

                const commentId = editButton.getAttribute('data-comment-id');
                const commentItem = document.querySelector(`#comment-${commentId}`);

                // Check if an edit form already exists for this comment
                const existingForm = commentItem.querySelector('.edit-form');
                if (existingForm) {
                    // Focus on the existing form instead of creating a new one
                    existingForm.querySelector('input').focus();
                    return;
                }

                // Remove any other edit forms that might be open
                document.querySelectorAll('.edit-form').forEach(form => {
                    const contentElement = form.previousElementSibling;
                    if (contentElement && contentElement.classList.contains('comment-content')) {
                        contentElement.style.display = '';
                    }
                    form.remove();
                });

                const contentP = commentItem.querySelector('.comment-content');
                const currentContent = contentP.textContent;

                // Create edit form
                const form = document.createElement('form');
                form.className = 'edit-form mb-2';
                form.innerHTML = `
                    <div class="input-group">
                        <input type="text" class="form-control" value="${currentContent.replace(/"/g, '&quot;')}" >
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary cancel-edit">Cancel</button>
                    </div>
                    <div class="text-danger comment-error mt-2" style="display: none;"></div>
                `;

                contentP.style.display = 'none';
                contentP.parentNode.insertBefore(form, contentP.nextSibling);

                const input = form.querySelector('input');
                input.focus();
                input.select();

                // Close dropdown
                const dropdown = editButton.closest('.dropdown-menu');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }

                // Handle cancel
                form.querySelector('.cancel-edit').addEventListener('click', () => {
                    form.remove();
                    contentP.style.display = '';
                });

                // Handle save
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const newContent = input.value.trim();
                    if (!newContent) return;

                    const errorDiv = form.querySelector('.comment-error');
                    const submitButton = form.querySelector('button[type="submit"]');
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                    const commentId = editButton.getAttribute('data-comment-id');

                    fetch(`/front/pages/comments/${commentId}/edit`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: new URLSearchParams({
                            content: newContent
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            contentP.textContent = data.comment.content;
                            form.remove();
                            contentP.style.display = '';
                        } else {
                            errorDiv.textContent = data.error || '• Error updating comment';
                            errorDiv.style.display = 'block';
                            submitButton.disabled = false;
                            submitButton.textContent = 'Save';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        errorDiv.textContent = '• Error updating comment';
                        errorDiv.style.display = 'block';
                        submitButton.disabled = false;
                        submitButton.textContent = 'Save';
                    });
                });
            });

            // Handle comment delete
            document.addEventListener('click', function(e) {
                const deleteButton = e.target.closest('.delete-comment');
                if (!deleteButton) return;

                e.preventDefault();
                e.stopPropagation();

                if (!confirm('Are you sure you want to delete this comment?')) return;

                const commentId = deleteButton.getAttribute('data-comment-id');
                if (!commentId) return;

                const commentItem = document.querySelector(`#comment-${commentId}`);
                if (!commentItem) return;

                // Disable button and show loading state
                const originalHtml = deleteButton.innerHTML;
                deleteButton.disabled = true;
                deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                // Close dropdown
                const dropdown = deleteButton.closest('.dropdown-menu');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }

                fetch(`/front/pages/comments/${commentId}/delete`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close any open edit forms
                        document.querySelectorAll('.edit-form').forEach(form => {
                            const contentElement = form.previousElementSibling;
                            if (contentElement && contentElement.classList.contains('comment-content')) {
                                contentElement.style.display = '';
                            }
                            form.remove();
                        });

                        // Update all comment count elements
                        const forumItem = commentItem.closest('.forum-item');
                        if (forumItem) {
                            const countElements = forumItem.querySelectorAll('.comment-count');
                            if (countElements.length > 0) {
                                const currentCount = parseInt(countElements[0].textContent.trim().replace(/\D/g, '') || '0', 10);
                                const newCount = Math.max(0, currentCount - 1);

                                // Update all instances of comment count
                                countElements.forEach(el => {
                                    // Check if the element has an icon
                                    if (el.querySelector('i')) {
                                        // If it has an icon, preserve it and update only the text part
                                        const icon = el.querySelector('i').outerHTML;
                                        el.innerHTML = `${icon}${newCount}`;
                                    } else {
                                        // If no icon, just update the text
                                        el.textContent = newCount;
                                    }
                                });
                            }
                        }

                        // Remove comment
                        commentItem.remove();

                        // Check if we need to show "no comments" message
                        const commentsList = document.querySelector(`#comments-list-${commentId}`);
                        if (commentsList && !commentsList.querySelector('.comment-item')) {
                            commentsList.innerHTML = '<p class="text-muted text-center mb-0">No comments yet. Be the first to comment!</p>';
                        }
                    } else {
                        alert(data.error || 'Error deleting comment');
                        deleteButton.disabled = false;
                        deleteButton.innerHTML = originalHtml;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting comment');
                    deleteButton.disabled = false;
                    deleteButton.innerHTML = originalHtml;
                });
            });

            // Handle comment likes with animation
            document.addEventListener('click', function(e) {
                const likeButton = e.target.closest('.like-button');
                if (!likeButton) return;

                e.preventDefault();

                const commentId = likeButton.getAttribute('data-comment-id');
                const isLiked = likeButton.classList.contains('liked');
                const likeCount = likeButton.querySelector('.like-count');
                const currentCount = parseInt(likeCount.textContent);

                // Create ripple animation element
                const ripple = document.createElement('span');
                ripple.className = 'like-animation';
                likeButton.appendChild(ripple);

                // Remove ripple after animation completes
                setTimeout(() => {
                    ripple.remove();
                }, 600);

                fetch(`/front/pages/comments/${commentId}/upvote`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (isLiked) {
                            likeButton.classList.remove('liked');
                            // Animate count down
                            likeCount.style.transform = 'translateY(-10px)';
                            likeCount.style.opacity = '0';
                            setTimeout(() => {
                                likeCount.textContent = currentCount - 1;
                                likeCount.style.transform = 'translateY(10px)';
                                setTimeout(() => {
                                    likeCount.style.transform = 'translateY(0)';
                                    likeCount.style.opacity = '1';
                                }, 10);
                            }, 150);
                        } else {
                            likeButton.classList.add('liked');
                            // Animate count up
                            likeCount.style.transform = 'translateY(10px)';
                            likeCount.style.opacity = '0';
                            setTimeout(() => {
                                likeCount.textContent = currentCount + 1;
                                likeCount.style.transform = 'translateY(-10px)';
                                setTimeout(() => {
                                    likeCount.style.transform = 'translateY(0)';
                                    likeCount.style.opacity = '1';
                                }, 10);
                            }, 150);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });

            // Handle forum editing
            document.querySelectorAll('.edit-forum-form').forEach(form => {
                const titleInput = form.querySelector('input[name="forums[title]"]');
                const contentInput = form.querySelector('textarea[name="forums[content]"]');

                // Title validation
                if (titleInput) {
                    titleInput.addEventListener('input', function() {
                        const feedback = this.nextElementSibling;
                        const value = this.value;

                        // Remove any characters that aren't letters or underscores
                        const sanitizedValue = value.replace(/[^A-Za-z_]/g, '');
                        if (sanitizedValue !== value) {
                            this.value = sanitizedValue;
                        }

                        if (!value) {
                            this.classList.add('is-invalid');
                            feedback.textContent = 'Title is required';
                        } else if (value.length < 3) {
                            this.classList.add('is-invalid');
                            feedback.textContent = 'Title must be at least 3 characters';
                        } else if (!/^[A-Za-z_]+$/.test(value)) {
                            this.classList.add('is-invalid');
                            feedback.textContent = 'Only letters and underscores are allowed';
                        } else {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                            feedback.textContent = '';
                        }
                    });
                }

                // Content validation
                if (contentInput) {
                    contentInput.setAttribute('minlength', '20');
                    contentInput.setAttribute('maxlength', '10000');

                    contentInput.addEventListener('input', function() {
                        const remainingChars = this.getAttribute('maxlength') - this.value.length;
                        const feedback = this.nextElementSibling;

                        if (this.value.length < 20) {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                            feedback.textContent = `Please enter at least 20 characters (${20 - this.value.length} more needed)`;
                        } else if (remainingChars < 100) {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                            feedback.textContent = `${remainingChars} characters remaining`;
                        } else {
                            this.classList.remove('is-invalid');
                            this.classList.add('is-valid');
                            feedback.textContent = '';
                        }
                    });
                }

                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    let isValid = true;

                    // Validate title
                    if (titleInput && (!titleInput.value || titleInput.value.length < 3 || !/^[A-Za-z_]+$/.test(titleInput.value))) {
                        isValid = false;
                        titleInput.classList.add('is-invalid');
                    }

                    // Validate content
                    if (contentInput && contentInput.value.length < 20) {
                        isValid = false;
                        contentInput.classList.add('is-invalid');
                    }

                    if (!isValid) {
                        return;
                    }

                    const formData = new FormData(this);
                    const submitButton = this.querySelector('button[type="submit"]');
                    submitButton.disabled = true;

                    fetch(this.action, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (response.ok) {
                            window.location.reload();
                        } else {
                            alert('Error updating forum');
                            submitButton.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error updating forum');
                        submitButton.disabled = false;
                    });
                });
            });

            // Handle forum deletion
            document.querySelectorAll('.delete-forum').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const forumId = this.getAttribute('data-forum-id');
                    const csrfToken = this.getAttribute('data-token');

                    if (confirm('Are you sure you want to delete this forum? This action cannot be undone.')) {
                        // Create a form with CSRF token
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = this.getAttribute('href');

                        // Add CSRF token
                        const csrfTokenInput = document.createElement('input');
                        csrfTokenInput.type = 'hidden';
                        csrfTokenInput.name = '_token';
                        csrfTokenInput.value = csrfToken;
                        form.appendChild(csrfTokenInput);

                        // Submit the form
                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });

            function createCommentElement(comment) {
                const div = document.createElement('div');
                div.className = 'comment-item mb-3';
                div.id = `comment-${comment.id}`;

                // Determine user avatar HTML
                let userAvatarHtml = '';
                if (comment.user && comment.user.image) {
                    // Fix the image path to match the server path format
                    const imagePath = comment.user.image.includes('/')
                        ? comment.user.image
                        : `profiles/${comment.user.image}`;
                    userAvatarHtml = `<img src="http://localhost/img/${imagePath}" alt="${comment.user.fullName}" class="rounded-circle">`;
                } else {
                    userAvatarHtml = `<i class="fas fa-user-circle fa-2x text-primary"></i>`;
                }

                // Determine user name HTML
                let userNameHtml = '';
                if (comment.user) {
                    userNameHtml = `<p class="mb-1 fw-bold small comment-author">${comment.user.fullName}</p>`;
                } else {
                    userNameHtml = `<p class="mb-1 fw-bold small comment-author">Unknown User</p>`;
                }

                // Format date
                const createdAt = comment.createdAt || new Date().toLocaleString();

                div.innerHTML = `
                    <div class="d-flex">
                        <!-- User Avatar -->
                        <div class="user-avatar me-2">
                            ${userAvatarHtml}
                        </div>

                        <div class="flex-grow-1">
                            <div class="comment-bubble">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <!-- User Name -->
                                        ${userNameHtml}
                                        <!-- Comment Content -->
                                        <p class="mb-0 comment-content">${comment.content}</p>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-link text-muted p-0 comment-menu" type="button">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <button class="dropdown-item edit-comment" data-comment-id="${comment.id}">
                                                <i class="fas fa-edit me-2"></i>Edit
                                            </button>
                                            <button class="dropdown-item text-danger delete-comment" data-comment-id="${comment.id}">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mt-1">
                                <small class="text-muted me-3">${createdAt}</small>
                                <button class="btn btn-link p-0 like-button" data-comment-id="${comment.id}">
                                    <i class="fas fa-heart"></i>
                                    <span class="like-count ms-1">${comment.upvotes || 0}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // Initialize event listeners for the new comment
                setTimeout(() => {
                    const menuButton = div.querySelector('.comment-menu');
                    if (menuButton) {
                        menuButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const dropdown = this.nextElementSibling;
                            dropdown.classList.toggle('show');
                        });
                    }

                    // We don't need to add a click handler here anymore
                    // The global event listener will handle all edit button clicks
                    // This prevents duplicate event handlers and multiple forms
                }, 100);

                return div;
            }

            // Initialize forum menu dropdowns
            document.querySelectorAll('.forum-menu').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const dropdown = this.nextElementSibling;

                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                        if (menu !== dropdown) {
                            menu.classList.remove('show');
                        }
                    });

                    // Toggle current dropdown
                    dropdown.classList.toggle('show');
                });
            });

            // Forum creation form validation
            const createForm = document.querySelector('.create-forum-form');
            if (createForm) {
                const formFields = createForm.querySelectorAll('input[type="text"], textarea');

                formFields.forEach(field => {
                    // Add required attribute and minlength
                    if (field.name.includes('[title]')) {
                        field.setAttribute('required', '');
                        field.setAttribute('minlength', '3');
                        field.setAttribute('maxlength', '255');
                        field.setAttribute('pattern', '[A-Za-z_]+');

                        // Special validation for title
                        field.addEventListener('input', function() {
                            const feedback = this.nextElementSibling;
                            const value = this.value;

                            // Remove any characters that aren't letters or underscores
                            const sanitizedValue = value.replace(/[^A-Za-z_]/g, '');
                            if (sanitizedValue !== value) {
                                this.value = sanitizedValue;
                            }

                            if (!value) {
                                this.classList.add('is-invalid');
                                feedback.textContent = 'Title is required';
                            } else if (value.length < 3) {
                                this.classList.add('is-invalid');
                                feedback.textContent = 'Title must be at least 3 characters';
                            } else if (!/^[A-Za-z_]+$/.test(value)) {
                                this.classList.add('is-invalid');
                                feedback.textContent = 'Only letters and underscores are allowed';
                            } else {
                                this.classList.remove('is-invalid');
                                this.classList.add('is-valid');
                                feedback.textContent = '';
                            }
                        });
                    }
                    if (field.name.includes('[content]')) {
                        field.setAttribute('required', '');
                        field.setAttribute('minlength', '20');
                        field.setAttribute('maxlength', '10000');
                    }

                    // Validate on input
                    field.addEventListener('input', function() {
                        if (field.name.includes('[content]')) {
                            const remainingChars = field.getAttribute('maxlength') - field.value.length;
                            const feedback = field.nextElementSibling;
                            if (field.value.length < 20) {
                                field.classList.remove('is-valid');
                                field.classList.add('is-invalid');
                                feedback.textContent = `Please enter at least 20 characters (${20 - field.value.length} more needed)`;
                            } else if (remainingChars < 100) {
                                field.classList.remove('is-invalid');
                                field.classList.add('is-valid');
                                feedback.textContent = `${remainingChars} characters remaining`;
                            } else {
                                field.classList.remove('is-invalid');
                                field.classList.add('is-valid');
                                feedback.textContent = '';
                            }
                        } else {
                            validateField(this);
                        }
                    });

                    // Validate on blur
                    field.addEventListener('blur', function() {
                        validateField(this);
                    });
                });

                // Form submission validation
                createForm.addEventListener('submit', function(event) {
                    let isValid = true;
                    formFields.forEach(field => {
                        if (field.name.includes('[content]')) {
                            if (field.value.length < 20) {
                                isValid = false;
                                field.classList.add('is-invalid');
                            }
                        } else if (!validateField(field)) {
                            isValid = false;
                        }
                    });

                    if (!isValid) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                });
            }

            // Validation helper function
            function validateField(field) {
                const feedback = field.nextElementSibling;
                let isValid = field.checkValidity();
                let message = '';

                if (!field.value) {
                    message = 'This field is required';
                    isValid = false;
                } else if (field.minLength && field.value.length < field.minLength) {
                    message = `Minimum length is ${field.minLength} characters`;
                    isValid = false;
                } else if (field.maxLength && field.value.length > field.maxLength) {
                    message = `Maximum length is ${field.maxLength} characters`;
                    isValid = false;
                }

                if (isValid) {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                    if (feedback) {
                        feedback.textContent = '';
                    }
                } else {
                    field.classList.remove('is-valid');
                    field.classList.add('is-invalid');
                    if (feedback) {
                        feedback.textContent = message;
                    }
                }

                return isValid;
            }
        });
    </script>
    <script src="{{ asset('js/forum-enhancements.js') }}"></script>
{% endblock %}