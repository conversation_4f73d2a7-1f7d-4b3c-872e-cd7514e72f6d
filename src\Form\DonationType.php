<?php

namespace App\Form;

use App\Entity\Donation;
use App\Entity\Partners;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DonationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('amount', NumberType::class, [
                'label' => 'Montant *',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Entrez le montant',
                    'min' => 1,
                    'max' => 1000000,
                    'step' => 1,
                    'inputmode' => 'numeric',
                    'pattern' => '[0-9]*'
                ],
                'scale' => 0,
                'html5' => false
            ])
            ->add('donation_date', DateType::class, [
                'label' => 'Date de donation *',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => [
                    'class' => 'form-control datepicker',
                    'autocomplete' => 'off'
                ]
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type de donation *',
                'choices' => [
                    'Monétaire' => 'Monetary',
                    'Matériel' => 'Material',
                    'Service' => 'Service'
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'placeholder' => 'Sélectionnez le type'
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Statut *',
                'choices' => [
                    'En attente' => 'pending',
                    'Approuvé' => 'approved',
                    'Complété' => 'completed',
                    'Annulé' => 'cancelled'
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'data' => 'pending'
            ])
            ->add('partner_id', EntityType::class, [
                'label' => 'Partenaire *',
                'class' => Partners::class,
                'choice_label' => 'name',
                'placeholder' => 'Sélectionnez un partenaire',
                'attr' => [
                    'class' => 'form-select'
                ]
            ]);

        // Add form event listener to handle payment method field dynamically
        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            $donation = $event->getData();
            $form = $event->getForm();

            // Only show payment method for monetary donations
            if (!$donation || $donation->getType() === 'Monetary' || $donation->getType() === null) {
                $form->add('payment_method', ChoiceType::class, [
                    'label' => 'Méthode de paiement *',
                    'choices' => [
                        'Espèces' => 'cash',
                        'Virement bancaire' => 'bank_transfer',
                        'Chèque' => 'check',
                        'Carte de crédit' => 'credit_card'
                    ],
                    'attr' => [
                        'class' => 'form-select'
                    ],
                    'placeholder' => 'Sélectionnez la méthode de paiement',
                    'required' => true
                ]);
            }
        });

        // Add listener for form submission to handle payment method
        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
            $data = $event->getData();
            $form = $event->getForm();

            if (isset($data['type'])) {
                if ($data['type'] === 'Monetary') {
                    if (!$form->has('payment_method')) {
                        $form->add('payment_method', ChoiceType::class, [
                            'label' => 'Méthode de paiement *',
                            'choices' => [
                                'Espèces' => 'cash',
                                'Virement bancaire' => 'bank_transfer',
                                'Chèque' => 'check',
                                'Carte de crédit' => 'credit_card'
                            ],
                            'attr' => [
                                'class' => 'form-select'
                            ],
                            'placeholder' => 'Sélectionnez la méthode de paiement',
                            'required' => true
                        ]);
                    }
                } else {
                    // For non-monetary donations, set payment_method to null
                    $data['payment_method'] = null;
                    $event->setData($data);
                }
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Donation::class,
            'attr' => [
                'class' => 'needs-validation',
                'novalidate' => 'novalidate'
            ]
        ]);
    }
}
