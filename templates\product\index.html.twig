{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Product Management</h1>
      <p class="text-muted">Manage and monitor all products in the system</p>
    </div>
    <div class="col-auto">
      <a href="{{ path('app_product_new') }}" class="btn btn-primary rounded-pill">
        <i class="ri-add-line me-1"></i> Add New Product
      </a>
    </div>
  </div>

  {% for message in app.flashes('success') %}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endfor %}

  {% for message in app.flashes('error') %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      {{ message }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endfor %}

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-md-3 mb-4 mb-md-0">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
              <i class="ri-shopping-bag-line text-primary fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Total Products</h6>
              <h3 class="mb-0">{{ products|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-4 mb-md-0">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
              <i class="ri-leaf-line text-success fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Ecological Products</h6>
              <h3 class="mb-0">{{ products|filter(p => p.isEcological)|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 mb-4 mb-md-0">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
              <i class="ri-stack-line text-warning fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Categories</h6>
              <h3 class="mb-0">{{ products|map(p => p.categorie)|filter(c => c)|unique|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stats-icon bg-info-subtle rounded-3 p-3 me-3">
              <i class="ri-map-pin-line text-info fs-4"></i>
            </div>
            <div>
              <h6 class="mb-0 text-muted">Origins</h6>
              <h3 class="mb-0">{{ products|map(p => p.origin)|filter(o => o)|unique|length }}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Include html2pdf.js from a CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

  <!-- Styles for print and PDF -->
  <style>
    @media print {
      .noPdf {
        display: none !important;
      }
    }

    /* Special styles for PDF output */
    .pdf-table {
      width: 100%;
      font-size: 12px; /* Larger font for better readability */
      table-layout: fixed;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    .pdf-table th {
      background-color: #4e73df !important;
      color: white !important;
      font-weight: bold;
      text-align: left;
      padding: 10px;
      border: 1px solid #e3e6f0;
      font-size: 13px; /* Slightly larger headers */
    }

    .pdf-table td {
      padding: 10px;
      border: 1px solid #e3e6f0;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      vertical-align: middle;
    }

    .pdf-table tr:nth-child(even) {
      background-color: #f8f9fc;
    }

    /* Ensure text is clearly visible */
    .pdf-table td, .pdf-table th {
      line-height: 1.4;
      letter-spacing: 0.01em;
    }

    /* Column widths optimized for A4 landscape */
    .pdf-col-name { width: 15%; }
    .pdf-col-img { width: 8%; }
    .pdf-col-desc { width: 25%; }
    .pdf-col-price { width: 8%; }
    .pdf-col-stock { width: 8%; }
    .pdf-col-category { width: 12%; }
    .pdf-col-origin { width: 12%; }
    .pdf-col-eco { width: 10%; }
    .pdf-col-actions { width: 0%; }

    /* Avatar Styles */
    .avatar {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      color: #fff;
      background-color: var(--primary-color);
      border-radius: 50%;
      overflow: hidden;
    }

    .avatar-sm {
      width: 32px;
      height: 32px;
      font-size: 0.875rem;
    }

    /* Stats Card Styles */
    .stats-card {
      border-radius: 0.75rem;
      transition: transform 0.3s ease;
    }

    .stats-card:hover {
      transform: translateY(-5px);
    }

    .stats-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Empty State Styles */
    .empty-state {
      padding: 2rem;
      text-align: center;
    }

    .empty-state-icon {
      font-size: 3rem;
      color: #d1d5db;
      margin-bottom: 1rem;
    }
  </style>

  <div class="row mb-4">
    <div class="col-md-8">
      <!-- Chart.js (Pie Chart) -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.4s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Products by Category</h5>
        </div>
        <div class="card-body">
          <canvas id="categoryPieChart" height="300"></canvas>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.5s">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 class="mb-0 fw-bold">Actions</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <button class="btn btn-primary btn-block w-100 py-2" id="downloadButton">
              <i class="ri-file-download-line me-1"></i> Download PDF Report
            </button>
            <a href="{{ path('app_product_new') }}" class="btn btn-success">
              <i class="ri-add-line me-1"></i> Add New Product
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Chart code
      let categoryCounts = {};
      {% for product in products %}
        var cat = "{{ product.categorie|e('js') }}";
        categoryCounts[cat] = (categoryCounts[cat] || 0) + 1;
      {% endfor %}
      const labels = Object.keys(categoryCounts);
      const data = Object.values(categoryCounts);
      const ctx = document.getElementById('categoryPieChart').getContext('2d');
      new Chart(ctx, {
        type: 'pie',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: ['#4e73df','#1cc88a','#36b9cc','#f6c23e','#e74a3b','#5a5c69','#858796']
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
              labels: {
                padding: 20,
                usePointStyle: true,
                pointStyle: 'circle'
              }
            }
          },
          cutout: '0%',
          borderWidth: 0
        }
      });

      // PDF download handler
      document.getElementById('downloadButton').addEventListener('click', function() {
        // Show loading indicator
        const loadingMessage = document.createElement('div');
        loadingMessage.innerHTML = '<div class="alert alert-info">Generating PDF, please wait...</div>';
        document.querySelector('.container-fluid').prepend(loadingMessage);

        // Create a PDF title and header
        const pdfHeader = document.createElement('div');
        pdfHeader.innerHTML = `
          <div style="text-align: center; margin-bottom: 25px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border-bottom: 3px solid #4e73df;">
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
              <div style="font-size: 24px; margin-right: 10px; color: #4e73df;">
                <i class="ri-shopping-bag-line"></i>
              </div>
              <h2 style="margin: 0; color: #4e73df; font-size: 24px; font-weight: 700;">Product Management Report</h2>
            </div>
            <p style="margin: 5px 0 0; color: #6c757d; font-size: 14px;">
              Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
            </p>
            <p style="margin: 5px 0 0; color: #6c757d; font-size: 14px;">
              eco.net - Product Management System
            </p>
          </div>
        `;

        // Create a clone of the content to avoid modifying the original
        const contentClone = document.getElementById('pdfContent').cloneNode(true);

        // Add the header to the clone
        contentClone.insertBefore(pdfHeader, contentClone.firstChild);

        // Remove action buttons from the clone
        const actionButtons = contentClone.querySelectorAll('.noPdf');
        actionButtons.forEach(el => {
          el.style.display = 'none';
        });

        // Apply PDF-specific class to the table for styling
        const table = contentClone.querySelector('table');
        table.classList.add('pdf-table');

        // Optimize images for PDF
        const images = contentClone.querySelectorAll('img');
        images.forEach(img => {
          // Set a better size for images in PDF
          img.style.maxWidth = '40px';
          img.style.maxHeight = '40px';
          img.style.width = '40px';
          img.style.height = '40px';
          img.style.objectFit = 'cover';
          img.style.borderRadius = '4px';
          img.style.border = '1px solid #e3e6f0';
          img.style.padding = '2px';
          img.style.backgroundColor = '#ffffff';

          // Add CORS attributes to help with image loading
          img.crossOrigin = 'Anonymous';

          // Create a backup text for images that might fail to load
          const parentCell = img.closest('td');
          if (parentCell) {
            const productName = parentCell.closest('tr').querySelector('td:first-child').textContent.trim();
            img.setAttribute('alt', 'Image: ' + productName);
            img.setAttribute('title', productName);

            // Add a wrapper div for better image display
            const wrapper = document.createElement('div');
            wrapper.style.display = 'flex';
            wrapper.style.justifyContent = 'center';
            wrapper.style.alignItems = 'center';
            wrapper.style.height = '100%';
            img.parentNode.insertBefore(wrapper, img);
            wrapper.appendChild(img);
          }
        });

        // Convert badges to simple text for better PDF rendering
        const badges = contentClone.querySelectorAll('.badge');
        badges.forEach(badge => {
          const text = badge.textContent.trim();
          const color = window.getComputedStyle(badge).backgroundColor;
          badge.innerHTML = text;
          badge.style.backgroundColor = 'transparent';
          badge.style.color = color;
          badge.style.padding = '0';
          badge.style.fontWeight = 'bold';
        });

        // Add statistics to the PDF
        const statsSection = document.createElement('div');
        statsSection.style.marginBottom = '20px';
        statsSection.style.padding = '10px';
        statsSection.style.backgroundColor = '#f8f9fa';
        statsSection.style.borderRadius = '5px';

        statsSection.innerHTML = `
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div style="text-align: center; padding: 15px; background-color: #e8f4fc; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #4e73df; font-size: 16px; font-weight: 600;">Total Products</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(1) h3').textContent}</p>
            </div>
            <div style="text-align: center; padding: 15px; background-color: #e8fcf8; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #1cc88a; font-size: 16px; font-weight: 600;">Ecological Products</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(2) h3').textContent}</p>
            </div>
            <div style="text-align: center; padding: 15px; background-color: #fff8e8; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #f6c23e; font-size: 16px; font-weight: 600;">Categories</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(3) h3').textContent}</p>
            </div>
            <div style="text-align: center; padding: 15px; background-color: #e8f8fc; border-radius: 8px; width: 23%; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
              <h4 style="margin: 0; color: #36b9cc; font-size: 16px; font-weight: 600;">Origins</h4>
              <p style="font-size: 28px; font-weight: bold; margin: 8px 0 0;">${document.querySelector('.col-md-3:nth-child(4) h3').textContent}</p>
            </div>
          </div>
        `;

        contentClone.insertBefore(statsSection, contentClone.querySelector('.table-responsive'));

        // html2pdf configuration with landscape orientation and margins
        const opt = {
          margin: [15, 10, 15, 10],
          filename: 'product_management_report.pdf',
          image: { type: 'jpeg', quality: 1.0 },
          html2canvas: {
            scale: 3, // Higher scale for better quality
            useCORS: true,
            allowTaint: true, // Allow tainted canvas for better image handling
            logging: false, // Disable logging for better performance
            letterRendering: true,
            imageTimeout: 5000, // Longer timeout for images
            backgroundColor: '#ffffff' // Ensure white background
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'landscape',
            compress: true,
            precision: 16 // Higher precision for better text rendering
          }
        };

        // Generate the PDF
        if (typeof html2pdf !== 'undefined') {
          html2pdf().set(opt).from(contentClone).save().then(() => {
            // Remove the loading message
            loadingMessage.remove();
          }).catch(err => {
            console.error('PDF generation error:', err);
            loadingMessage.innerHTML = '<div class="alert alert-danger">PDF generation failed. Please try again.</div>';
            setTimeout(() => loadingMessage.remove(), 3000);
          });
        } else {
          console.error('html2pdf library not loaded');
          loadingMessage.innerHTML = '<div class="alert alert-danger">PDF library not loaded. Please refresh and try again.</div>';
          setTimeout(() => loadingMessage.remove(), 3000);
        }
      });
    });
  </script>

  <!-- Search and Filter Section -->
  <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.6s">
    <div class="card-header bg-white py-3">
      <h5 class="mb-0 fw-bold">Product List</h5>
    </div>
    <div class="card-body">
      <div class="row mb-4">
        <div class="col-md-8">
          <form method="get" class="mb-0">
            <div class="input-group">
              <span class="input-group-text bg-light border-end-0">
                <i class="ri-search-line"></i>
              </span>
              <input type="text" name="search" value="{{ app.request.query.get('search') }}" class="form-control border-start-0" placeholder="Search by name, category or origin...">
              <button type="submit" class="btn btn-primary">
                <i class="ri-search-line me-1"></i> Search
              </button>
            </div>
          </form>
        </div>
        <div class="col-md-4 d-flex justify-content-end align-items-center mt-3 mt-md-0">
          <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="ri-filter-3-line me-1"></i> Filter
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><h6 class="dropdown-header">Filter by Category</h6></li>
              {% set categories = products|map(p => p.categorie)|filter(c => c)|unique %}
              {% for category in categories %}
                <li><a class="dropdown-item" href="?category={{ category }}">{{ category }}</a></li>
              {% endfor %}
              <li><hr class="dropdown-divider"></li>
              <li><h6 class="dropdown-header">Filter by Origin</h6></li>
              {% set origins = products|map(p => p.origin)|filter(o => o)|unique %}
              {% for origin in origins %}
                <li><a class="dropdown-item" href="?origin={{ origin }}">{{ origin }}</a></li>
              {% endfor %}
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="?ecological=1">Ecological Only</a></li>
              <li><a class="dropdown-item" href="{{ path('app_product_index') }}">Clear Filters</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- The content we want to include in the PDF -->
      <div id="pdfContent">
        <div class="table-responsive">
          <table class="table table-hover align-middle mb-0">
            <thead class="table-light">
              <tr>
                <th scope="col" class="pdf-col-name">Product Name</th>
                <th scope="col" class="pdf-col-img">Image</th>
                <th scope="col" class="pdf-col-desc">Description</th>
                <th scope="col" class="pdf-col-price">Price</th>
                <th scope="col" class="pdf-col-stock">Stock</th>
                <th scope="col" class="pdf-col-category">Category</th>
                <th scope="col" class="pdf-col-origin">Origin</th>
                <th scope="col" class="pdf-col-eco">Ecological</th>
                <th scope="col" class="pdf-col-actions noPdf text-end">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for product in products %}
                <tr class="align-middle">
                  <td>
                    <div class="fw-semibold">{{ product.nomP }}</div>
                  </td>
                  <td>
                    {% if product.image is not empty %}
                      <div class="product-img-wrapper rounded overflow-hidden" style="width: 40px; height: 40px;">
                        <img src="http://localhost/img/{{ product.image }}"
                             alt="{{ product.nomP }}"
                             class="img-fluid"
                             style="width: 100%; height: 100%; object-fit: cover;">
                      </div>
                    {% else %}
                      <div class="avatar avatar-sm bg-light text-secondary">
                        <i class="ri-image-line"></i>
                      </div>
                    {% endif %}
                  </td>
                  <td>
                    <div class="text-truncate" style="max-width: 200px;" title="{{ product.description }}">
                      {{ product.description|length > 50 ? product.description|slice(0, 50) ~ '...' : product.description }}
                    </div>
                  </td>
                  <td>
                    <span class="fw-semibold">{{ product.price }}</span>
                  </td>
                  <td>
                    {% if product.stock > 10 %}
                      <span class="badge bg-success rounded-pill">{{ product.stock }}</span>
                    {% elseif product.stock > 0 %}
                      <span class="badge bg-warning rounded-pill">{{ product.stock }}</span>
                    {% else %}
                      <span class="badge bg-danger rounded-pill">Out of stock</span>
                    {% endif %}
                  </td>
                  <td>
                    <span class="badge bg-primary-subtle text-primary rounded-pill">{{ product.categorie }}</span>
                  </td>
                  <td>
                    <span class="badge bg-info-subtle text-info rounded-pill">{{ product.origin }}</span>
                  </td>
                  <td>
                    {% if product.isEcological %}
                      <span class="badge bg-success rounded-pill">
                        <i class="ri-check-line me-1"></i> Yes
                      </span>
                    {% else %}
                      <span class="badge bg-danger rounded-pill">
                        <i class="ri-close-line me-1"></i> No
                      </span>
                    {% endif %}
                  </td>
                  <td class="noPdf text-end">
                    <div class="d-flex justify-content-end gap-2">
                      <a href="{{ path('app_product_show', {'id': product.id}) }}"
                         class="btn btn-sm btn-outline-primary"
                         data-bs-toggle="tooltip"
                         data-bs-placement="top"
                         title="View Details">
                        <i class="ri-eye-line"></i>
                      </a>
                      <a href="{{ path('app_product_edit', {'id': product.id}) }}"
                         class="btn btn-sm btn-outline-secondary"
                         data-bs-toggle="tooltip"
                         data-bs-placement="top"
                         title="Edit Product">
                        <i class="ri-pencil-line"></i>
                      </a>
                      <form method="post"
                            action="{{ path('app_product_delete', {'id': product.id}) }}"
                            class="d-inline"
                            onsubmit="return confirm('Are you sure you want to delete this product? This action cannot be undone.');">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ product.id) }}">
                        <button type="submit"
                                class="btn btn-sm btn-outline-danger"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Delete Product">
                          <i class="ri-delete-bin-line"></i>
                        </button>
                      </form>
                    </div>
                  </td>
                </tr>
              {% else %}
                <tr>
                  <td colspan="9" class="text-center py-5">
                    <div class="empty-state">
                      <i class="ri-shopping-bag-line empty-state-icon"></i>
                      <h5>No products found</h5>
                      <p class="text-muted">Try adjusting your search criteria or add a new product</p>
                      <a href="{{ path('app_product_new') }}" class="btn btn-primary mt-3">
                        <i class="ri-add-line me-1"></i> Add New Product
                      </a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add animation to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
      card.classList.add('animate__animated', 'animate__fadeIn');
      card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add hover effect to table rows
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
      row.addEventListener('mouseenter', function() {
        this.style.cursor = 'pointer';
      });
    });
  });
</script>
{% endblock %}