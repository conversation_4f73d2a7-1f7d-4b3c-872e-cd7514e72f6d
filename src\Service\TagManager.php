<?php

namespace App\Service;

use Symfony\Component\Filesystem\Filesystem;

class TagManager
{
    private string $tagsFilePath;
    private Filesystem $filesystem;

    public function __construct(string $projectDir, Filesystem $filesystem)
    {
        $this->tagsFilePath = $projectDir . '/public/data/forum_tags.json';
        $this->filesystem = $filesystem;

        // Create the directory if it doesn't exist
        $dir = dirname($this->tagsFilePath);
        if (!$this->filesystem->exists($dir)) {
            $this->filesystem->mkdir($dir);
        }

        // Create the file if it doesn't exist
        if (!$this->filesystem->exists($this->tagsFilePath)) {
            $this->filesystem->dumpFile($this->tagsFilePath, json_encode(['tags' => []]));
        }
    }

    /**
     * Extract hashtags from text
     */
    public function extractTags(string $text): array
    {
        preg_match_all('/#(\w+)/', $text, $matches);
        return $matches[1] ?? [];
    }

    /**
     * Save tags to JSON file
     */
    public function saveTags(array $newTags): void
    {
        if (empty($newTags)) {
            return;
        }

        $content = $this->readTagsFile();
        $existingTags = $content['tags'] ?? [];

        // Merge and deduplicate tags
        $allTags = array_unique(array_merge($existingTags, $newTags));

        // Sort tags alphabetically
        sort($allTags);

        // Save back to file
        $this->filesystem->dumpFile(
            $this->tagsFilePath,
            json_encode(['tags' => $allTags], JSON_PRETTY_PRINT)
        );
    }

    /**
     * Get all tags from the JSON file
     */
    public function getAllTags(): array
    {
        $content = $this->readTagsFile();
        return $content['tags'] ?? [];
    }

    /**
     * Read the tags file
     */
    private function readTagsFile(): array
    {
        if (!$this->filesystem->exists($this->tagsFilePath)) {
            return ['tags' => []];
        }

        $content = file_get_contents($this->tagsFilePath);
        return json_decode($content, true) ?: ['tags' => []];
    }
}
