<?php

namespace App\Controller;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

final class CameraController extends AbstractController{
    #[Route('/face-id/capture', name: 'app_camera')]
    public function index(): Response
    {
        return $this->render('face_detection/capture.html.twig', [
            'controller_name' => 'CameraController',
        ]);
    }

    #[Route('/save-image', name: 'save_image', methods: ['POST'])]
    public function saveImage(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw new AccessDeniedException('You must be logged in to use Face ID.');
        }

        $data = json_decode($request->getContent(), true);
        $imageData = $data['image']; // Base64 image data

        // Remove the "data:image/png;base64," part
        $imageData = str_replace('data:image/png;base64,', '', $imageData);
        $imageData = base64_decode($imageData);

        // Define the file name using user ID
        $fileName = $user->getId() . '.png';
        $uploadDir = 'C:\xampp\htdocs\img\faces\\';
        $filePath = $uploadDir . $fileName;

        // Create the directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        // Save the image to the file
        file_put_contents($filePath, $imageData);

        // Update user's face_id field
        $user->setFaceId($fileName);
        $entityManager->persist($user);
        $entityManager->flush();

        return $this->json([
            'message' => 'Face ID registered successfully!',
            'redirect' => $this->generateUrl('back_profile_edit')
        ]);
    }

    #[Route('/disable-face-id', name: 'disable_face_id', methods: ['POST'])]
    public function disableFaceId(EntityManagerInterface $entityManager, Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw new AccessDeniedException('You must be logged in to manage Face ID.');
        }

        // Get the current face ID file name
        $faceId = $user->getFaceId();
        if ($faceId) {
            // Delete the face ID image file
            $filePath = 'C:\xampp\htdocs\img\faces\\' . $faceId;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Clear the face_id field
            $user->setFaceId(null);
            $entityManager->persist($user);
            $entityManager->flush();

            $this->addFlash('success', 'Face ID disabled successfully!');
        }

        // Always redirect, which is what Turbo expects
        return $this->redirectToRoute('back_profile_edit');
    }
}
