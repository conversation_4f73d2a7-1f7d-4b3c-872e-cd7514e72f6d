{% extends 'front/base.html.twig' %}

{% block title %}Question {{ questionNumber }} - {{ challenge.name }}{% endblock %}

{% block content %}
    <!-- Navbar & Hero Start -->
    <div class="container-fluid position-relative p-0">
        {% include 'front/includes/navbar.html.twig' %}

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb-challenges">
            <div class="container text-center py-5" style="max-width: 900px">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
                    Challenge - {{ challenge.name }}
                </h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item">
                        <a class="text-white" href="{{path('app_home')}}">Home</a>
                    </li>
                    <li class="breadcrumb-item active text-white-50">Pages</li>
                    <li class="breadcrumb-item active text-primary">{{ challenge.name }}</li>
                </ol>
            </div>
        </div>
        <!-- Header End -->
    </div>
    <!-- Navbar & Hero End -->

    <div class="container-xxl py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <!-- Progress Section -->
                    <div class="text-center mb-4 wow fadeInUp" data-wow-delay="0.1s">
                        <div class="position-relative mb-3">
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" 
                                     role="progressbar" 
                                     style="width: {{ (questionNumber / totalQuestions) * 100 }}%"
                                     aria-valuenow="{{ (questionNumber / totalQuestions) * 100 }}"
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                    <strong>Question {{ questionNumber }}/{{ totalQuestions }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Card -->
                    <div class="wow fadeInUp" data-wow-delay="0.3s">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-primary py-3">
                                <h4 class="text-white mb-0 text-center">{{ quiz.question }}</h4>
                            </div>
                            <div class="card-body p-4">
                                <form action="{{ path('app_challenge_submit_answer', {'id': challenge.id}) }}" method="POST" id="quizForm" novalidate>
                                    <input type="hidden" name="quiz_id" value="{{ quiz.id }}">
                                    
                                    <div id="validationMessage" class="alert alert-danger d-none fade show mb-4" role="alert">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            Please select an answer before submitting.
                                        </div>
                                    </div>

                                    <div class="choices-grid">
                                        {% for letter, choice in {
                                            'A': quiz.choice1,
                                            'B': quiz.choice2,
                                            'C': quiz.choice3,
                                            'D': quiz.choice4
                                        } %}
                                            <div class="choice-item mb-3">
                                                <input type="radio" class="btn-check" name="answer" id="choice{{ letter }}" value="{{ letter }}" required>
                                                <label class="btn btn-outline-primary w-100 p-3 d-flex align-items-center" for="choice{{ letter }}">
                                                    <span class="choice-letter me-3">{{ letter }}</span>
                                                    <span class="choice-text">{{ choice }}</span>
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>

                                    <div class="text-center mt-4">
                                        <a href="{{ path('app_challenges_list') }}" 
                                           class="btn btn-light btn-lg px-5 py-3 me-3 wow fadeInUp" 
                                           data-wow-delay="0.5s"
                                           onclick="return confirm('Exiting will lose your current progress. Are you sure?')">
                                            <i class="fas fa-times me-2"></i>
                                            Exit Quiz
                                        </a>
                                        <button type="submit" class="btn btn-primary btn-lg px-5 py-3 wow fadeInUp" data-wow-delay="0.5s">
                                            Submit Answer
                                            <i class="fa fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </form>

                                <script>
                                    document.getElementById('quizForm').addEventListener('submit', function(e) {
                                        const radioButtons = this.querySelectorAll('input[type="radio"]');
                                        const validationMessage = document.getElementById('validationMessage');
                                        let isChecked = false;

                                        radioButtons.forEach(radio => {
                                            if (radio.checked) isChecked = true;
                                        });

                                        if (!isChecked) {
                                            e.preventDefault();
                                            validationMessage.classList.remove('d-none');
                                            validationMessage.classList.add('animate__animated', 'animate__shakeX');
                                            setTimeout(() => {
                                                validationMessage.classList.remove('animate__animated', 'animate__shakeX');
                                            }, 1000);
                                        } else {
                                            validationMessage.classList.add('d-none');
                                        }
                                    });

                                    document.querySelectorAll('input[type="radio"]').forEach(radio => {
                                        radio.addEventListener('change', function() {
                                            document.getElementById('validationMessage').classList.add('d-none');
                                        });
                                    });
                                </script>
                            </div>
                        </div>

                        <!-- Progress Stats -->
                        <div class="card border-0 shadow-sm mt-4 wow fadeInUp" data-wow-delay="0.7s">
                            <div class="card-body p-4">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h5 class="fw-bold text-primary mb-1">{{ progress.progressnb }}</h5>
                                        <p class="text-muted mb-0">Questions Answered</p>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="fw-bold text-primary mb-1">{{ progress.score }}</h5>
                                        <p class="text-muted mb-0">Current Score</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        #validationMessage {
            transition: all 0.3s ease-in-out;
            border-left: 4px solid #dc3545;
        }

        .choice-item .btn {
            text-align: left;
            border-width: 2px;
            transition: all 0.3s ease;
        }
        
        .choice-item .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .choice-letter {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: rgba(var(--bs-primary-rgb), 0.1);
            color: var(--bs-primary);
            font-weight: bold;
        }

        .btn-check:checked + .btn .choice-letter {
            background-color: var(--bs-white);
            color: var(--bs-primary);
        }

        .choices-grid {
            display: grid;
            gap: 1rem;
        }

        @media (min-width: 768px) {
            .choices-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
{% endblock %}
