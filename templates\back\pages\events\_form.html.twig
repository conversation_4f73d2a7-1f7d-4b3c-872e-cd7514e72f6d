{{ form_start(form) }}
    <div class="row">
        <div class="col-md-6 mb-3">
            {{ form_label(form.title) }}
            {{ form_widget(form.title) }}
            {% if form_errors(form.title) %}
                <div class="invalid-feedback d-block">
                    {{ form_errors(form.title) }}
                </div>
            {% endif %}
        </div>

        <div class="col-md-6 mb-3">
            {{ form_label(form.date) }}
            {{ form_widget(form.date) }}
            {% if form_errors(form.date) %}
                <div class="invalid-feedback d-block">
                    {{ form_errors(form.date) }}
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mb-3">
        {{ form_label(form.location) }}
        {{ form_widget(form.location) }}
        {% if form_errors(form.location) %}
            <div class="invalid-feedback d-block">
                {{ form_errors(form.location) }}
            </div>
        {% endif %}
    </div>

    <div class="mb-3">
        {{ form_label(form.maxParticipants) }}
        {{ form_widget(form.maxParticipants) }}
        {% if form_errors(form.maxParticipants) %}
            <div class="invalid-feedback d-block">
                {{ form_errors(form.maxParticipants) }}
            </div>
        {% endif %}
    </div>

    <div class="mb-3">
        {{ form_label(form.description) }}
        {{ form_widget(form.description) }}
        {% if form_errors(form.description) %}
            <div class="invalid-feedback d-block">
                {{ form_errors(form.description) }}
            </div>
        {% endif %}
    </div>

    <button class="btn btn-primary">{{ button_label|default('Enregistrer') }}</button>
{{ form_end(form) }} 