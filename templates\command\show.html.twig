{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col-auto">
      <a href="{{ path('app_command_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Orders">
        <i class="ri-arrow-left-line"></i>
      </a>
    </div>
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Order Details</h1>
      <p class="text-muted mb-0">View detailed information about this order</p>
    </div>
    <div class="col-auto">
      <div class="dropdown">
        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
          <i class="ri-settings-3-line me-1"></i> Actions
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
          <li>
            <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post">
              <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
              <input type="hidden" name="status" value="completed">
              <button type="submit" class="dropdown-item">
                <i class="ri-check-line me-2 text-success"></i> Mark as Completed
              </button>
            </form>
          </li>
          <li>
            <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post">
              <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
              <input type="hidden" name="status" value="cancelled">
              <button type="submit" class="dropdown-item">
                <i class="ri-close-line me-2 text-danger"></i> Cancel Order
              </button>
            </form>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <!-- Order Details Card -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
          <h5 class="mb-0 fw-bold">Order Information</h5>
          <span class="status-badge status-{{ command.status }}">{{ command.status }}</span>
        </div>
        <div class="card-body">
          <div class="row g-4">
            <div class="col-md-6">
              <div class="mb-4">
                <h6 class="text-muted fw-semibold mb-2">Order Reference</h6>
                <p class="mb-0 fs-5">Order #{{ command.createAt ? command.createAt|date('Ymd') : '' }}-{{ command.id }}</p>
              </div>
              <div class="mb-4">
                <h6 class="text-muted fw-semibold mb-2">Created At</h6>
                <p class="mb-0">{{ command.createAt ? command.createAt|date('Y-m-d H:i:s') : '' }}</p>
              </div>
              <div class="mb-4">
                <h6 class="text-muted fw-semibold mb-2">Total Amount</h6>
                <p class="mb-0 fs-5 fw-bold text-primary">{{ command.totalAmount }} DT</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-4">
                <h6 class="text-muted fw-semibold mb-2">Delivery Address</h6>
                <p class="mb-0">{{ command.deliveryAddress }}</p>
              </div>
              <div class="mb-4">
                <h6 class="text-muted fw-semibold mb-2">Notes</h6>
                <p class="mb-0">{{ command.notes }}</p>
              </div>
              <div class="mb-4">
                <h6 class="text-muted fw-semibold mb-2">Customer</h6>
                <p class="mb-0">{{ userName }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Products Card -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Ordered Products</h5>
        </div>
        <div class="card-body">
          {% if command.products|length > 0 %}
            <div class="table-responsive">
              <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                  <tr>
                    <th scope="col">Product</th>
                    <th scope="col">Category</th>
                    <th scope="col">Price</th>
                    <th scope="col">Ecological</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in command.products %}
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          {% if product.image is defined and product.image is not empty %}
                            <div class="product-img-wrapper rounded overflow-hidden me-3" style="width: 40px; height: 40px;">
                              <img src="http://localhost/img/{{ product.image }}"
                                   alt="{{ product.nomP }}"
                                   class="img-fluid"
                                   style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                          {% else %}
                            <div class="avatar avatar-sm bg-light text-secondary me-3">
                              <i class="ri-image-line"></i>
                            </div>
                          {% endif %}
                          <div>
                            <div class="fw-semibold">{{ product.nomP }}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-primary-subtle text-primary rounded-pill">{{ product.categorie }}</span>
                      </td>
                      <td>
                        <span class="fw-semibold">{{ product.price }} DT</span>
                      </td>
                      <td>
                        {% if product.isEcological %}
                          <span class="badge bg-success rounded-pill">
                            <i class="ri-check-line me-1"></i> Yes
                          </span>
                        {% else %}
                          <span class="badge bg-danger rounded-pill">
                            <i class="ri-close-line me-1"></i> No
                          </span>
                        {% endif %}
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="empty-state">
              <i class="ri-shopping-bag-line empty-state-icon"></i>
              <h5>No products found</h5>
              <p class="text-muted">This order doesn't have any products attached</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Order Status Card -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Order Status</h5>
        </div>
        <div class="card-body">
          <div class="mb-4">
            <h6 class="text-muted fw-semibold mb-3">Current Status</h6>
            <div class="d-flex align-items-center">
              {% if command.status == 'pending' %}
                <div class="stats-icon rounded-3 p-3 me-3" style="background-color: rgba(13, 110, 253, 0.1);">
                  <i class="ri-time-line fs-4" style="color: #0d6efd;"></i>
                </div>
                <div>
                  <h5 class="mb-0" style="color: #0d6efd;">Pending</h5>
                  <p class="text-muted mb-0">Order is awaiting processing</p>
                </div>
              {% elseif command.status == 'processing' %}
                <div class="stats-icon rounded-3 p-3 me-3" style="background-color: rgba(253, 126, 20, 0.1);">
                  <i class="ri-loader-4-line fs-4" style="color: #fd7e14;"></i>
                </div>
                <div>
                  <h5 class="mb-0" style="color: #fd7e14;">Processing</h5>
                  <p class="text-muted mb-0">Order is being processed</p>
                </div>
              {% elseif command.status == 'completed' %}
                <div class="stats-icon rounded-3 p-3 me-3" style="background-color: rgba(25, 135, 84, 0.1);">
                  <i class="ri-check-line fs-4" style="color: #198754;"></i>
                </div>
                <div>
                  <h5 class="mb-0" style="color: #198754;">Completed</h5>
                  <p class="text-muted mb-0">Order has been completed</p>
                </div>
              {% elseif command.status == 'cancelled' %}
                <div class="stats-icon rounded-3 p-3 me-3" style="background-color: rgba(220, 53, 69, 0.1);">
                  <i class="ri-close-line fs-4" style="color: #dc3545;"></i>
                </div>
                <div>
                  <h5 class="mb-0" style="color: #dc3545;">Cancelled</h5>
                  <p class="text-muted mb-0">Order has been cancelled</p>
                </div>
              {% endif %}
            </div>
          </div>

          <h6 class="text-muted fw-semibold mb-3">Update Status</h6>
          <form action="{{ path('app_command_update_status', {'id': command.id}) }}" method="post" class="mb-3">
            <input type="hidden" name="_token" value="{{ csrf_token('edit' ~ command.id) }}">
            <div class="mb-3">
              <select name="status" class="form-select">
                <option value="pending" {% if command.status == 'pending' %}selected{% endif %}>Pending</option>
                <option value="processing" {% if command.status == 'processing' %}selected{% endif %}>Processing</option>
                <option value="completed" {% if command.status == 'completed' %}selected{% endif %}>Completed</option>
                <option value="cancelled" {% if command.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
              </select>
            </div>
            <button type="submit" class="btn btn-primary w-100">
              <i class="ri-refresh-line me-1"></i> Update Status
            </button>
          </form>
        </div>
      </div>

      <!-- Customer Info Card -->
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.3s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Customer Information</h5>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-4">
            <div class="avatar me-3 bg-primary-subtle text-primary">
              <i class="ri-user-line"></i>
            </div>
            <div>
              <h6 class="mb-0">{{ userName }}</h6>
              <small class="text-muted">Customer</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Status Badge Styles */
  .status-badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    border-radius: 0.25rem;
    text-transform: capitalize;
  }

  .status-pending {
    background-color: #0d6efd;
    color: #fff;
  }

  .status-processing {
    background-color: #fd7e14;
    color: #fff;
  }

  .status-completed {
    background-color: #198754;
    color: #fff;
  }

  .status-cancelled {
    background-color: #dc3545;
    color: #fff;
  }

  /* Avatar Styles */
  .avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: #fff;
    background-color: var(--primary-color);
    border-radius: 50%;
    overflow: hidden;
  }

  .avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }

  /* Stats Icon */
  .stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Button Icon */
  .btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Empty State Styles */
  .empty-state {
    padding: 2rem;
    text-align: center;
  }

  .empty-state-icon {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
{% endblock %}
