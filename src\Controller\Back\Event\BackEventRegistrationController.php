<?php

namespace App\Controller\Back\Event;

use App\Entity\Event;
use App\Entity\EventRegistration;
use App\Repository\EventRegistrationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/event-registrations')]
#[IsGranted('ROLE_ADMIN')]
class BackEventRegistrationController extends AbstractController
{
    #[Route('/', name: 'app_admin_event_registration_index', methods: ['GET'])]
    public function index(EventRegistrationRepository $registrationRepository): Response
    {
        $registrations = $registrationRepository->findAllWithUserDetails();
        $registrationCounts = $registrationRepository->getRegistrationCountsByEvent();

        return $this->render('back/pages/event_registrations/index.html.twig', [
            'registrations' => $registrations,
            'registrationCounts' => $registrationCounts
        ]);
    }

    #[Route('/event/{id}', name: 'app_admin_event_registration_by_event', methods: ['GET'])]
    public function showEventRegistrations(Event $event, EventRegistrationRepository $registrationRepository): Response
    {
        $registrations = $registrationRepository->findByEventWithUserDetails($event->getId());

        return $this->render('back/pages/event_registrations/by_event.html.twig', [
            'event' => $event,
            'registrations' => $registrations
        ]);
    }
}