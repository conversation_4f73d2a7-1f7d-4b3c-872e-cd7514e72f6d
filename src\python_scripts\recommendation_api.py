from flask import Flask, jsonify
import mysql.connector
import pandas as pd
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from flask_cors import CORS
app = Flask(__name__)
CORS(app)

def get_recommendations(user_id, n_recommendations=5):
    try:
        conn = mysql.connector.connect(
            host="127.0.0.1",
            user="root",
            password="",
            database="econet"
        )
        if not conn.is_connected():
            return []
            
    except Exception as e:
        return []
    cursor = conn.cursor(dictionary=True)
    cursor.execute("""
        SELECT p.* FROM product p
        JOIN command_product cp ON p.id = cp.product_id
        JOIN command c ON cp.command_id = c.id
        WHERE c.id_user = %s AND c.status != 'cancelled'
    """, (user_id,))
    user_purchased_products = cursor.fetchall()
    user_purchased_ids = [product['id'] for product in user_purchased_products]
    if not user_purchased_products:
        cursor.execute("""
            SELECT p.* FROM product p
            JOIN command_product cp ON p.id = cp.product_id
            JOIN command c ON cp.command_id = c.id
            WHERE c.id_user = %s
        """, (user_id,))
        all_user_interactions = cursor.fetchall()
        is_ecological = 1
        if all_user_interactions:
            ecological_count = sum(1 for product in all_user_interactions if product['is_ecological'] == 1)
            non_ecological_count = len(all_user_interactions) - ecological_count
            is_ecological = 1 if ecological_count >= non_ecological_count else 0
        cursor.execute("""
            SELECT p.* FROM product p
            LEFT JOIN command_product cp ON p.id = cp.product_id
            LEFT JOIN command c ON cp.command_id = c.id
            WHERE p.is_ecological = %s
            GROUP BY p.id
            ORDER BY COUNT(cp.product_id) DESC, p.stock DESC
            LIMIT %s
        """, (is_ecological, n_recommendations))
        popular_products = cursor.fetchall()
        conn.close()
        return popular_products
    cursor.execute("SELECT * FROM product")
    all_products = cursor.fetchall()
    products_df = pd.DataFrame(all_products)
    user_profile = {}
    categories = [p['categorie'] for p in user_purchased_products]
    origins = [p['origin'] for p in user_purchased_products]
    is_ecological_count = sum(1 for p in user_purchased_products if p['is_ecological'] == 1)
    for c in set(categories):
        user_profile[f'category_{c}'] = categories.count(c) / len(categories)
    for o in set(origins):
        user_profile[f'origin_{o}'] = origins.count(o) / len(origins)
    user_profile['is_ecological'] = is_ecological_count / len(user_purchased_products)
    product_profiles = []
    for _, product in products_df.iterrows():
        profile = {}
        for cat in set(products_df['categorie']):
            profile[f'category_{cat}'] = 1 if product['categorie'] == cat else 0
        for org in set(products_df['origin']):
            profile[f'origin_{org}'] = 1 if product['origin'] == org else 0
        profile['is_ecological'] = 1 if product['is_ecological'] == 1 else 0
        product_profiles.append(profile)
    all_features = set()
    for profile in product_profiles:
        all_features.update(profile.keys())
    for feature in all_features:
        if feature not in user_profile:
            user_profile[feature] = 0
    for profile in product_profiles:
        for feature in all_features:
            if feature not in profile:
                profile[feature] = 0
    user_vector = np.array([user_profile[f] for f in sorted(all_features)]).reshape(1, -1)
    product_vectors = np.array([[profile[f] for f in sorted(all_features)] for profile in product_profiles])
    similarities = cosine_similarity(user_vector, product_vectors).flatten()
    similarity_df = pd.DataFrame({
        'product_id': products_df['id'],
        'similarity': similarities
    })
    similarity_df = similarity_df[~similarity_df['product_id'].isin(user_purchased_ids)]
    similarity_df = similarity_df.sort_values('similarity', ascending=False)
    top_ids = similarity_df.head(n_recommendations)['product_id'].tolist()
    recommended_products = []
    for pid in top_ids:
        cursor.execute("SELECT * FROM product WHERE id = %s", (pid,))
        prod = cursor.fetchone()
        if prod:
            recommended_products.append(prod)
    conn.close()
    return recommended_products

@app.route('/recommendations/<int:user_id>', methods=['GET'])
def recommendations_route(user_id):
    recs = get_recommendations(user_id, 5)
    if not recs:
        return jsonify({"message": "No recommendations found"}), 404
    return jsonify({"recommendations": recs}), 200

if __name__ == "__main__":
    app.run(debug=True)
