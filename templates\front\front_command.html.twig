{% extends 'front/base.html.twig' %}

{% block content %}
<!-- Nav<PERSON> & Hero Start -->
<div class="container-fluid position-relative p-0">
    {% include 'front/includes/navbar.html.twig' %}
    <div class="container-fluid bg-breadcrumb-products">
        <div class="container text-center py-5" style="max-width: 900px">
            <h4 class="text-white display-4 mb-4">My Commands</h4>
            <ol class="breadcrumb d-flex justify-content-center mb-0">
                <li class="breadcrumb-item"><a class="text-white" href="{{ path('app_home') }}">Home</a></li>
                <li class="breadcrumb-item active text-primary">My Commands</li>
            </ol>
        </div>
    </div>
</div>
<!-- Navbar & Hero End -->

<div class="container mt-5">
    <h1 class="mb-4 text-center">Commands for User {{ username }}</h1>
    
    {% if commands|length > 0 %}
        <div class="accordion" id="commandsAccordion">
            {% for command in commands %}
                <div class="accordion-item mb-3">
                    <h2 class="accordion-header" id="heading{{ command.id }}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ command.id }}" aria-expanded="false" aria-controls="collapse{{ command.id }}">
                            Command #{{ command.id }} - {{ command.status }} - Total: {{ command.totalAmount }} DT
                        </button>
                    </h2>
                    <div id="collapse{{ command.id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ command.id }}" data-bs-parent="#commandsAccordion">
                        <div class="accordion-body">
                            <p><strong>Created At:</strong> {{ command.createAt ? command.createAt|date('Y-m-d H:i:s') : '' }}</p>
                            <p><strong>Delivery Address:</strong> {{ command.deliveryAddress }}</p>
                            <p><strong>Notes:</strong> {{ command.notes }}</p>

                            {% if command.products|length > 0 %}
                                <h5>Products in this Command:</h5>
                                <ul class="list-group mb-3">
                                    {% for product in command.products %}
                                        <li class="list-group-item">
                                            <div class="row align-items-center">
                                                <div class="col-md-2">
                                                    {% if product.image is not empty %}
                                                        <img src="{{ asset('uploads/products/' ~ product.image) }}" alt="{{ product.nomP }}" class="img-fluid">
                                                    {% else %}
                                                        N/A
                                                    {% endif %}
                                                </div>
                                                <div class="col-md-8">
                                                    <h6>{{ product.nomP }}</h6>
                                                    <p>{{ product.description }}</p>
                                                </div>
                                                <div class="col-md-2">
                                                    <p><strong>Price:</strong> {{ product.price }} DT</p>
                                                    <p><strong>Stock:</strong> {{ product.stock }}</p>
                                                </div>
                                            </div>
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <p>No products found for this command.</p>
                            {% endif %}

                            {% if command.status == 'pending' %}
                                <form method="post" action="{{ path('app_command_update_status2', {'id': command.id}) }}">
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-danger btn-sm">Cancel</button>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <p class="text-center">No commands found.</p>
    {% endif %}
</div>
{% endblock %}
