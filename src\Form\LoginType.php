<?php

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use App\Dto\LoginDTO;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Email;


class LoginType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('email', EmailType::class, [
                'attr' => [
                    'placeholder' => '<EMAIL>',
                    'class' => 'form-control',
                    'novalidate' => true
                ],
                'label' => 'Email address',
                'constraints' => [
                    new NotBlank(),
                    new Email(),
                ],
            ])
            ->add('password', PasswordType::class, [
                'attr' => [
                    'placeholder' => 'Password',
                    'class' => 'form-control',
                    'novalidate' => true
                ],
                'label' => 'Password',
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('remember_me', CheckboxType::class, [
                'required' => false,
                // ... other options
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LoginDTO::class,
            'csrf_token_id' => 'authenticate', // Ensure CSRF token matches security.yaml
            'validation_groups' => ['user_login'],
        ]);
    }

    public function getBlockPrefix(): string
    {
        return 'login';
    }
} 