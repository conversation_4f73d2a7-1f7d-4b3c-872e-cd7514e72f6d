{% extends 'front/base.html.twig' %}

{% block title %}Liste des événements{% endblock %}

{% block content %}

    <!-- Navbar & Hero Start -->
	<div class="container-fluid position-relative p-0">

		{% include 'front/includes/navbar.html.twig' %}

		<!-- Header Start -->
		<div class="container-fluid bg-breadcrumb-events">
			<div class="container text-center py-5" style="max-width: 900px">
				<h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">
					Events
				</h4>
				<ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
					<li class="breadcrumb-item">
						<a class="text-white" href="{{path('app_home')}}">Home</a>
					</li>
					<li class="breadcrumb-item active text-white-50">Pages</li>
					<li class="breadcrumb-item active text-primary">Events</li>
				</ol>
			</div>
		</div>
		<!-- Header End -->
	</div>
	<!-- Navbar & Hero End -->

    <div id="alertMessages" class="container mt-3">
        {% for message in app.flashes('success') %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
        {% for message in app.flashes('error') %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    </div>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Liste des événements</h1>
        </div>

        <div class="row">
            {% if events is empty %}
                <div class="col-12">
                    <p class="text-center">Aucun événement n'a été créé pour le moment.</p>
                </div>
            {% else %}
                {% for event in events %}
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            {% if event.imagePath %}
                                <img src="http://localhost/img/event/{{ event.imagePath }}" class="card-img-top" alt="{{ event.title }}" style="height: 200px; object-fit: cover;">
                            {% else %}
                                <div class="bg-light text-center py-5">
                                    <i class="fas fa-calendar-alt fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                            <div class="card-body">
                                <h5 class="card-title">{{ event.title }}</h5>
                                <p class="card-text">{{ event.description|slice(0, 100) }}{% if event.description|length > 100 %}...{% endif %}</p>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> {{ event.date ? event.date|date('d/m/Y H:i') : 'Date non définie' }}
                                    </small>
                                </p>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt"></i> {{ event.location ?: 'Lieu non défini' }}
                                    </small>
                                </p>
                                {% if event.maxParticipants %}
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-users"></i> Max: {{ event.maxParticipants }} participants
                                        </small>
                                    </p>
                                {% endif %}
                                <div class="mt-3 d-flex justify-content-between">
                                    <div style="display: inline-block;"></div>
                                    <a href="#" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#registerModal{{ event.id }}">
                                        <i class="fa-solid fa-bookmark"></i> Reserve
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Modal for each event -->
                    <div class="modal fade" id="registerModal{{ event.id }}" tabindex="-1" aria-labelledby="registerModalLabel{{ event.id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="registerModalLabel{{ event.id }}">Register for {{ event.title }}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    {% if event.imagePath %}
                                        <div class="text-center mb-3">
                                            <img src="http://localhost/img/event/{{ event.imagePath }}" class="img-fluid rounded" alt="{{ event.title }}" style="max-height: 200px;">
                                        </div>
                                    {% endif %}

                                    <div class="mb-3">
                                        <h6>Event Details:</h6>
                                        <p><strong>Date:</strong> {{ event.date ? event.date|date('d/m/Y H:i') : 'Date non définie' }}</p>
                                        <p><strong>Location:</strong> {{ event.location ?: 'Lieu non défini' }}</p>
                                        <p><strong>Description:</strong> {{ event.description }}</p>
                                    </div>

                                    <div id="modalAlert{{ event.id }}" class="alert" style="display: none;"></div>
                                    <form id="registrationForm{{ event.id }}" onsubmit="handleRegistration(event, {{ event.id }})">
                                        <div class="mb-3">
                                            <label for="email{{ event.id }}" class="form-label">Email address</label>
                                            <input type="email" class="form-control" id="email{{ event.id }}" required>
                                            <div class="form-text">We'll add this event to your calendar using this email.</div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            <button type="submit" class="btn btn-primary">Register & Add to Calendar</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>

    {% block javascripts %}
    {{ parent() }}
    <script>
    async function handleRegistration(event, eventId) {
        event.preventDefault();

        const form = document.getElementById('registrationForm' + eventId);
        const email = document.getElementById('email' + eventId).value;
        const modalAlert = document.getElementById('modalAlert' + eventId);
        const submitButton = form.querySelector('button[type="submit"]');

        // Disable submit button and show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';

        // Reset alert
        modalAlert.style.display = 'none';
        modalAlert.className = 'alert';

        try {
            const response = await fetch(`/events/${eventId}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: email
                })
            });

            // Check if response is OK
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server response:', response.status, errorText);
                throw new Error(`Server error: ${response.status}`);
            }

            // Try to parse JSON response
            let data;
            try {
                data = await response.json();
            } catch (e) {
                console.error('JSON parse error:', e);
                throw new Error('Invalid response format from server');
            }

            console.log('Server response:', data);

            if (data.success) {
                modalAlert.className = 'alert alert-success';
                modalAlert.textContent = data.message;
                modalAlert.style.display = 'block';

                // Close modal after success
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('registerModal' + eventId));
                    if (modal) {
                        modal.hide();
                    }
                }, 2000);
            } else {
                throw new Error(data.error || 'Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);
            modalAlert.className = 'alert alert-danger';
            modalAlert.textContent = error.message;
            modalAlert.style.display = 'block';
        } finally {
            // Re-enable submit button and restore text
            submitButton.disabled = false;
            submitButton.innerHTML = 'Register & Add to Calendar';
        }
    }
    </script>
    {% endblock %}

{% endblock %}
