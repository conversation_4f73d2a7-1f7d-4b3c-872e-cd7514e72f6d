<?php

namespace App\Form;

use App\Entity\Command;
use App\Entity\Product;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Validator\Constraints\NotBlank;
class ProductType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $isEdit=$options['is_edit'] ?? false;
        $imageContraints=[
            new File([
                'maxSize'=>'30M',
                'mimeTypes' => ['image/jpeg', 'image/png'],
                'mimeTypesMessage' => 'Veuillez uploader un fichier image valide (JPEG ou PNG)'
            ]),
        ];
        if(!$isEdit){
            $imageContraints[]=new NotBlank([
                'message' => 'Veuillez sélectionner une image',
            ]);
        }
        $builder
            ->add('nom_p',TextType::class, [
                'label' => 'Nom du produit',
                'attr' => ['placeholder' => 'Nom du produit'],
                'empty_data'=>'',
            ])
            ->add('image',FileType::class, [
                'label' => 'Image du produit',
                'mapped' => false,
                'required' => true,
                'empty_data'=>'',
                'constraints' =>$imageContraints,
            ])
            ->add('description',TextType::class ,[
                'label' => 'Description du produit',
                'attr' => ['placeholder' => 'Description du produit'],
                'empty_data'=>'',
            ])
            ->add('price', TextType::class,[
                'label' => 'Prix du produit',
                'attr' => ['placeholder' => 'Prix du produit'],
                'empty_data'=>'',
            ])
            ->add('stock',TextType::class,[
                'label' => 'Stock du produit',
                'attr' => ['placeholder' => 'Stock du produit'],
                'empty_data'=>'',
            ])
            ->add('categorie',ChoiceType::class,[
                'label' => 'Catégorie du produit',
                'choices' => [
                    'Clothes'=>'clothes',
                    'Electronics'=>'electronics',
                    'Food'=>'food',
                    'Books'=>'books',
                    'Others'=>'others'
                ],
                'placeholder' => 'Choissiez une categorie',
                'empty_data'=>'',
            ])
            ->add('origin',TextType::class,[
                'label' => 'Origine du produit',
                'attr' => ['placeholder' => 'Origine du produit'],
                'empty_data'=>'',
            ])
            ->add('is_ecological',CheckboxType::class,[
                'label' => 'Produit écologique?',
                'required'=>false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Product::class,
            'is_edit'=>false,
        ]);
    }
}
