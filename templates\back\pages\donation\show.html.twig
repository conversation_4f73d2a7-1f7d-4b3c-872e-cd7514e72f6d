{% extends 'back/base.html.twig' %}

{% block title %}Donation Details{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .donation-details {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-top: 2rem;
        }
        .page-title {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #4CAF50;
        }
        .back-link {
            color: #4CAF50;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .back-link:hover {
            color: #45a049;
        }
        .details-table {
            width: 100%;
            margin-bottom: 1rem;
        }
        .details-table th {
            background-color: #f8f9fa;
            color: #2c3e50;
            font-weight: 600;
            padding: 1rem;
            width: 200px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .details-table td {
            padding: 1rem;
            color: #34495e;
            border-bottom: 1px solid #eee;
        }
        .badge {
            padding: 0.4rem 0.8rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .badge-pending {
            background-color: #f1c40f;
            color: white;
        }
        .badge-approved {
            background-color: #2ecc71;
            color: white;
        }
        .badge-completed {
            background-color: #3498db;
            color: white;
        }
        .badge-cancelled {
            background-color: #e74c3c;
            color: white;
        }
        .action-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
        .btn-edit {
            background-color: #f1c40f;
            border-color: #f1c40f;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .btn-edit:hover {
            background-color: #f39c12;
            border-color: #f39c12;
            color: white;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container mt-4">
        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endfor %}

        <a href="{{ path('app_back_donation_index') }}" class="back-link">
            <i class="fas fa-arrow-left me-2"></i> Back to Donations List
        </a>
        
        <div class="donation-details">
            <h1 class="page-title">Donation Details</h1>
            
            <table class="details-table">
                <tbody>
                    <tr>
                        <th>Amount</th>
                        <td>{{ donation.amount }} TND</td>
                    </tr>
                    <tr>
                        <th>Donation Date</th>
                        <td>{{ donation.donationDate ? donation.donationDate|date('Y-m-d') : '' }}</td>
                    </tr>
                    <tr>
                        <th>Type</th>
                        <td>{{ donation.type|capitalize }}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <span class="badge badge-{{ donation.status }}">
                                {{ donation.status|capitalize }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>Payment Method</th>
                        <td>{{ donation.paymentMethod|replace({'_': ' '})|capitalize }}</td>
                    </tr>
                    <tr>
                        <th>Partner</th>
                        <td>{{ donation.partnerId.name }}</td>
                    </tr>
                </tbody>
            </table>

            <div class="action-buttons">
                <a href="{{ path('app_back_donation_edit', {'id': donation.id}) }}" class="btn btn-edit">
                    <i class="fas fa-edit me-2"></i> Edit
                </a>
                {{ include('back/pages/donation/_delete_form.html.twig') }}
            </div>
        </div>
    </div>
{% endblock %}
