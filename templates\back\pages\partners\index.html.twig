{% extends 'back/base.html.twig' %}

{% block title %}Partners List{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Header Styles */
        .page-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            padding: 2rem 0;
            margin-bottom: 2rem;
            color: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Card Styles */
        .content-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        /* Table Styles */
        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #4CAF50;
            color: #2c3e50;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            padding: 1rem;
        }

        .table tbody td {
            padding: 1rem;
            vertical-align: middle;
            color: #34495e;
            border-bottom: 1px solid #eee;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* Button Styles */
        .btn-add {
            background-color: #4CAF50;
            border-color: #4CAF50;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            background-color: #45a049;
            border-color: #45a049;
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
            transform: translateY(-1px);
        }

        .btn-action {
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            margin: 0 2px;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
        }

        .btn-action i {
            font-size: 14px !important;
            color: white !important;
            display: inline-block !important;
            line-height: 1 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .btn-view {
            background-color: #3498db;
            color: white;
        }

        .btn-view:hover {
            background-color: #2980b9;
        }

        .btn-edit {
            background-color: #f1c40f;
            color: white;
        }

        .btn-edit:hover {
            background-color: #f39c12;
        }

        .btn-delete {
            background-color: #e74c3c;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c0392b;
        }

        .btn-accept {
            background-color: #e74c3c;
            color: white;
        }

        .btn-accept:hover {
            background-color: #c0392b;
        }

        .btn-accept.accepted {
            background-color: #2ecc71;
        }

        .btn-accept.accepted:hover {
            background-color: #27ae60;
        }

        .btn-group {
            display: inline-flex;
            gap: 5px;
        }

        .table td {
            vertical-align: middle;
        }

        /* Status Badge */
        .badge-type {
            padding: 0.4rem 0.8rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Empty State */
        .empty-state {
            padding: 3rem;
            text-align: center;
            color: #7f8c8d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #bdc3c7;
        }

        /* Alert styling */
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn-close {
            font-size: 0.8rem;
            opacity: 0.5;
        }
        .btn-close:hover {
            opacity: 0.75;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .table-responsive {
                border: 0;
            }

            .btn-action {
                padding: 0.3rem 0.6rem;
                font-size: 0.8rem;
            }

            .page-header {
                padding: 1.5rem 0;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }

        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }
        
        .popup-content {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 600px;
            width: 90%;
            z-index: 1001;
        }
        
        .popup-header {
            margin-bottom: 30px;
        }
        
        .popup-header h3 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .popup-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 0;
        }
        
        .popup-body {
            margin-bottom: 35px;
        }

        .donation-type-card {
            padding: 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: block;
            color: #2c3e50;
            background: #f8f9fa;
            height: 100%;
        }

        .donation-type-card:hover {
            transform: translateY(-5px);
            background: #e9ecef;
            color: #2c3e50;
        }

        .donation-type-card i {
            font-size: 2.5rem;
            color: #4CAF50;
            margin-bottom: 15px;
            display: block;
        }

        .donation-type-card p {
            font-weight: 500;
            margin: 0;
            font-size: 1.1rem;
        }
        
        .popup-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn-popup {
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
        }
        
        .btn-popup-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-popup-primary:hover {
            background-color: #45a049;
            color: white;
            transform: translateY(-2px);
        }
        
        .btn-popup-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-popup-secondary:hover {
            background-color: #5a6268;
            color: white;
            transform: translateY(-2px);
        }

        .btn-donate-front {
            background: #4CAF50;
            color: white;
            padding: 10px 25px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-donate-front:hover {
            background: #45a049;
            transform: translateY(-2px);
            color: white;
        }

        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6c757d;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close-button:hover {
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .popup-content {
                padding: 25px;
            }
            
            .donation-type-card {
                margin-bottom: 15px;
            }
        }

        .btn-list-donations {
            background: #2196F3;
            color: white;
            padding: 10px 25px;
            border-radius: 50px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-list-donations:hover {
            background: #1976D2;
            transform: translateY(-2px);
            color: white;
        }

        .donations-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 20px;
        }

        .donations-table th,
        .donations-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .donations-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .donations-table tr:last-child td {
            border-bottom: none;
        }

        .donations-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .donation-amount {
            font-weight: 500;
            color: #4CAF50;
        }

        .donation-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .donation-status {
            padding: 4px 12px;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-completed {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }

        .popup-content.donations-list {
            max-width: 800px;
        }

        .donations-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .donations-total {
            background: #e9ecef;
            padding: 8px 16px;
            border-radius: 50px;
            font-weight: 500;
            color: #2c3e50;
        }

        .table-container {
            max-height: 400px;
            overflow-y: auto;
            margin: -20px;
            padding: 20px;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .btn-group {
            display: flex;
            gap: 5px;
            justify-content: flex-end;
        }

        .btn-group .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .btn-group form {
            margin: 0;
        }

        .btn-group .btn-delete {
            margin: 0;
            height: 100%;
        }

        .table td {
            vertical-align: middle;
        }

        .action-buttons {
            display: inline-flex;
            gap: 8px;
        }

        .action-buttons .btn {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
        }

        .action-buttons form {
            margin: 0;
            display: inline;
        }

        .action-buttons .btn-delete {
            padding: 6px 12px;
        }

        .partner-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            overflow: hidden;
        }

        .partner-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .partner-logo i {
            font-size: 1.2rem;
            color: #6c757d;
        }

        .partner-logo.ngo {
            background-color: #e8f5e9;
            border-color: #c8e6c9;
        }

        .partner-logo.company {
            background-color: #e3f2fd;
            border-color: #bbdefb;
        }

        .partner-logo.individual {
            background-color: #fff3e0;
            border-color: #ffe0b2;
        }

        .table > tbody > tr > td {
            vertical-align: middle;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .action-buttons .btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease-in-out;
        }

        .btn-view {
            background-color: #0dcaf0;
            border-color: #0dcaf0;
            color: #fff;
        }

        .btn-view:hover {
            background-color: #31d2f2;
            border-color: #25cff2;
            color: #fff;
        }

        .btn-edit {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
        }

        .btn-edit:hover {
            background-color: #ffca2c;
            border-color: #ffc720;
            color: #000;
        }

        .btn-delete {
            background-color: #dc3545;
            border-color: #dc3545;
            color: #fff;
        }

        .btn-delete:hover {
            background-color: #bb2d3b;
            border-color: #b02a37;
            color: #fff;
        }

        .badge-type {
            padding: 0.5em 0.75em;
            font-size: 0.75em;
            font-weight: 500;
            border-radius: 0.375rem;
            background-color: #0d6efd;
            color: #fff;
            display: inline-block;
        }

        .table > :not(caption) > * > * {
            padding: 1rem;
            vertical-align: middle;
        }

        .location-info {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
{% endblock %}

{% block body %}
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="page-title">Partners Management</h1>
                <div class="d-flex gap-2">
                    <a href="#" class="btn-list-donations" onclick="showDonationsList()">
                        <i class="fas fa-list"></i> View Donations
                    </a>
                    <a href="#" class="btn-donate-front" onclick="showDonationPopup()">
                        <i class="fas fa-hand-holding-heart"></i> Make a Donation
                    </a>
                    <a href="{{ path('app_back_donation_new') }}" class="btn btn-add">
                        <i class="fas fa-hand-holding-heart me-2"></i> New Donation
                    </a>
                    <a href="{{ path('app_partners_new') }}" class="btn btn-add">
                        <i class="fas fa-plus me-2"></i> New Partner
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Donations List Popup -->
    <div id="donationsListPopup" class="popup-overlay">
        <div class="popup-content donations-list">
            <button onclick="hideDonationsList()" class="close-button">
                <i class="fas fa-times"></i>
            </button>
            <div class="donations-header">
                <h3>All Donations</h3>
                <span class="donations-total">Total: {{ donations|length }} donations</span>
            </div>
            <div class="table-container">
                <table class="donations-table">
                    <thead>
                        <tr>
                            <th>Partner</th>
                            <th>Amount</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for donation in donations %}
                            <tr>
                                <td>{{ donation.partnerId.name }}</td>
                                <td class="donation-amount">{{ donation.amount }} TND</td>
                                <td>{{ donation.type }}</td>
                                <td class="donation-date">{{ donation.donationDate|date('Y-m-d') }}</td>
                                <td>
                                    <span class="donation-status status-{{ donation.status }}">
                                        {{ donation.status|capitalize }}
                                    </span>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No donations found
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Donation Popup -->
    <div id="donationPopup" class="popup-overlay">
        <div class="popup-content">
            <button onclick="hideDonationPopup()" class="close-button">
                <i class="fas fa-times"></i>
            </button>
            <div class="popup-header">
                <h3>Choose Your Donation Type</h3>
                <p>Select how you would like to contribute to our cause</p>
            </div>
            <div class="popup-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ path('app_front_donation_new', {'type': 'Monetary'}) }}" class="donation-type-card">
                            <i class="fas fa-dollar-sign"></i>
                            <p>Monetary Donation</p>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ path('app_front_donation_new', {'type': 'Material'}) }}" class="donation-type-card">
                            <i class="fas fa-box-open"></i>
                            <p>Material Donation</p>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ path('app_front_donation_new', {'type': 'Service'}) }}" class="donation-type-card">
                            <i class="fas fa-hands-helping"></i>
                            <p>Service Donation</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="popup-buttons">
                <a href="{{ path('app_front_donation_index') }}" class="btn-popup btn-popup-primary">
                    <i class="fas fa-list-ul me-2"></i> View All Options
                </a>
                <button onclick="hideDonationPopup()" class="btn-popup btn-popup-secondary">
                    <i class="fas fa-times me-2"></i> Close
                </button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Flash Messages -->
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label == 'error' ? 'danger' : label }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endfor %}

        <div class="content-card">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Logo</th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Location</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for partner in partners %}
                        <tr>
                            <td>
                                {% if partner.logo %}
                                    {% set filename = partner.logo|split('\\')|last %}
                                    <div class="logo-container">
                                        <img src="http://localhost/images/{{ filename }}"
                                            alt="Logo of {{ partner.name }}"
                                            class="partner-logo">
                                    </div>
                                {% else %}
                                    <div class="partner-logo {{ partner.type|lower }}">
                                        {% if partner.type == 'NGO' %}
                                            <i class="fas fa-hands-helping"></i>
                                        {% elseif partner.type == 'Company' %}
                                            <i class="fas fa-building"></i>
                                        {% elseif partner.type == 'Individual' %}
                                            <i class="fas fa-user-tie"></i>
                                        {% else %}
                                            <i class="fas fa-handshake"></i>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </td>
                            <td>{{ partner.name }}</td>
                            <td>
                                <span class="badge-type">
                                    {{ partner.type }}
                                </span>
                            </td>
                            <td>
                                Lat: {{ partner.latitude }}<br>
                                Long: {{ partner.longitude }}
                            </td>
                            <td class="actions">
                                <div class="btn-group">
                                    <button onclick="toggleAccept(this, {{ partner.id }})" 
                                            class="btn btn-action btn-accept {% if partner.isAccepted %}accepted{% endif %}" 
                                            title="{% if partner.isAccepted %}Partner Accepted{% else %}Accept Partner{% endif %}"
                                            data-partner-id="{{ partner.id }}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <a href="{{ path('app_partners_show', {'id': partner.id}) }}" 
                                       class="btn btn-action btn-view" 
                                       title="View Partner Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ path('app_partners_edit', {'id': partner.id}) }}" 
                                       class="btn btn-action btn-edit" 
                                       title="Edit Partner">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{{ path('app_partners_delete', {'id': partner.id}) }}" 
                                          onsubmit="return confirm('Are you sure you want to delete this partner?');" 
                                          style="display: inline;">
                                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ partner.id) }}">
                                        <button class="btn btn-action btn-delete" title="Delete Partner">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="6" class="empty-state">
                                <i class="fas fa-folder-open"></i>
                                <p class="mb-0">No partners found. Start by adding a new partner!</p>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function toggleAccept(button, partnerId) {
            const isAccepted = button.classList.contains('accepted');
            
            // Send AJAX request to update partner status
            fetch(`{{ path('app_partners_toggle_accept', {'id': 'PARTNER_ID'}) }}`.replace('PARTNER_ID', partnerId), {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    button.classList.toggle('accepted');
                    if (data.isAccepted) {
                        button.style.backgroundColor = '#2ecc71';
                    } else {
                        button.style.backgroundColor = '#e74c3c';
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the partner status');
            });
        }

        function showDonationPopup() {
            const popup = document.getElementById('donationPopup');
            popup.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Add fade-in animation
            popup.style.opacity = 0;
            setTimeout(() => {
                popup.style.opacity = 1;
                popup.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function hideDonationPopup() {
            const popup = document.getElementById('donationPopup');
            popup.style.opacity = 0;
            popup.style.transition = 'opacity 0.3s ease';
            
            setTimeout(() => {
                popup.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        function showDonationsList() {
            const popup = document.getElementById('donationsListPopup');
            popup.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            popup.style.opacity = 0;
            setTimeout(() => {
                popup.style.opacity = 1;
                popup.style.transition = 'opacity 0.3s ease';
            }, 10);
        }

        function hideDonationsList() {
            const popup = document.getElementById('donationsListPopup');
            popup.style.opacity = 0;
            popup.style.transition = 'opacity 0.3s ease';
            
            setTimeout(() => {
                popup.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // Close popup when clicking outside
        document.getElementById('donationPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                hideDonationPopup();
            }
        });

        document.getElementById('donationsListPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                hideDonationsList();
            }
        });

        // Close popup when pressing ESC key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.getElementById('donationsListPopup').style.display === 'block') {
                    hideDonationsList();
                }
                if (document.getElementById('donationPopup').style.display === 'block') {
                    hideDonationPopup();
                }
            }
        });
    </script>
{% endblock %}
