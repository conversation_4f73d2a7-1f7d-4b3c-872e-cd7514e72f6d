<?php

namespace App\Controller\Front;

use App\Entity\Comments;
use App\Entity\Forums;
use App\Form\ForumsType;
use App\Repository\ForumsRepository;
use App\Service\SentimentAnalyzer;
use App\Service\TagManager;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/front/pages/forums')]
#[IsGranted('ROLE_USER')]
final class ForumsController extends AbstractController
{
    public function __construct(
        private PaginatorInterface $paginator,
        private TagManager $tagManager
    ) {}

    #[Route(name: 'front_forums_index', methods: ['GET'])]
    public function index(ForumsRepository $forumsRepository, Request $request): Response
    {
        // Get view mode from query or default to 'grid'
        $viewMode = $request->query->get('view', 'grid');

        // Get filter for user's posts only
        $myPostsOnly = $request->query->getBoolean('my_posts', false);

        // Get tag filter if any
        $tagFilter = $request->query->get('tag');

        // Get user ID for filtering if needed
        $userId = $myPostsOnly ? $this->getUser()->getId() : null;

        // Get the query with filters applied
        if ($tagFilter) {
            // If tag filter is applied, use the findByTag method
            $forums = $forumsRepository->findByTag($tagFilter);

            // If user filter is also applied, filter the results further
            if ($userId) {
                $forums = array_filter($forums, function($forum) use ($userId) {
                    return $forum->getUser() && $forum->getUser()->getId() === $userId;
                });
            }

            // Create a custom query for pagination
            $query = $forumsRepository->createQueryBuilder('f')
                ->where('f.id IN (:ids)')
                ->setParameter('ids', array_map(function($forum) {
                    return $forum->getId();
                }, $forums))
                ->orderBy('f.createdAt', 'DESC')
                ->getQuery();
        } else {
            // No tag filter, use the standard query
            $query = $forumsRepository->getForumsQuery($userId);
        }

        // Apply pagination
        $pagination = $this->paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            10 // Items per page
        );

        // Get top contributors
        $topContributors = $forumsRepository->getTopContributors(3);

        // Get all tags
        $allTags = $this->tagManager->getAllTags();

        return $this->render('front/pages/forums/index.html.twig', [
            'forums' => $pagination,
            'viewMode' => $viewMode,
            'myPostsOnly' => $myPostsOnly,
            'topContributors' => $topContributors,
            'allTags' => $allTags,
            'currentTag' => $tagFilter,
        ]);
    }

    #[Route('/tags', name: 'front_forums_tags', methods: ['GET'])]
    public function getTags(): JsonResponse
    {
        return $this->json([
            'tags' => $this->tagManager->getAllTags()
        ]);
    }

    #[Route('/new', name: 'front_forums_new', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_USER')]
    public function new(
        Request $request,
        EntityManagerInterface $entityManager,
        SentimentAnalyzer $sentimentAnalyzer
    ): Response {
        $forum = new Forums();
        $forum->setUser($this->getUser());
        $form = $this->createForm(ForumsType::class, $forum);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Analyze sentiment of title and content combined
            $text = $forum->getTitle() . ' ' . $forum->getContent();
            $sentiment = $sentimentAnalyzer->analyzeSentiment($text);
            $forum->setSentiment($sentiment);

            // Extract and save hashtags
            $tags = $this->tagManager->extractTags($forum->getContent());
            if (!empty($tags)) {
                $this->tagManager->saveTags($tags);
            }

            $entityManager->persist($forum);
            $entityManager->flush();

            return $this->redirectToRoute('front_forums_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('front/pages/forums/new.html.twig', [
            'forum' => $forum,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'front_forums_show', methods: ['GET'])]
    public function show(Forums $forum): Response
    {
        return $this->render('front/pages/forums/show.html.twig', [
            'forum' => $forum,
        ]);
    }

    #[Route('/{id}/edit', name: 'front_forums_edit', methods: ['GET', 'POST'])]
    public function edit(
        Request $request,
        Forums $forum,
        EntityManagerInterface $entityManager,
        SentimentAnalyzer $sentimentAnalyzer
    ): Response {
        // Check if the current user is the author of the post
        if ($forum->getUser() !== $this->getUser()) {
            throw $this->createAccessDeniedException('You can only edit your own posts.');
        }

        $form = $this->createForm(ForumsType::class, $forum);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Re-analyze sentiment after edit
            $text = $forum->getTitle() . ' ' . $forum->getContent();
            $sentiment = $sentimentAnalyzer->analyzeSentiment($text);
            $forum->setSentiment($sentiment);

            // Extract and save hashtags
            $tags = $this->tagManager->extractTags($forum->getContent());
            if (!empty($tags)) {
                $this->tagManager->saveTags($tags);
            }

            $entityManager->flush();

            return $this->redirectToRoute('front_forums_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('front/pages/forums/edit.html.twig', [
            'forum' => $forum,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'front_forums_delete', methods: ['POST'])]
    public function delete(Request $request, Forums $forum, EntityManagerInterface $entityManager): Response
    {
        // Check if the current user is the author of the post
        if ($forum->getUser() !== $this->getUser()) {
            throw $this->createAccessDeniedException('You can only delete your own posts.');
        }

        if ($this->isCsrfTokenValid('delete'.$forum->getId(), $request->request->get('_token'))) {
            $entityManager->remove($forum);
            $entityManager->flush();
        }

        return $this->redirectToRoute('front_forums_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/comment', name: 'front_forums_add_comment', methods: ['POST'])]
    public function addComment(Request $request, Forums $forum, EntityManagerInterface $entityManager): Response
    {
        $content = $request->request->get('content');

        if (!$content) {
            return $this->json(['error' => 'Comment content is required'], 400);
        }

        $comment = new Comments();
        $comment->setContent($content);
        $comment->setUpvotes(0);
        $comment->setPostid($forum);

        $entityManager->persist($comment);
        $entityManager->flush();

        return $this->redirectToRoute('front_forums_show', ['id' => $forum->getId()], Response::HTTP_SEE_OTHER);
    }

    #[Route('/comment/{id}/upvote', name: 'front_forums_upvote_comment', methods: ['POST'])]
    public function upvoteComment(Comments $comment, EntityManagerInterface $entityManager): Response
    {
        $comment->setUpvotes($comment->getUpvotes() + 1);
        $entityManager->flush();

        return $this->redirectToRoute('front_forums_show', ['id' => $comment->getPostid()->getId()], Response::HTTP_SEE_OTHER);
    }
}
