<?php

namespace App\Controller\Back;

use App\Entity\Partners;
use App\Form\PartnersType;
use App\Repository\DonationRepository;
use App\Repository\PartnersRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\String\Slugger\SluggerInterface;

#[Route('/back/partner')]
class PartnersController extends AbstractController
{
    #[Route('/', name: 'app_partners_index', methods: ['GET'])]
    public function index(PartnersRepository $partnersRepository, DonationRepository $donationRepository): Response
    {
        return $this->render('back/pages/partners/index.html.twig', [
            'partners' => $partnersRepository->findAll(),
            'donations' => $donationRepository->findBy([], ['donation_date' => 'DESC']),
        ]);
    }

    #[Route('/new', name: 'app_partners_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager, SluggerInterface $slugger): Response
    {
        $partner = new Partners();
        $form = $this->createForm(PartnersType::class, $partner);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $logoFile = $form->get('logo')->getData();

            if ($logoFile) {
                $originalFilename = pathinfo($logoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $slugger->slug($originalFilename);
                $newFilename = $safeFilename.'-'.uniqid().'.'.$logoFile->guessExtension();

                try {
                    $logoFile->move(
                        $this->getParameter('partners_logos_directory'),
                        $newFilename
                    );
                    $partner->setLogo($newFilename);
                } catch (FileException $e) {
                    // Let the exception bubble up to be handled by Symfony's error handler
                }
            }

            $entityManager->persist($partner);
            $entityManager->flush();

            return $this->redirectToRoute('app_partners_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('back/pages/partners/new.html.twig', [
            'partner' => $partner,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partners_show', methods: ['GET'])]
    public function show(Partners $partner): Response
    {
        return $this->render('back/pages/partners/show.html.twig', [
            'partner' => $partner,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_partners_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Partners $partner, EntityManagerInterface $entityManager, SluggerInterface $slugger): Response
    {
        $form = $this->createForm(PartnersType::class, $partner);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $logoFile = $form->get('logo')->getData();

            if ($logoFile) {
                $originalFilename = pathinfo($logoFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $slugger->slug($originalFilename);
                $newFilename = $safeFilename.'-'.uniqid().'.'.$logoFile->guessExtension();

                try {
                    // Delete old logo if exists
                    if ($partner->getLogo()) {
                        $oldLogoPath = $this->getParameter('partners_logos_directory').'/'.$partner->getLogo();
                        if (file_exists($oldLogoPath)) {
                            unlink($oldLogoPath);
                        }
                    }

                    $logoFile->move(
                        $this->getParameter('partners_logos_directory'),
                        $newFilename
                    );
                    $partner->setLogo($newFilename);
                } catch (FileException $e) {
                    // Let the exception bubble up to be handled by Symfony's error handler
                }
            }

            $entityManager->flush();
            return $this->redirectToRoute('app_partners_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('back/pages/partners/edit.html.twig', [
            'partner' => $partner,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partners_delete', methods: ['POST'])]
    public function delete(Request $request, Partners $partner, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partner->getId(), $request->request->get('_token'))) {
            if ($partner->getLogo()) {
                $logoPath = $this->getParameter('partners_logos_directory').'/'.$partner->getLogo();
                if (file_exists($logoPath)) {
                    unlink($logoPath);
                }
            }

            $entityManager->remove($partner);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_partners_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/toggle-accept', name: 'app_partners_toggle_accept', methods: ['POST'])]
    public function toggleAccept(Request $request, Partners $partner, EntityManagerInterface $entityManager): JsonResponse
    {
        try {
            $partner->setIsAccepted(!$partner->isIsAccepted());
            $entityManager->flush();

            return $this->json([
                'success' => true,
                'isAccepted' => $partner->isIsAccepted()
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => 'An error occurred while updating the partner status'
            ], 500);
        }
    }
}
