<?php

namespace App\Controller\Front;

use App\Entity\Comments;
use App\Entity\Forums;
use App\Repository\CommentsRepository;
use App\Repository\ForumsRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/front/pages/comments')]
#[IsGranted('ROLE_USER')]
final class CommentsController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private ValidatorInterface $validator
    ) {}

    #[Route('/new', name: 'front_comments_new', methods: ['POST'])]
    public function new(
        Request $request,
        ForumsRepository $forumsRepository
    ): Response {
        try {
            // Get and validate input
            $content = trim($request->request->get('content', ''));
            $forumId = (int) $request->request->get('forumId');

            if (!$content || !$forumId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Missing required fields'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Find the forum
            $forum = $forumsRepository->find($forumId);
            if (!$forum) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Forum not found'
                ], Response::HTTP_NOT_FOUND);
            }

            // Create and validate comment
            $comment = new Comments();
            $comment->setUser($this->getUser());
            $comment->setContent($content);
            $comment->setForum($forum);
            $comment->setCreatedAt(new \DateTimeImmutable());

            // Validate the entity
            $errors = $this->validator->validate($comment);
            if (count($errors) > 0) {
                $errorMessages = [];
                foreach ($errors as $error) {
                    $errorMessages[] = $error->getMessage();
                }
                return new JsonResponse([
                    'success' => false,
                    'error' => implode(', ', $errorMessages)
                ], Response::HTTP_BAD_REQUEST);
            }

            // Save to database
            $this->entityManager->persist($comment);
            $this->entityManager->flush();

            // Return success response with user information
            $user = $this->getUser();
            return new JsonResponse([
                'success' => true,
                'comment' => [
                    'id' => $comment->getId(),
                    'content' => $comment->getContent(),
                    'upvotes' => $comment->getUpvotes(),
                    'createdAt' => $comment->getCreatedAt()->format('M d, Y H:i'),
                    'user' => [
                        'id' => $user->getId(),
                        'fullName' => $user->getFullName(),
                        'image' => $user->getImage()
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Error creating comment: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{id}/edit', name: 'front_comments_edit', methods: ['POST'])]
    public function edit(
        Request $request,
        Comments $comment
    ): JsonResponse {
        try {
            // Check if the current user is the owner of the comment
            if ($comment->getUser() !== $this->getUser()) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'You can only edit your own comments'
                ], Response::HTTP_FORBIDDEN);
            }

            $content = trim($request->request->get('content', ''));

            if (!$content) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Content is required'
                ], Response::HTTP_BAD_REQUEST);
            }

            $comment->setContent($content);

            // Validate the entity
            $errors = $this->validator->validate($comment);
            if (count($errors) > 0) {
                $errorMessages = [];
                foreach ($errors as $error) {
                    $errorMessages[] = $error->getMessage();
                }
                return new JsonResponse([
                    'success' => false,
                    'error' => implode(', ', $errorMessages)
                ], Response::HTTP_BAD_REQUEST);
            }

            $this->entityManager->flush();

            $user = $comment->getUser();
            return new JsonResponse([
                'success' => true,
                'comment' => [
                    'id' => $comment->getId(),
                    'content' => $comment->getContent(),
                    'upvotes' => $comment->getUpvotes(),
                    'createdAt' => $comment->getCreatedAt()->format('M d, Y H:i'),
                    'user' => [
                        'id' => $user->getId(),
                        'fullName' => $user->getFullName(),
                        'image' => $user->getImage()
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Error updating comment: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{id}/delete', name: 'front_comments_delete', methods: ['POST'])]
    public function delete(Comments $comment): JsonResponse
    {
        try {
            // Check if the current user is the owner of the comment
            if ($comment->getUser() !== $this->getUser()) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'You can only delete your own comments'
                ], Response::HTTP_FORBIDDEN);
            }

            $this->entityManager->remove($comment);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Error deleting comment: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{id}/upvote', name: 'front_comments_upvote', methods: ['POST'])]
    public function upvote(Comments $comment): JsonResponse
    {
        try {
            // Toggle the upvote
            if ($comment->getUpvotes() > 0) {
                $comment->setUpvotes(0); // Unlike
            } else {
                $comment->setUpvotes(1); // Like
            }

            $this->entityManager->persist($comment);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'upvotes' => $comment->getUpvotes()
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Error upvoting comment: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
