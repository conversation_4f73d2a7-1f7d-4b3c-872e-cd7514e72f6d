{% extends 'back/base.html.twig' %}

{% block title %}Event Registrations Management{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        /* Stats Card Styles */
        .stats-card {
            border-radius: 0.75rem;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.5rem;
        }

        /* Chart Styles */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 0 auto;
        }

        .chart-card {
            background: #ffffff;
            border: none;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,.08);
        }

        .chart-card .card-header {
            background: #ffffff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.5rem;
        }

        .chart-card .card-body {
            padding: 1.5rem;
        }

        .chart-title {
            font-weight: 600;
            margin-bottom: 0;
            display: flex;
            align-items: center;
        }

        .chart-title i {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .chart-actions {
            display: flex;
            gap: 10px;
        }

        .chart-action-btn {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #f8f9fa;
            color: #6c757d;
            border: none;
            transition: all 0.2s ease;
        }

        .chart-action-btn:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .event-stat-pill {
            padding: 8px 15px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.85rem;
            display: inline-flex;
            align-items: center;
            margin-right: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .event-stat-pill i {
            margin-right: 5px;
            font-size: 1.1em;
        }

        /* Background Subtle Colors */
        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .text-primary {
            color: #0d6efd !important;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-warning {
            color: #ffc107 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* Gradient Colors */
        .gradient-blue {
            background: linear-gradient(45deg, #4e73df, #36b9cc);
            color: white;
        }

        .gradient-green {
            background: linear-gradient(45deg, #1cc88a, #20c997);
            color: white;
        }

        .gradient-orange {
            background: linear-gradient(45deg, #f6c23e, #fd7e14);
            color: white;
        }

        .gradient-red {
            background: linear-gradient(45deg, #e74a3b, #e83e8c);
            color: white;
        }

        /* Chart Legend */
        .chart-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            font-size: 0.85rem;
            padding: 5px 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .legend-item:hover {
            background-color: #e9ecef;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
            margin-right: 8px;
        }

        /* Animation Classes */
        .animate__animated {
            animation-duration: 0.5s;
        }

        .animate__fadeIn {
            animation-name: fadeIn;
        }

        .animate__fadeInUp {
            animation-name: fadeInUp;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0 text-gray-800">Event Registrations Management</h1>
            <p class="text-muted">Monitor and manage all event registrations</p>
        </div>
    </div>

    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="ri-checkbox-circle-line me-2"></i> {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="ri-error-warning-line me-2"></i> {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    <!-- Statistics Summary Cards -->
    <div class="row mb-4">
        <!-- Total Events Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary-subtle rounded-3 p-3 me-3">
                            <i class="ri-calendar-event-line text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted">Total Events</h6>
                            <h3 class="mb-0">{{ registrationCounts|length }}</>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Registrations Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-danger-subtle rounded-3 p-3 me-3">
                            <i class="ri-user-follow-line text-danger fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted">Total Registrations</h6>
                            <h3 class="mb-0">{{ registrationCounts|map(count => count.registration_count)|reduce((sum, count) => sum + count, 0) }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Registrations Card -->
        <div class="col-md-3 mb-4 mb-md-0">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success-subtle rounded-3 p-3 me-3">
                            <i class="ri-bar-chart-line text-success fs-4"></i>
                        </div>
                        <div>
                            {% set totalEvents = registrationCounts|length %}
                            {% set totalRegistrations = registrationCounts|map(count => count.registration_count)|reduce((sum, count) => sum + count, 0) %}
                            {% set avgRegistrations = totalEvents > 0 ? (totalRegistrations / totalEvents)|round(1) : 0 %}
                            <h6 class="mb-0 text-muted">Avg. Per Event</h6>
                            <h3 class="mb-0">{{ avgRegistrations }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Most Popular Event Card -->
        <div class="col-md-3">
            <div class="card border-0 shadow-sm stats-card animate__animated animate__fadeIn" style="animation-delay: 0.3s">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning-subtle rounded-3 p-3 me-3">
                            <i class="ri-trophy-line text-warning fs-4"></i>
                        </div>
                        <div>
                            {% set mostPopularEvent = {'title': 'None', 'count': 0} %}
                            {% for count in registrationCounts %}
                                {% if count.registration_count > mostPopularEvent.count %}
                                    {% set mostPopularEvent = {'title': count.event_title, 'count': count.registration_count} %}
                                {% endif %}
                            {% endfor %}
                            <h6 class="mb-0 text-muted">Most Popular</h6>
                            <h3 class="mb-0">{{ mostPopularEvent.count }}</h3>                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Chart Section -->
    <div class="chart-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="chart-title">
                <i class="ri-bar-chart-box-line text-primary"></i>
                Event Registration Statistics
            </h5>
            <div class="chart-actions">
                <button type="button" class="chart-action-btn" id="toggleChartType" data-bs-toggle="tooltip" data-bs-placement="top" title="Toggle Chart Type">
                    <i class="ri-exchange-line"></i>
                </button>
                <button type="button" class="chart-action-btn" id="downloadChart" data-bs-toggle="tooltip" data-bs-placement="top" title="Download Chart">
                    <i class="ri-download-line"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <span class="event-stat-pill gradient-blue">
                    <i class="ri-calendar-check-line"></i>
                    {{ registrationCounts|length }} Events
                </span>
                <span class="event-stat-pill gradient-green">
                    <i class="ri-user-follow-line"></i>
                    {{ registrationCounts|map(count => count.registration_count)|reduce((sum, count) => sum + count, 0) }} Registrations
                </span>
            </div>

            <div class="chart-container">
                <canvas id="registrationChart"></canvas>
            </div>

            <div class="chart-legend">
                {% for count in registrationCounts %}
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: {{ ['rgba(54, 162, 235, 0.8)', 'rgba(75, 192, 192, 0.8)', 'rgba(255, 159, 64, 0.8)', 'rgba(255, 99, 132, 0.8)', 'rgba(153, 102, 255, 0.8)'][loop.index0 % 5] }}"></div>
                        <span>{{ count.event_title }}: {{ count.registration_count }}</span>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.3s; border-radius: 0.75rem;">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0 fw-bold">Registration List</h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" id="registrationSearch" class="form-control" placeholder="Search registrations...">
                        <span class="input-group-text bg-primary text-white">
                            <i class="ri-search-line"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle border-0" id="registrationsTable">
                    <thead class="table-light">
                        <tr>
                            <th>Event Title</th>
                            <th>User</th>
                            <th>Status</th>
                            <th>Registration Date</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for registration in registrations %}
                            <tr class="align-middle">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="event-icon me-2 bg-primary-subtle text-primary rounded-circle" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                                            <i class="ri-calendar-event-line"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ registration.event.title }}</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2 bg-success-subtle rounded-circle">
                                            <span class="avatar-text text-success">{{ registration.userFullName|slice(0,1)|upper }}</span>
                                        </div>
                                        {{ registration.userFullName }}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ registration.status == 'registered' ? 'success' : 'secondary' }} rounded-pill">
                                        <i class="ri-{{ registration.status == 'registered' ? 'check-line' : 'time-line' }} me-1"></i>
                                        {{ registration.status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="ri-calendar-line text-muted me-2"></i>
                                        <div>
                                            <div>{{ registration.registrationDate|date('M d, Y') }}</div>
                                            <small class="text-muted">{{ registration.registrationDate|date('H:i') }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ path('app_admin_event_registration_by_event', {'id': registration.event.id}) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip"
                                           data-bs-placement="top"
                                           title="View Event Details">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="ri-calendar-event-line empty-state-icon"></i>
                                        <h5>No registrations found</h5>
                                        <p class="text-muted">There are no event registrations yet</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Register Chart.js plugins
            Chart.register(ChartDataLabels);

            // Chart data
            const labels = {{ registrationCounts|map(count => count.event_title)|json_encode|raw }};
            const data = {{ registrationCounts|map(count => count.registration_count)|json_encode|raw }};

            // Generate gradient colors for each bar
            const colorPalette = [
                ['rgba(54, 162, 235, 0.8)', 'rgba(54, 162, 235, 0.4)'],
                ['rgba(75, 192, 192, 0.8)', 'rgba(75, 192, 192, 0.4)'],
                ['rgba(255, 159, 64, 0.8)', 'rgba(255, 159, 64, 0.4)'],
                ['rgba(255, 99, 132, 0.8)', 'rgba(255, 99, 132, 0.4)'],
                ['rgba(153, 102, 255, 0.8)', 'rgba(153, 102, 255, 0.4)']
            ];

            const backgroundColors = [];
            const borderColors = [];

            for (let i = 0; i < data.length; i++) {
                const colorSet = colorPalette[i % colorPalette.length];
                backgroundColors.push(colorSet[0]);
                borderColors.push(colorSet[1]);
            }

            // Create chart
            const ctx = document.getElementById('registrationChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Number of Registrations',
                        data: data,
                        backgroundColor: backgroundColors,
                        borderColor: borderColors,
                        borderWidth: 2,
                        borderRadius: 6,
                        borderSkipped: false,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                font: {
                                    size: 12
                                },
                                color: '#6c757d'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 12
                                },
                                color: '#6c757d'
                            },
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            padding: 12,
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 13
                            },
                            displayColors: false,
                            callbacks: {
                                title: function(tooltipItems) {
                                    return tooltipItems[0].label;
                                },
                                label: function(context) {
                                    return `Registrations: ${context.parsed.y}`;
                                }
                            }
                        },
                        datalabels: {
                            color: '#fff',
                            font: {
                                weight: 'bold'
                            },
                            formatter: function(value) {
                                return value;
                            },
                            display: function(context) {
                                return context.dataset.data[context.dataIndex] > 0;
                            }
                        }
                    }
                }
            });

            // Toggle chart type between bar and pie
            document.getElementById('toggleChartType').addEventListener('click', function() {
                const currentType = chart.config.type;
                let newType = currentType === 'bar' ? 'pie' : 'bar';

                // Update chart type
                chart.config.type = newType;

                // Adjust options based on chart type
                if (newType === 'pie') {
                    chart.options.plugins.datalabels.formatter = function(value, context) {
                        return context.chart.data.labels[context.dataIndex];
                    };
                    chart.options.plugins.tooltip.callbacks.label = function(context) {
                        const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                        const percentage = Math.round((context.parsed * 100) / total);
                        return `Registrations: ${context.parsed} (${percentage}%)`;
                    };
                } else {
                    chart.options.plugins.datalabels.formatter = function(value) {
                        return value;
                    };
                    chart.options.plugins.tooltip.callbacks.label = function(context) {
                        return `Registrations: ${context.parsed.y}`;
                    };
                }

                // Update icon
                const icon = this.querySelector('i');
                icon.className = newType === 'bar' ? 'ri-exchange-line' : 'ri-pie-chart-line';

                // Update chart
                chart.update();
            });

            // Download chart as image
            document.getElementById('downloadChart').addEventListener('click', function() {
                const link = document.createElement('a');
                link.download = 'event-registration-statistics.png';
                link.href = chart.toBase64Image();
                link.click();
            });

            // Registration search functionality
            const searchInput = document.getElementById('registrationSearch');
            if (searchInput) {
                const table = document.getElementById('registrationsTable');
                const rows = table.querySelectorAll('tbody tr');

                searchInput.addEventListener('keyup', function() {
                    const searchTerm = searchInput.value.toLowerCase();

                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });
            });
        });
    </script>
{% endblock %}