{% extends 'base.html.twig' %}

{% block title %}Liste des partenaires{% endblock %}

{% block body %}
    <div class="container mt-4">
        {% include 'flash_messages.html.twig' %}
        
        <h1>Liste des partenaires</h1>

        <a href="{{ path('admin_partner_new') }}" class="btn btn-primary mb-3">Ajouter un nouveau partenaire</a>

        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nom</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>Localisation</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            {% for partner in partners %}
                <tr>
                    <td>{{ partner.partnerId }}</td>
                    <td>{{ partner.name }}</td>
                    <td>{{ partner.type }}</td>
                    <td>{{ partner.description }}</td>
                    <td>{{ partner.geoLocation }}</td>
                    <td>
                        <a href="{{ path('admin_partner_show', {'partner_id': partner.partnerId}) }}" class="btn btn-info btn-sm">Voir</a>
                        <a href="{{ path('admin_partner_edit', {'partner_id': partner.partnerId}) }}" class="btn btn-warning btn-sm">Modifier</a>
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="6">Aucun partenaire trouvé</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}
