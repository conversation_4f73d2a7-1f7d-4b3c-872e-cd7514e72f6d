{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'challenge-form'}}) }}
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-trophy-line text-primary me-2"></i> Challenge Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="form-group">
                                {{ form_label(form.name, 'Challenge Name', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-primary-subtle text-primary">
                                        <i class="ri-award-line"></i>
                                    </span>
                                    {{ form_widget(form.name, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'placeholder': 'Enter challenge name',
                                            'required': 'required',
                                            'style': 'border-color: #dee2e6;'
                                        }
                                    }) }}
                                </div>
                                <div class="invalid-feedback">
                                    Please enter a challenge name
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.name) }}
                                </div>
                            </div>
                        </div>

                        <div class="col-12 mb-4">
                            <div class="form-group">
                                {{ form_label(form.description, 'Challenge Description', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                {{ form_widget(form.description, {
                                    'attr': {
                                        'class': 'form-control form-field rounded-3',
                                        'rows': 4,
                                        'placeholder': 'Describe the challenge...',
                                        'required': 'required',
                                        'style': 'border-color: #dee2e6; resize: vertical;'
                                    }
                                }) }}
                                <div class="invalid-feedback">
                                    Please enter a challenge description
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.description) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> Provide a clear description of what this challenge is about
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="form-group">
                                {{ form_label(form.start, 'Start Date', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-info-subtle text-info">
                                        <i class="ri-calendar-event-line"></i>
                                    </span>
                                    {{ form_widget(form.start, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'style': 'border-color: #dee2e6;'
                                        }
                                    }) }}
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.start) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> When the challenge will become available
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="form-group">
                                {{ form_label(form.end, 'End Date', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-success-subtle text-success">
                                        <i class="ri-calendar-check-line"></i>
                                    </span>
                                    {{ form_widget(form.end, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'style': 'border-color: #dee2e6;'
                                        }
                                    }) }}
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.end) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> When the challenge will no longer be available
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <div class="form-group">
                                {{ form_label(form.duration, 'Duration (minutes)', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-warning-subtle text-warning">
                                        <i class="ri-time-line"></i>
                                    </span>
                                    {{ form_widget(form.duration, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'min': 1,
                                            'required': 'required',
                                            'style': 'border-color: #dee2e6;'
                                        }
                                    }) }}
                                </div>
                                <div class="invalid-feedback">
                                    Please enter a valid duration (minimum 1 minute)
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.duration) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> How long participants have to complete the challenge
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ path('app_challenge_index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i> Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i> {{ button_label|default('Save Challenge') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.1s; border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-image-line text-primary me-2"></i> Challenge Image
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {% if form.vars.data and form.vars.data.image %}
                            <div class="text-center mb-4">
                                <div class="challenge-image-container">
                                    <img src="http://localhost/{{ form.vars.data.image }}"
                                         alt="Current challenge image"
                                         class="img-fluid rounded"
                                         style="max-height: 200px;">
                                </div>
                                <p class="text-muted mt-3">Current image</p>
                            </div>
                        {% else %}
                            <div class="text-center mb-4">
                                <div style="background-color: #f8f9fa; border-radius: 0.75rem; padding: 2rem; text-align: center;">
                                    <i class="ri-image-add-line" style="font-size: 48px; color: #adb5bd;"></i>
                                    <p class="text-muted mt-3">No image uploaded yet</p>
                                </div>
                            </div>
                        {% endif %}

                        <div class="form-group">
                            {{ form_label(form.image, 'Upload Image', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                            {{ form_widget(form.image, {
                                'attr': {
                                    'class': 'form-control form-field',
                                    'style': 'border-color: #dee2e6;'
                                }
                            }) }}
                            <div class="form-text text-muted mt-2">
                                <i class="ri-information-line me-1"></i> Allowed formats: JPEG, PNG, WEBP (max 2MB)
                            </div>
                            <div class="text-danger">
                                {{ form_errors(form.image) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s; border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-information-line text-primary me-2"></i> Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0 ps-3">
                        <li class="mb-2">Set clear start and end dates for your challenge</li>
                        <li class="mb-2">Choose an appropriate duration based on the number of questions</li>
                        <li class="mb-2">Upload an engaging image to attract participants</li>
                        <li>After creating the challenge, add questions from the challenge details page</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
{{ form_end(form) }}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formFields = document.querySelectorAll('.form-field');

        formFields.forEach(field => {
            // Validate on input (while typing)
            field.addEventListener('input', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });

        // Form submission validation
        document.getElementById('challenge-form').addEventListener('submit', function(event) {
            let isValid = true;
            formFields.forEach(field => {
                if (!field.checkValidity()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                }
            });

            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();
            }
        });
    });
</script>
