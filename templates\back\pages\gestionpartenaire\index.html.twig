l{% extends 'back/base.html.twig' %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0 text-gray-800">User Management</h1>
        </div>
    </div>

    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center" style="width: 60px">ID</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th style="width: 120px">Role</th>
                            <th style="width: 180px">Created At</th>
                            <th style="width: 120px">Status</th>
                            <th class="text-end" style="width: 100px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            {% if user and user.id %}
                                <tr>
                                    <td class="text-center">{{ user.id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm me-2 bg-primary-subtle rounded-circle">
                                                {{ user.fullName|slice(0,1)|upper }}
                                            </div>
                                            {{ user.fullName }}
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge {% if user.role == 'ROLE_ADMIN' %}bg-danger{% else %}bg-primary{% endif %} rounded-pill">
                                            {{ user.role|replace({'ROLE_': ''}) }}
                                        </span>
                                    </td>
                                    <td>{{ user.createdAt|date('M d, Y H:i') }}</td>
                                    <td>
                                        <span class="badge {% if user.banned %}bg-danger{% else %}bg-success{% endif %} rounded-pill">
                                            {{ user.banned ? 'Banned' : 'Active' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-end gap-2">
                                            {% if app.user and user.id != app.user.id %}
                                                <a href="{{ path('back_admin_users_edit', {'id': user.id}) }}" 
                                                   class="btn btn-sm btn-primary" 
                                                   title="Edit">
                                                    <i class="ri-pencil-line"></i>
                                                </a>
                                                <form action="{{ path('back_admin_users_toggle_ban', {'id': user.id}) }}" method="POST" class="d-inline">
                                                    <input type="hidden" name="_token" value="{{ csrf_token('toggle_ban' ~ user.id) }}">
                                                    <button type="submit" 
                                                            class="btn btn-sm {{ user.banned ? 'btn-success' : 'btn-danger' }}" 
                                                            title="{{ user.banned ? 'Unban' : 'Ban' }}">
                                                        <i class="ri-{{ user.banned ? 'user-follow' : 'user-forbid' }}-line"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-4">No users found</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.avatar {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}
.table > :not(caption) > * > * {
    padding: 1rem 0.75rem;
}
.card {
    border: none;
    margin-bottom: 24px;
    box-shadow: 0 0 0.875rem 0 rgba(33,37,41,.05);
}
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.02);
}
.modal-backdrop {
    opacity: 0.5;
    background-color: #000;
}
.modal {
    background-color: rgba(0, 0, 0, 0.5);
}
.modal.show {
    display: block;
}
.modal-dialog {
    margin: 1.75rem auto;
    max-width: 500px;
}
.modal-content {
    position: relative;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0.3rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
}
/* Prevent page scrolling when modal is open */
body.modal-open {
    overflow: hidden;
}
</style>
{% endblock %} 