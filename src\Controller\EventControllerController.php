<?php

namespace App\Controller;

use App\Entity\Event;
use App\Entity\EventRegistration;
use App\Form\EventType;
use App\Repository\EventRepository;
use App\Service\CalendarService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/event')]
class EventControllerController extends AbstractController
{
    #[Route('/', name: 'app_event_list', methods: ['GET'])]
    public function index(EventRepository $eventRepository): Response
    {
        return $this->render('front/pages/events/index.html.twig', [
            'events' => $eventRepository->findAll(),
        ]);
    }

    /*#[Route('/register/{id}', name: 'app_event_register', methods: ['POST'])]
    public function register(
        Request $request,
        Event $event,
        EntityManagerInterface $entityManager,
        CalendarService $calendarService
    ): JsonResponse {
        try {
            $content = $request->getContent();
            $data = json_decode($content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON data received: ' . json_last_error_msg());
            }

            $email = $data['email'] ?? null;
            if (!$email) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Email is required'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Add event to user's calendar
            $calendarResponse = $calendarService->addEventToCalendar($event, $email);
            
            if (!$calendarResponse['success']) {
                throw new \Exception($calendarResponse['error'] ?? 'Failed to add event to calendar');
            }

            // Create event registration
            $registration = new EventRegistration();
            $registration->setEvent($event);
            $registration->setEmail($email);
            $registration->setRegistrationDate(new \DateTime());
            
            $entityManager->persist($registration);
            $entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Registration successful! The event has been added to your calendar.',
                'calendarDetails' => $calendarResponse['data'] ?? null
            ]);
        } catch (\Exception $e) {
            // Log the error
            error_log('Event registration error: ' . $e->getMessage());
            error_log('Request content: ' . $request->getContent());
            
            return new JsonResponse([
                'success' => false,
                'error' => 'Registration failed: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }*/
}
