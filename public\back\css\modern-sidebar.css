/* Modern Sidebar Styles */
:root {
    --eco-primary: #6BB748;
    --eco-primary-dark: #5a9a3d;
    --eco-primary-light: #8cc96e;
    --eco-secondary: #4a6741;
    --eco-text-light: #ffffff;
    --eco-text-dark: #333333;
    --eco-bg-light: #f8faf8;
    --eco-sidebar-width: 260px;
    --eco-sidebar-collapsed-width: 70px;
    --eco-transition-speed: 0.3s;
    --eco-border-radius: 12px;
    --eco-box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

/* Sidebar Container */
.sidebar {
    min-width: var(--eco-sidebar-width);
    max-width: var(--eco-sidebar-width);
    background: #ffffff;
    color: var(--eco-text-dark);
    transition: all var(--eco-transition-speed);
    min-height: 100vh;
    box-shadow: var(--eco-box-shadow);
    position: relative;
    z-index: 100;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--eco-primary) transparent;
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: var(--eco-primary);
    border-radius: 20px;
}

/* Sidebar Header */
.sidebar .sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--eco-primary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.sidebar .sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 20%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 20%);
    opacity: 0.6;
}

.sidebar .sidebar-header h3 {
    color: #fff;
    font-size: 1.5rem;
    margin: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.sidebar .sidebar-header h3 i {
    font-size: 1.8rem;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

/* Sidebar Components */
.sidebar ul.components {
    padding: 0;
    margin: 0 10px;
}

/* Sidebar Menu Items */
.sidebar ul li {
    margin-bottom: 5px;
    border-radius: var(--eco-border-radius);
    overflow: hidden;
    position: relative;
}

.sidebar ul li a.menu-item {
    padding: 12px 15px;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--eco-text-dark);
    text-decoration: none;
    transition: all var(--eco-transition-speed);
    border-radius: var(--eco-border-radius);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.sidebar ul li a.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--eco-primary);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    transform-origin: bottom;
}

.sidebar ul li a.menu-item i {
    font-size: 1.25rem;
    min-width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--eco-primary);
    transition: all var(--eco-transition-speed);
    position: relative;
    z-index: 1;
}

.sidebar ul li a.menu-item span {
    position: relative;
    z-index: 1;
}

/* Hover Effect */
.sidebar ul li a.menu-item:hover {
    background: rgba(107, 183, 72, 0.1);
    color: var(--eco-primary);
}

.sidebar ul li a.menu-item:hover::before {
    transform: scaleY(1);
}

.sidebar ul li a.menu-item:hover i {
    transform: translateX(3px);
}

/* Active State */
.sidebar ul li.active > a.menu-item {
    background: var(--eco-primary);
    color: white;
    box-shadow: 0 4px 10px rgba(107, 183, 72, 0.3);
}

.sidebar ul li.active > a.menu-item::before {
    transform: scaleY(1);
    background: white;
}

.sidebar ul li.active > a.menu-item i {
    color: white;
}

/* Dropdown Toggle */
.sidebar ul li a.dropdown-toggle {
    position: relative;
}

.sidebar ul li a.dropdown-toggle::after {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform var(--eco-transition-speed);
}

.sidebar ul li a.dropdown-toggle[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Submenu */
.sidebar ul.collapse {
    margin-left: 36px;
    margin-top: 5px;
    margin-bottom: 5px;
    border-left: 2px solid rgba(107, 183, 72, 0.2);
    padding-left: 5px;
    transition: all 0.3s ease;
}

.sidebar ul.collapse.show {
    animation: fadeInSubmenu 0.3s ease forwards;
}

@keyframes fadeInSubmenu {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sidebar ul.collapse li {
    margin-bottom: 2px;
}

.sidebar ul.collapse li a.submenu-item {
    padding: 8px 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--eco-text-dark);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: var(--eco-border-radius);
    position: relative;
}

.sidebar ul.collapse li a.submenu-item i {
    font-size: 1rem;
    color: var(--eco-primary);
    opacity: 0.8;
    transition: all 0.2s ease;
}

.sidebar ul.collapse li a.submenu-item:hover {
    background: rgba(107, 183, 72, 0.08);
    color: var(--eco-primary);
}

.sidebar ul.collapse li a.submenu-item:hover i {
    transform: translateX(2px);
    opacity: 1;
}

/* Collapsed Sidebar */
.sidebar.active {
    margin-left: calc(-1 * var(--eco-sidebar-width));
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        margin-left: calc(-1 * var(--eco-sidebar-width));
    }

    .sidebar.active {
        margin-left: 0;
        box-shadow: 0 0 50px rgba(0, 0, 0, 0.2);
    }
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 15px 20px;
    margin-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.8rem;
    color: var(--eco-text-dark);
    opacity: 0.7;
    text-align: center;
}
