{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'forum-form'}}) }}
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12 mb-3">
                    {{ form_label(form.title, 'Title', {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.title, {
                        'attr': {
                            'class': 'form-control form-field',
                            'placeholder': 'Enter forum title',
                            'minlength': '3',
                            'maxlength': '255',
                            'required': 'required',
                            'pattern': '^[a-zA-Z0-9\\s\\-_\\.]+$'
                        }
                    }) }}
                    <div class="text-danger">
                        {# Please enter a valid title (3-255 characters, alphanumeric, spaces, and -_. allowed) #}
                        {{ form_errors(form.title) }}
                    </div>
                    <small class="form-text text-muted">Choose a clear, descriptive title (3-255 characters, alphanumeric)</small>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-3">
                    {{ form_label(form.content, 'Content', {'label_attr': {'class': 'form-label'}}) }}
                    <textarea name="{{ field_name(form.content) }}" class="form-control form-field" rows="6" placeholder="Enter forum content" minlength="10" required style="resize: vertical;">{{ form.content.vars.value }}</textarea>
                    <div class="text-danger">
                        {# Please enter content with at least 10 characters #}
                        {{ form_errors(form.content) }}
                    </div>
                    <small class="form-text text-muted">Describe your forum topic in detail (minimum 10 characters)</small>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    {{ form_label(form.createdat, 'Created At', {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.createdat, {
                        'attr': {
                            'class': 'form-control form-field',
                            'required': 'required'
                        }
                    }) }}
                    <div class="invalid-feedback">
                        Please select a valid date and time
                    </div>
                    <small class="form-text text-muted">Select the date and time</small>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <a href="{{ path('app_forums_index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a> 
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {{ button_label|default('Save Forum') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
{{ form_end(form) }}

{# {% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formFields = document.querySelectorAll('.form-field');
    
    formFields.forEach(field => {
        // Validate on input (while typing)
        field.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Form submission validation
    document.getElementById('forum-form').addEventListener('submit', function(event) {
        let isValid = true;
        formFields.forEach(field => {
            if (!field.checkValidity()) {
                isValid = false;
                field.classList.add('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});
</script>
{% endblock %} #}
