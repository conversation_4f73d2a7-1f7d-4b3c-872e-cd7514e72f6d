{% extends 'back/pages/home/<USER>' %}

{% block content %}
<div class="container-fluid px-4">
  <!-- Page Header -->
  <div class="row align-items-center mb-4 animate__animated animate__fadeIn">
    <div class="col-auto">
      <a href="{{ path('app_command_index') }}" class="btn btn-outline-secondary rounded-circle btn-icon me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Back to Orders">
        <i class="ri-arrow-left-line"></i>
      </a>
    </div>
    <div class="col">
      <h1 class="h3 mb-0 text-gray-800">Create New Order</h1>
      <p class="text-muted mb-0">Add a new order to the system</p>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      {{ include('command/_form.html.twig', {'button_label': 'Create Order'}) }}
    </div>

    <div class="col-lg-4">
      <div class="card border-0 shadow-sm mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="card-header bg-white py-3">
          <h5 class="mb-0 fw-bold">Order Information</h5>
        </div>
        <div class="card-body">
          <p class="text-muted mb-0">Fill in the form to create a new order. All fields marked with an asterisk (*) are required.</p>
          <hr>
          <h6 class="fw-bold mb-3">Tips:</h6>
          <ul class="text-muted mb-0">
            <li>Make sure to select the correct customer ID</li>
            <li>The status will be set to "pending" by default</li>
            <li>You can add products to the order after creation</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
{% endblock %}
