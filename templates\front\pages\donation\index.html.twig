{% extends 'front/base.html.twig' %}

{% block title %}Make a Donation{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .donation-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .donation-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }
        
        .donation-card:hover {
            transform: translateY(-5px);
        }
        
        .donation-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .donation-header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .donation-header p {
            color: #6c757d;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .donation-type {
            text-align: center;
            padding: 20px;
        }
        
        .donation-type i {
            font-size: 2.5rem;
            color: #4CAF50;
            margin-bottom: 15px;
        }
        
        .donation-type h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .donation-type p {
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .btn-donate {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-donate:hover {
            background-color: #45a049;
            transform: translateY(-2px);
            color: white;
        }
        
        .donation-stats {
            text-align: center;
            padding: 40px 0;
            background: white;
            margin-top: 60px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-item h4 {
            color: #4CAF50;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .stat-item p {
            color: #6c757d;
            margin: 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .donation-header h1 {
                font-size: 2rem;
            }
            
            .donation-type {
                margin-bottom: 30px;
            }
            
            .stat-item {
                margin-bottom: 20px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class="donation-section">
        <div class="container">
            <div class="donation-header">
                <h1>Make a Difference Today</h1>
                <p>Your donation can help us continue our mission of making a positive impact in our community. Choose how you'd like to contribute below.</p>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="donation-card">
                        <div class="donation-type">
                            <i class="fas fa-dollar-sign"></i>
                            <h3>Monetary Donation</h3>
                            <p>Support our cause with a financial contribution. Every amount makes a difference.</p>
                            <a href="{{ path('app_front_donation_new', {'type': 'Monetary'}) }}" class="btn-donate">
                                <i class="fas fa-heart"></i> Donate Now
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="donation-card">
                        <div class="donation-type">
                            <i class="fas fa-box-open"></i>
                            <h3>Material Donation</h3>
                            <p>Donate goods, supplies, or equipment to support our initiatives.</p>
                            <a href="{{ path('app_front_donation_new', {'type': 'Material'}) }}" class="btn-donate">
                                <i class="fas fa-gift"></i> Donate Items
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="donation-card">
                        <div class="donation-type">
                            <i class="fas fa-hands-helping"></i>
                            <h3>Service Donation</h3>
                            <p>Contribute your time and expertise to help our cause.</p>
                            <a href="{{ path('app_front_donation_new', {'type': 'Service'}) }}" class="btn-donate">
                                <i class="fas fa-handshake"></i> Offer Service
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="donation-stats">
                <div class="row">
                    <div class="col-md-4">
                        <div class="stat-item">
                            <h4>{{ total_donations|default(0) }}</h4>
                            <p>Total Donations</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <h4>{{ total_amount|default(0) }} TND</h4>
                            <p>Amount Raised</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-item">
                            <h4>{{ donors_count|default(0) }}</h4>
                            <p>Generous Donors</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
