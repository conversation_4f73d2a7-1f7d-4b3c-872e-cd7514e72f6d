{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'progress-form'}}) }}
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-bar-chart-line text-primary me-2"></i> Progress Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                {{ form_label(form.user, 'User', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-primary-subtle text-primary">
                                        <i class="ri-user-line"></i>
                                    </span>
                                    {{ form_widget(form.user, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'required': 'required',
                                            'style': 'border-color: #dee2e6;'
                                        }
                                    }) }}
                                </div>
                                <div class="invalid-feedback">
                                    Please select a user
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.user) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> Select the user for this progress entry
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                {{ form_label(form.challenge, 'Challenge', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-success-subtle text-success">
                                        <i class="ri-trophy-line"></i>
                                    </span>
                                    {{ form_widget(form.challenge, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'required': 'required',
                                            'style': 'border-color: #dee2e6;'
                                        }
                                    }) }}
                                </div>
                                <div class="invalid-feedback">
                                    Please select a challenge
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.challenge) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> Select the challenge for this progress entry
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                {{ form_label(form.score, 'Score', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-warning-subtle text-warning">
                                        <i class="ri-award-line"></i>
                                    </span>
                                    {{ form_widget(form.score, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'required': 'required',
                                            'style': 'border-color: #dee2e6;',
                                            'min': '0'
                                        }
                                    }) }}
                                </div>
                                <div class="invalid-feedback">
                                    Please enter a valid score
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.score) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> Enter the user's score for this challenge
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                {{ form_label(form.progressnb, 'Progress Number', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                                <div class="input-group">
                                    <span class="input-group-text bg-info-subtle text-info">
                                        <i class="ri-question-answer-line"></i>
                                    </span>
                                    {{ form_widget(form.progressnb, {
                                        'attr': {
                                            'class': 'form-control form-field rounded-end',
                                            'required': 'required',
                                            'style': 'border-color: #dee2e6;',
                                            'min': '0'
                                        }
                                    }) }}
                                </div>
                                <div class="invalid-feedback">
                                    Please enter a valid progress number
                                </div>
                                <div class="text-danger">
                                    {{ form_errors(form.progressnb) }}
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="ri-information-line me-1"></i> Enter the number of questions completed
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-4">
                        {{ form_label(form.lastUpdated, 'Last Updated', {'label_attr': {'class': 'form-label fw-semibold'}}) }}
                        <div class="input-group">
                            <span class="input-group-text bg-primary-subtle text-primary">
                                <i class="ri-time-line"></i>
                            </span>
                            {{ form_widget(form.lastUpdated, {
                                'attr': {
                                    'class': 'form-control form-field rounded-end',
                                    'style': 'border-color: #dee2e6;'
                                }
                            }) }}
                        </div>
                        <div class="text-danger">
                            {{ form_errors(form.lastUpdated) }}
                        </div>
                        <small class="form-text text-muted mt-2">
                            <i class="ri-information-line me-1"></i> Select the last update date and time
                        </small>
                    </div>
                </div>
                <div class="card-footer bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ path('app_progress_index') }}" class="btn btn-outline-secondary">
                            <i class="ri-arrow-left-line me-1"></i> Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-save-line me-1"></i> {{ button_label|default('Save Progress') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow-sm border-0 mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.1s; border-radius: 0.75rem;">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 fw-bold d-flex align-items-center">
                        <i class="ri-information-line text-primary me-2"></i> Progress Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="ri-information-line" style="font-size: 24px;"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-1">About Progress Tracking</h6>
                                <p class="mb-0 small">Progress entries track how users are performing on challenges. The score represents points earned, while progress number tracks completed questions.</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6 class="fw-bold mb-3">Tips for Progress Management</h6>
                        <ul class="mb-0 ps-3">
                            <li class="mb-2">Score is typically calculated as 10 points per correct answer</li>
                            <li class="mb-2">Progress number represents the number of questions completed</li>
                            <li class="mb-2">Last updated field tracks when the progress was last modified</li>
                            <li>Make sure the progress number doesn't exceed the total questions in the challenge</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
{{ form_end(form) }}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const formFields = document.querySelectorAll('.form-field');

    formFields.forEach(field => {
        // Validate on input (while typing)
        field.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Form submission validation
    document.getElementById('progress-form').addEventListener('submit', function(event) {
        let isValid = true;
        formFields.forEach(field => {
            if (!field.checkValidity()) {
                isValid = false;
                field.classList.add('is-invalid');
            }
        });

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});
</script>
