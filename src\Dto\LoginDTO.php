<?php


namespace App\Dto;

use Symfony\Component\Validator\Constraints as Assert;

class LoginDTO
{
    #[Assert\NotBlank(message: 'Email cannot be blank.', groups: ['Default', 'user_login'])]
    #[Assert\Email(
        message: 'Invalid email format (e.g., missing @ or invalid domain).',
        mode: Assert\Email::VALIDATION_MODE_STRICT,
        groups: ['Default', 'user_login']
    )]
    public string $email;

    #[Assert\NotBlank(message: 'Password cannot be blank.', groups: ['Default', 'user_login'])]
    #[Assert\Length(
        min: 8,
        minMessage: 'Your password must be at least {{ limit }} characters long',
        groups: ['Default', 'user_login']
    )]
    public string $password;

    public bool $remember_me = false;
}

