<?php

namespace App\Controller;

use App\Entity\Challenge;
use App\Entity\Progress;
use App\Entity\Quizz;
use App\Form\ChallengeType;
use App\Repository\ChallengeRepository;
use App\Repository\QuizzRepository;
use App\Repository\ProgressRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\String\Slugger\SluggerInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Imagine\Gd\Imagine;
use Imagine\Image\Box;
use Imagine\Image\Point;
use Knp\Component\Pager\PaginatorInterface;

#[Route('/challenge')]
#[IsGranted('ROLE_USER')]
final class ChallengeController extends AbstractController
{
    private function convertLetterToNumber(string $letter): int
    {
        return match (strtoupper($letter)) {
            'A' => 1,
            'B' => 2,
            'C' => 3,
            'D' => 4,
            default => 0,
        };
    }

    private function resetQuizProgress(
        Challenge $challenge,
        Request $request,
        ProgressRepository $progressRepository,
        EntityManagerInterface $entityManager
    ): Progress {
        // Reset session
        $session = $request->getSession();
        $session->remove('current_question_' . $challenge->getId());
        $session->set('current_question_' . $challenge->getId(), 0);

        // Get or create progress
        $progress = $progressRepository->findOneBy([
            'user' => $this->getUser(),
            'challenge' => $challenge
        ]);

        if (!$progress) {
            $progress = new Progress();
            $progress->setUser($this->getUser());
            $progress->setChallenge($challenge);
        }

        // Reset progress
        $progress->setProgressnb(0);
        $progress->setScore(0);
        $entityManager->persist($progress);
        $entityManager->flush();

        return $progress;
    }

    #[Route('/back',name: 'app_challenge_index', methods: ['GET'])]
    public function index(ChallengeRepository $challengeRepository): Response
    {
        return $this->render('back/pages/challenge/index.html.twig', [
            'challenges' => $challengeRepository->findAll(),
        ]);
    }

    #[Route('/back/new', name: 'app_challenge_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager, SluggerInterface $slugger): Response
    {
        $challenge = new Challenge();
        $form = $this->createForm(ChallengeType::class, $challenge);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $imageFile = $form->get('image')->getData();

            if ($imageFile) {
                $originalFilename = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $slugger->slug($originalFilename);
                $newFilename = $safeFilename . '-' . uniqid() . '.' . $imageFile->guessExtension();

                try {
                    // Move uploaded file to temporary location
                    $tempPath = sys_get_temp_dir() . '/' . $newFilename;
                    $imageFile->move(sys_get_temp_dir(), $newFilename);

                    // Initialize Imagine
                    $imagine = new Imagine();

                    // Load the source image
                    $image = $imagine->open($tempPath);

                    // Load the watermark
                    $watermark = $imagine->open($this->getParameter('kernel.project_dir') . '/public/assets/img/eco-net.png');

                    // Get image dimensions
                    $size = $image->getSize();
                    $wSize = $watermark->getSize();

                    // Calculate watermark size based on image dimensions
                    $maxWatermarkWidth = (int)($size->getWidth() * 0.2);
                    $maxWatermarkHeight = (int)($size->getHeight() * 0.2);

                    // Resize watermark proportionally within bounds
                    $watermarkRatio = $wSize->getWidth() / $wSize->getHeight();
                    if ($maxWatermarkWidth / $watermarkRatio > $maxWatermarkHeight) {
                        $newWidth = (int)($maxWatermarkHeight * $watermarkRatio);
                        $newHeight = $maxWatermarkHeight;
                    } else {
                        $newWidth = $maxWatermarkWidth;
                        $newHeight = (int)($maxWatermarkWidth / $watermarkRatio);
                    }

                    // Resize watermark and set opacity
                    $watermark->resize(new Box($newWidth, $newHeight));
                    $watermark->effects()->gamma(0.3);

                    // Calculate grid spacing based on resized watermark
                    $spacingX = (int)($newWidth * 1.5);
                    $spacingY = (int)($newHeight * 1.5);

                    // Calculate number of watermarks needed
                    $numX = ceil($size->getWidth() / $spacingX);
                    $numY = ceil($size->getHeight() / $spacingY);

                    // Apply watermark grid pattern with boundary checks
                    for ($y = 0; $y < $numY; $y++) {
                        for ($x = 0; $x < $numX; $x++) {
                            // Calculate position with offset for alternating rows
                            $posX = ($x * $spacingX) + ($y % 2 === 0 ? $spacingX/4 : 0);
                            $posY = $y * $spacingY;

                            // Ensure coordinates stay within image bounds
                            $posX = max(0, min($posX, $size->getWidth() - $newWidth));
                            $posY = max(0, min($posY, $size->getHeight() - $newHeight));

                            $image->paste($watermark, new Point($posX, $posY));
                        }
                    }

                    // Save the final image
                    $image->save($this->getParameter('challenges_directory') . '/' . $newFilename);

                    // Clean up temporary file
                    unlink($tempPath);

                    $challenge->setImage($newFilename);
                } catch (FileException $e) {
                    $this->addFlash('error', 'There was an error uploading your image');
                }
            }

            $entityManager->persist($challenge);
            $entityManager->flush();

            return $this->redirectToRoute('app_quizz_new', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('back/pages/challenge/new.html.twig', [
            'challenge' => $challenge,
            'form' => $form,
        ]);
    }

    #[Route('/back/{id}', name: 'app_challenge_show', methods: ['GET'])]
    public function show(Challenge $challenge): Response
    {
        return $this->render('back/pages/challenge/show.html.twig', [
            'challenge' => $challenge,
        ]);
    }

    #[Route('/back/{id}/questions', name: 'app_challenge_questions', methods: ['GET'])]
    public function questions(Challenge $challenge, QuizzRepository $quizzRepository): Response
    {
        $quizzes = $quizzRepository->findBy(['challenge' => $challenge]);

        return $this->render('back/pages/challenge/questions.html.twig', [
            'challenge' => $challenge,
            'quizzes' => $quizzes,
        ]);
    }

    #[Route('/{id}/take-quiz', name: 'app_challenge_take_quiz', methods: ['GET'])]
    public function takeQuiz(
        Challenge $challenge,
        Request $request,
        QuizzRepository $quizzRepository,
        ProgressRepository $progressRepository,
        EntityManagerInterface $entityManager
    ): Response {
        $user = $this->getUser();
        if (!$user) {
            throw $this->createAccessDeniedException('You must be logged in to take a quiz.');
        }

        // Get all quizzes for this challenge
        $quizzes = $quizzRepository->findBy(['challenge' => $challenge]);

        // If no questions, redirect to challenges list with a professional error message
        if (empty($quizzes)) {
            $this->addFlash(
                'error',
                sprintf(
                    'The challenge "%s" currently has no questions available. Please check back later when the challenge has been updated with questions.',
                    $challenge->getName()
                )
            );
            return $this->redirectToRoute('app_challenges_list');
        }

        // Get session
        $session = $request->getSession();

        // Get current question number
        $currentQuestionNumber = $session->get('current_question_' . $challenge->getId());

        // Get progress
        $progress = $progressRepository->findOneBy([
            'user' => $user,
            'challenge' => $challenge
        ]);

        // Reset quiz if:
        // 1. Explicitly requested via restart parameter
        // 2. No current question number (first time or session expired)
        // 3. No progress record exists
        // 4. Current question number is beyond the quiz length
        if ($request->query->has('restart')
            || $currentQuestionNumber === null
            || !$progress
            || $currentQuestionNumber >= count($quizzes)
        ) {
            $progress = $this->resetQuizProgress($challenge, $request, $progressRepository, $entityManager);
            $currentQuestionNumber = 0;
        }

        // Get current quiz
        $currentQuiz = $quizzes[$currentQuestionNumber];

        return $this->render('front/pages/challenge/take_quiz.html.twig', [
            'challenge' => $challenge,
            'quiz' => $currentQuiz,
            'questionNumber' => $currentQuestionNumber + 1,
            'totalQuestions' => count($quizzes),
            'progress' => $progress
        ]);
    }

    #[Route('/{id}/submit-answer', name: 'app_challenge_submit_answer', methods: ['POST'])]
    public function submitAnswer(
        Challenge $challenge,
        Request $request,
        ProgressRepository $progressRepository,
        QuizzRepository $quizzRepository,
        EntityManagerInterface $entityManager
    ): Response {
        $user = $this->getUser();
        if (!$user) {
            throw $this->createAccessDeniedException('You must be logged in to submit answers.');
        }

        $answer = $request->request->get('answer');
        $quizId = $request->request->get('quiz_id');

        // Get progress
        $progress = $progressRepository->findOneBy([
            'user' => $user,
            'challenge' => $challenge
        ]);

        if (!$progress) {
            // If somehow the progress doesn't exist, create it
            $progress = new Progress();
            $progress->setUser($user);
            $progress->setChallenge($challenge);
            $progress->setProgressnb(0);
            $progress->setScore(0);
            $entityManager->persist($progress);
        }

        // Update progress
        $progress->setProgressnb($progress->getProgressnb() + 1);

        // Check if answer is correct and update score
        $quiz = $quizzRepository->find($quizId);
        if ($quiz && $this->convertLetterToNumber($answer) === $quiz->getAnswer()) {
            $progress->setScore($progress->getScore() + 10); // Award 10 points for correct answer
        }

        $entityManager->flush();

        // Get all quizzes to check if we're done
        $quizzes = $quizzRepository->findBy(['challenge' => $challenge]);

        // Increment question counter in session
        $session = $request->getSession();
        $currentQuestion = $session->get('current_question_' . $challenge->getId(), 0);
        $nextQuestion = $currentQuestion + 1;

        // If all questions are answered, redirect to results
        if ($nextQuestion >= count($quizzes)) {
            return $this->redirectToRoute('app_challenge_results', ['id' => $challenge->getId()]);
        }

        // Otherwise, move to next question
        $session->set('current_question_' . $challenge->getId(), $nextQuestion);
        return $this->redirectToRoute('app_challenge_take_quiz', ['id' => $challenge->getId()]);
    }

    #[Route('/{id}/results', name: 'app_challenge_results', methods: ['GET'])]
    public function results(
        Challenge $challenge,
        ProgressRepository $progressRepository
    ): Response {
        $user = $this->getUser();
        if (!$user) {
            throw $this->createAccessDeniedException('You must be logged in to view results.');
        }

        $progress = $progressRepository->findOneBy([
            'user' => $user,
            'challenge' => $challenge
        ]);

        if (!$progress) {
            throw $this->createNotFoundException('Progress not found');
        }

        return $this->render('front/pages/challenge/results.html.twig', [
            'challenge' => $challenge,
            'progress' => $progress
        ]);
    }

    #[Route('/challenges', name: 'app_challenges_list', methods: ['GET'])]
    public function listChallenges(
        ChallengeRepository $challengeRepository,
        ProgressRepository $progressRepository,
        QuizzRepository $quizzRepository,
        Request $request,
        PaginatorInterface $paginator
    ): Response {
        $user = $this->getUser();
        $filter = $request->query->get('filter', 'all');
        $validFilters = ['all', 'done', 'in-progress', 'todo'];

        // Validate filter parameter
        if (!in_array($filter, $validFilters)) {
            $filter = 'all';
        }

        // Get all challenges to determine their status
        $allChallenges = $challengeRepository->findAll();
        $challengeStatuses = [];
        $filteredChallengeIds = [];

        // Determine status for each challenge
        foreach ($allChallenges as $challenge) {
            $progress = $progressRepository->findOneBy([
                'user' => $user,
                'challenge' => $challenge
            ]);

            $totalQuestions = count($challenge->getQuizzs());

            // Determine challenge status
            $status = 'todo'; // Default status

            if ($progress) {
                if ($progress->getProgressnb() >= $totalQuestions) {
                    $status = 'done';
                } elseif ($progress->getProgressnb() > 0) {
                    $status = 'in-progress';
                }
            }

            $challengeStatuses[$challenge->getId()] = $status;

            // Filter challenges based on selected filter
            if ($filter === 'all' || $filter === $status) {
                $filteredChallengeIds[] = $challenge->getId();
            }
        }

        // Create query builder with filter
        $queryBuilder = $challengeRepository->createQueryBuilder('c')
            ->orderBy('c.id', 'DESC');

        // Apply filter if not 'all'
        if (!empty($filteredChallengeIds)) {
            $queryBuilder->where('c.id IN (:ids)')
                ->setParameter('ids', $filteredChallengeIds);
        } else {
            // If no challenges match the filter, return empty result
            $queryBuilder->where('c.id = 0');
        }

        // Paginate the filtered results
        $pagination = $paginator->paginate(
            $queryBuilder,
            $request->query->getInt('page', 1), // Get current page number, default to 1
            6, // Items per page
            ['pageParameterName' => 'page']
        );

        // Prepare challenge progress data for the view
        $challengeProgress = [];
        foreach ($pagination as $challenge) {
            $progress = $progressRepository->findOneBy([
                'user' => $user,
                'challenge' => $challenge
            ]);

            $totalQuestions = count($challenge->getQuizzs());

            $challengeProgress[$challenge->getId()] = [
                'progress' => $progress,
                'totalQuestions' => $totalQuestions
            ];
        }

        return $this->render('front/pages/managements/challenges.html.twig', [
            'pagination' => $pagination,
            'challengeProgress' => $challengeProgress,
            'challengeStatuses' => $challengeStatuses,
            'currentFilter' => $filter,
        ]);
    }


    #[Route('/back/{id}/edit', name: 'app_challenge_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Challenge $challenge, EntityManagerInterface $entityManager, SluggerInterface $slugger): Response
    {
        $form = $this->createForm(ChallengeType::class, $challenge, [
            'is_edit' => true,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $imageFile = $form->get('image')->getData();

            if ($imageFile) {
                $originalFilename = pathinfo($imageFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $slugger->slug($originalFilename);
                $newFilename = $safeFilename . '-' . uniqid() . '.' . $imageFile->guessExtension();

                try {
                    // Delete old image if it exists
                    $oldImage = $challenge->getImage();
                    if ($oldImage) {
                        $oldImagePath = $this->getParameter('challenges_directory') . '/' . $oldImage;
                        if (file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }

                    // Move uploaded file to temporary location
                    $tempPath = sys_get_temp_dir() . '/' . $newFilename;
                    $imageFile->move(sys_get_temp_dir(), $newFilename);

                    // Initialize Imagine
                    $imagine = new Imagine();

                    // Load the source image
                    $image = $imagine->open($tempPath);

                    // Load the watermark
                    $watermark = $imagine->open($this->getParameter('kernel.project_dir') . '/public/assets/img/eco-net.png');

                    // Get image dimensions
                    $size = $image->getSize();
                    $wSize = $watermark->getSize();

                    // Calculate watermark size based on image dimensions
                    $maxWatermarkWidth = (int)($size->getWidth() * 0.2);
                    $maxWatermarkHeight = (int)($size->getHeight() * 0.2);

                    // Resize watermark proportionally within bounds
                    $watermarkRatio = $wSize->getWidth() / $wSize->getHeight();
                    if ($maxWatermarkWidth / $watermarkRatio > $maxWatermarkHeight) {
                        $newWidth = (int)($maxWatermarkHeight * $watermarkRatio);
                        $newHeight = $maxWatermarkHeight;
                    } else {
                        $newWidth = $maxWatermarkWidth;
                        $newHeight = (int)($maxWatermarkWidth / $watermarkRatio);
                    }

                    // Resize watermark and set opacity
                    $watermark->resize(new Box($newWidth, $newHeight));
                    $watermark->effects()->gamma(0.3);

                    // Calculate grid spacing based on resized watermark
                    $spacingX = (int)($newWidth * 1.5);
                    $spacingY = (int)($newHeight * 1.5);

                    // Calculate number of watermarks needed
                    $numX = ceil($size->getWidth() / $spacingX);
                    $numY = ceil($size->getHeight() / $spacingY);

                    // Apply watermark grid pattern with boundary checks
                    for ($y = 0; $y < $numY; $y++) {
                        for ($x = 0; $x < $numX; $x++) {
                            // Calculate position with offset for alternating rows
                            $posX = ($x * $spacingX) + ($y % 2 === 0 ? $spacingX/4 : 0);
                            $posY = $y * $spacingY;

                            // Ensure coordinates stay within image bounds
                            $posX = max(0, min($posX, $size->getWidth() - $newWidth));
                            $posY = max(0, min($posY, $size->getHeight() - $newHeight));

                            $image->paste($watermark, new Point($posX, $posY));
                        }
                    }

                    // Save the final image
                    $image->save($this->getParameter('challenges_directory') . '/' . $newFilename);

                    // Clean up temporary file
                    unlink($tempPath);

                    $challenge->setImage($newFilename);
                } catch (FileException $e) {
                    $this->addFlash('error', 'There was an error uploading your image');
                }
            }

            $entityManager->flush();

            return $this->redirectToRoute('app_challenge_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('back/pages/challenge/edit.html.twig', [
            'challenge' => $challenge,
            'form' => $form,
        ]);
    }

    #[Route('/back/delete/{id}', name: 'app_challenge_delete', methods: ['POST'])]
    public function delete(Request $request, Challenge $challenge, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$challenge->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($challenge);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_challenge_index', [], Response::HTTP_SEE_OTHER);
    }
}
