<?php

namespace App\Repository;

use App\Entity\EventRegistration;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EventRegistration>
 */
class EventRegistrationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EventRegistration::class);
    }

    /**
     * Find all event registrations with user details
     *
     * @return array Returns an array of EventRegistration objects with user details
     */
    public function findAllWithUserDetails(): array
    {
        $registrations = $this->findAll();
        return $this->addUserDetailsToRegistrations($registrations);
    }

    /**
     * Find event registrations by event with user details
     *
     * @param int $eventId The event ID
     * @return array Returns an array of EventRegistration objects with user details
     */
    public function findByEventWithUserDetails(int $eventId): array
    {
        $registrations = $this->findBy(['event' => $eventId]);
        return $this->addUserDetailsToRegistrations($registrations);
    }

    /**
     * Add user details to event registrations
     *
     * @param array $registrations Array of EventRegistration objects
     * @return array Returns the same array with user details added
     */
    private function addUserDetailsToRegistrations(array $registrations): array
    {
        $entityManager = $this->getEntityManager();
        $userRepository = $entityManager->getRepository(User::class);

        foreach ($registrations as $registration) {
            $userId = $registration->getUserId();
            if ($userId) {
                $user = $userRepository->find($userId);
                if ($user) {
                    // Add user details to the registration object as a property
                    $registration->userFullName = $user->getFullName() ?: 'Unknown User';
                } else {
                    $registration->userFullName = 'User Not Found';
                }
            } else {
                $registration->userFullName = 'Guest';
            }
        }

        return $registrations;
    }

    public function getRegistrationCountsByEvent(): array
    {
        return $this->createQueryBuilder('er')
            ->select('e.title as event_title, COUNT(er.id) as registration_count')
            ->leftJoin('er.event', 'e')
            ->groupBy('e.id')
            ->getQuery()
            ->getResult();
    }
}
