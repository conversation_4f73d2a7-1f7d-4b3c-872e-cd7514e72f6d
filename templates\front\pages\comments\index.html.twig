{% extends 'back/pages/home/<USER>' %}

{% block dash %} {% endblock %}
{% block forum %}{% endblock %}
{% block cmt %}active{% endblock %}

{% block content %}
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Comments Management</h1>
            <a href="{{ path('app_comments_new') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create New Comment
            </a>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Comments List</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="commentsTable">
                        <thead class="thead-light">
                            <tr>
                                <th>ID</th>
                                <th width="50%">Content</th>
                                <th>Upvotes</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for comment in comments %}
                            <tr>
                                <td>{{ comment.id }}</td>
                                <td>{{ comment.content|length > 150 ? comment.content|slice(0, 150) ~ '...' : comment.content }}</td>
                                <td>
                                    <span class="badge badge-success">
                                        <i class="fas fa-thumbs-up"></i> {{ comment.upvotes }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <a href="{{ path('app_comments_show', {'id': comment.id}) }}" class="btn btn-info btn-sm" title="View">
                                         <i class="ri-eye-line"></i>
                                    </a>
                                    <a href="{{ path('app_comments_edit', {'id': comment.id}) }}" class="btn btn-warning btn-sm" title="Edit">
                                        <i class="ri-edit-box-line"></i>
                                    </a>
                                    <form method="post" action="{{ path('app_comments_delete', {'id': comment.id}) }}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ comment.id) }}">
                                        <button class="btn btn-danger btn-sm" title="Delete">
                                            <i class="ri-close-line"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="4" class="text-center">No comments found</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $('#commentsTable').DataTable({
                "order": [[ 0, "desc" ]],
                "pageLength": 10,
                "language": {
                    "lengthMenu": "Show _MENU_ comments per page",
                    "zeroRecords": "No comments found",
                    "info": "Showing page _PAGE_ of _PAGES_",
                    "infoEmpty": "No comments available",
                    "infoFiltered": "(filtered from _MAX_ total comments)"
                }
            });
        });
    </script>
{% endblock %}
