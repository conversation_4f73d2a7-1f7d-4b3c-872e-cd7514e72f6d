<?php

namespace App\Service;

use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class SendEmailService
{
    public function sendEmail(string $fromEmail, string $emailType = 'reset_password', string $resetToken = ''): string
    {
        // Use the absolute path to the script
        $pythonScriptPath = realpath(__DIR__ . '/../python_scripts/send_email.py');

        if (!$pythonScriptPath) {
            return 'Python script not found! Path: ' . $pythonScriptPath;
        }

        // Debugging: Check the resolved path
        // echo 'Resolved Python script path: ' . $pythonScriptPath . PHP_EOL;

        // Ensure the Python executable is called correctly
        $process = new Process(['python', $pythonScriptPath, $fromEmail, $emailType, $resetToken]);

        try {
            $process->mustRun();
            return $process->getOutput();
        } catch (ProcessFailedException $exception) {
            return 'Error: ' . $exception->getMessage();
        }
    }
}
